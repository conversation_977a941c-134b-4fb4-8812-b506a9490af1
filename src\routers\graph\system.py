from fastapi import APIRouter, status

from ...settings import SETTINGS
from ...utils import router_response_handler


from ...database.graph.services.graph_data import (
    DataManager as GraphDataManager,
    SystemDataRequest as SystemGraphRequest,
    SystemDataResponse as SystemGraphResponse,
    SecretData as SecretGraph,
    AdjacentNodeRequest as AdjacentNodeRequest,
    AdjacentNodeResponse as AdjacentNodeResponse
)


from ...schemas.format import Response

router = APIRouter(tags=["Graph-System"])

# API DB Session
if SETTINGS.BASE.APP_API == True:
    default_api_call = True
else:
    default_api_call = False


@router.post("/system/graph/query", status_code=status.HTTP_200_OK, response_model=SystemGraphResponse)
def system_query_graph(request: SystemGraphRequest, api_call: bool = default_api_call) -> SystemGraphResponse:
    request = SystemGraphRequest(**request.__dict__)
    response_data, response = GraphDataManager(api_call=api_call, graph_storage=SETTINGS.GPDB.FORM, graph_location=SETTINGS.GPDB.LOCA, graph_config=SETTINGS.GPDB.CONFIG).query_data_by_system(request=request)
    router_response_handler(response=response, api_call=api_call)
    return response_data

@router.post("/system.graph/get/adjancent_nodes", status_code=status.HTTP_200_OK, response_model=AdjacentNodeResponse)
def system_query_adjancent_nodes(request: AdjacentNodeRequest, api_call: bool = default_api_call) -> AdjacentNodeResponse:
    request = AdjacentNodeRequest(**request.__dict__)
    response_data, response = GraphDataManager(api_call=api_call, graph_storage=SETTINGS.GPDB.FORM, graph_location=SETTINGS.GPDB.LOCA, graph_config=SETTINGS.GPDB.CONFIG).query_adjacent_nodes(request=request)
    router_response_handler(response=response, api_call=api_call)
    return response_data