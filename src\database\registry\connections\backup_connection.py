import os
import urllib.parse
from sqlalchemy import create_engine
from sqlalchemy.engine import Engine
from sqlalchemy.orm import sessionmaker
from sqlalchemy.exc import OperationalError
from contextlib import contextmanager

from ..schemas.database import BackupDatabaseConfiguration

from ....logger.log_handler import get_logger

logger = get_logger(__name__)

def init_bkup_db_engine(db_config: BackupDatabaseConfiguration) -> Engine:
    if db_config.format.upper() == "PGDB":
        # Access Database Settings
        DB_URL = '{user}:{pswd}@{host}:{port}/{name}'.format(
            host=urllib.parse.quote_plus(db_config.host),  # DB host
            port=urllib.parse.quote_plus(db_config.port),  # DB port
            name=urllib.parse.quote_plus(db_config.name),  # DB name
            user=urllib.parse.quote_plus(db_config.user),  # DB user
            pswd=urllib.parse.quote_plus(db_config.pswd)   # DB pswd
        )
        DATABASE_URL = f"postgresql://{DB_URL}" 

    elif db_config.format.upper() == "SLDB":
        if db_config.location.lower() == "local":
            # Check/Create Local Database Directory
            if not db_config.sdir:
                db_dir = db_config.rdir
            else:
                db_dir = os.path.join(db_config.rdir, db_config.sdir)

            if not os.path.exists(db_dir):
                try:
                    os.makedirs(db_dir)
                except:
                    err_msg = f"Export DB Creation Error : <{db_config.name}> Database"
                    logger.error(err_msg)
                    raise Exception(err_msg)
                
            # Access Database Settings
            DB_URL = os.path.join(db_config.rdir, db_config.sdir, db_config.name)

            DATABASE_URL = f"sqlite:///{DB_URL}??check_same_thread=False"

    else:
        err_msg = f"Unknown DB Error : <{db_config.format}> Database"
        logger.error(err_msg)
        raise Exception(err_msg)

    # Test DB Connection
    try:
        engine = create_engine(DATABASE_URL, pool_pre_ping=True)
        with engine.connect():
            logger.info(f"Connected : <{db_config.name}> Database")

    # Handle any operational errors that might occur
    except OperationalError as e:
        err_msg = f"Connection Error : <{db_config.name}> Database"
        logger.error(err_msg)
        raise Exception(err_msg)

    # Handle any other exceptions that might occur
    except:
        err_msg = f"Unexpected Error : <{db_config.name}> Database"
        logger.error(err_msg)
        raise Exception(err_msg)

    return engine

@contextmanager
def get_bkup_db_func(engine: Engine):
    SessionLocal = sessionmaker(
        autocommit=False, 
        autoflush=False, 
        bind=engine
    )
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

# def init_bkup_table(engine: Engine):
#     Base.metadata.create_all(engine)



