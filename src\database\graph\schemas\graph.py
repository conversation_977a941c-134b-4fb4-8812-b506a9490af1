from pydantic import BaseModel, Field 
import uuid
from datetime import datetime, timezone
from fastapi import UploadFile
from typing import List, Any, Dict, Optional, Literal

from ....settings import SETTINGS

""""
    Graph General Operation
"""
class EdgeCreate(BaseModel):
    edge_id:        str=Field(default_factory=lambda: str(uuid.uuid4()))
    edge_traceid:   str=Field(default_factory=lambda: str(uuid.uuid4()))
    edge_version:   int=1
    edge_status:    int=1

    updated_by:     str='system'

    source_node_id: str
    target_node_id: str
    edge_type:      str
    edge_cluster:   str='default'
    edge_category:  str='default'
    edge_name:      str=''
    edge_strength:  float=-1.0

    # Dependent
    knowledge_id:   str='' # Map to Original Knowledge

    edge_tags:      list[str]=[]

    created_at:     datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    updated_at:     datetime = Field(default_factory=lambda: datetime.now(timezone.utc))

class NodeCreate(BaseModel):
    # Trace Information
    node_id:        str=Field(default_factory=lambda: str(uuid.uuid4()))
    node_traceid:   str=Field(default_factory=lambda: str(uuid.uuid4()))
    node_version:   int=1
    node_status:    int=1
    
    # Control Information
    updated_by:     str='system'

    # Specification
    node_sequence:  int=-1
    node_type:      str='chunk'
    node_cluster:   str='default'
    node_category:  str='default'
    node_name:      str=''

    # Statistics
    node_indegree:  int=0
    node_outdegree: int=0

    # Chunkg Trace Information
    data_id:        str=''
    data_traceid:   str=''
    data_version:   int=1

    # Chunk Category Information
    data_type:      str='' # TEXT, IMAGE, TABLE, DOCUMENT, etc.
    content_type:   str=''

    # Chunk Tags
    data_languages: list[str]=[]
    data_keywords:  list[str]=[]

    # Dependent
    knowledge_id:   str='' # Map to Original Knowledge

    # Tags
    node_tags:      list[str]=[]

    # Time Information
    created_at:     datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    updated_at:     datetime = Field(default_factory=lambda: datetime.now(timezone.utc))

class GraphCreate(BaseModel):
    nodes: list[NodeCreate]=[]
    edges: list[EdgeCreate]=[]

class GraphCreateRequest(BaseModel):
    user_requestid:   str | None = None
    user_id:          str | None = ""
    user_name:        str | None = ""
    is_admin:         bool | None = False

    container_name:   str | None = ""
    partition_key:    str # knowledge_id

    data:             GraphCreate = GraphCreate()

class GraphBatchCreateRequest(BaseModel):
    create_requests: list[GraphCreateRequest]

# Node CRUD
class EdgeUpdate(BaseModel):
    edge_id:        str | None = None
    edge_traceid:   str | None = None
    edge_version:   int | None = None
    edge_status:    int | None = None

    updated_by:     str | None = None

    source_node_id: str | None = None
    target_node_id: str | None = None
    edge_type:      str | None = None
    edge_category:  str | None = None
    edge_name:      str | None = None
    edge_strength:  float | None = None

    # Dependent
    knowledge_id:   str | None = None

    edge_tags:      list | None = None

    created_at:     datetime | None = None
    updated_at:     datetime | None = None

class NodeUpdate(BaseModel):
    # Trace Information
    node_id:        str | None = None
    node_traceid:   str | None = None
    node_version:   int | None = None
    node_status:    int | None = None

    # Control Information
    updated_by:     str | None = None
    
    # Specification
    node_sequence:  int | None = None
    node_type:      str | None = None
    node_cluster:   str | None = None
    node_category:  str | None = None
    node_name:      str | None = None

    # Statistics
    node_indegree:  int | None = None
    node_outdegree: int | None = None

    # Chunkg Trace Information
    data_id:        str | None = None
    data_traceid:   str | None = None
    data_version:   int | None = None

    # Chunk Category Information
    data_type:      str | None = None # TEXT, IMAGE, TABLE, DOCUMENT, etc.
    content_type:   str | None = None

    # Chunk Tags
    data_languages: list[str] | None = None
    data_keywords:  list[str] | None = None

    # Dependent
    knowledge_id:   str | None = None # Map to Original Knowledge

    # Tags
    node_tags:      list[str] | None = None

    # Time Information
    created_at:     datetime | None = None
    updated_at:     datetime | None = None

class GraphUpdate(BaseModel):
    node_id: str | None = None
    edge_id: str | None = None
    node:    NodeUpdate | None = None
    edge:    EdgeUpdate | None = None

class GraphUpdateRequest(BaseModel):
    user_requestid: str | None = None
    user_id:        str=''
    user_name:      str=''
    is_admin:       bool=False

    container_name: str | None = None
    partition_key:  str # knowledge_id

    update_data:    list[GraphUpdate]=[]
    overwrite:      bool = True
    
class GraphRequest(BaseModel):
    user_requestid: str | None = None
    user_id:        str | None = None
    user_name:      str | None = None

    container_name: str | None = None
    partition_key:  str

    node_id:        str | None = None
    edge_id:        str | None = None

class GraphBatchRequest(BaseModel):
    graph_requests: list[GraphRequest]


# System-level Access
class SecretEdge(BaseModel):
    edge_id:        str | None = None
    edge_traceid:   str | None = None
    edge_version:   int | None = None
    edge_status:    int | None = None

    updated_by:     str | None = None

    source_node_id: str | None = None
    target_node_id: str | None = None
    edge_type:      str | None = None
    edge_category:  str | None = None
    edge_name:      str | None = None
    edge_strength:  float | None = None

    # Dependent
    knowledge_id:   str | None = None

    edge_tags:      List[str] | None = None

    created_at:     datetime | None = None
    updated_at:     datetime | None = None

class SecretNode(BaseModel):
    # Trace Information
    node_id:        str | None = None
    node_traceid:   str | None = None
    node_version:   int | None = None
    node_status:    int | None = None

    # Control Information
    updated_by:     str | None = None

    # Specification
    node_sequence:  int | None = None
    node_type:      str | None = None
    node_cluster:   str | None = None
    node_category:  str | None = None
    node_name:      str | None = None

    # Statistics
    node_indegree:  int | None = None
    node_outdegree: int | None = None

    # Chunkg Trace Information
    data_id:        str | None = None
    data_traceid:   str | None = None
    data_version:   int | None = None

    # Chunk Category Information
    data_type:      str | None = None # TEXT, IMAGE, TABLE, DOCUMENT, etc.
    content_type:   str | None = None

    # Chunk Tags
    data_languages: List[str] | None = None
    data_keywords:  List[str] | None = None

    # Dependent
    knowledge_id:   str | None = None # Map to Original Knowledge

    # Tags
    node_tags:      List[str] | None = None

    # Time Information
    created_at:     datetime | None = None
    updated_at:     datetime | None = None

class SecretGraph(BaseModel):
    nodes: list[SecretNode] | None = None
    edges: list[SecretEdge] | None = None


"""
    Graph Filter
"""
class EdgeStringFilter(BaseModel):
    edge_id_filter:        list[str] | None = None
    edge_traceid_filter:   list[str] | None = None

    updated_by_filter:     list[str] | None = None

    source_node_id_filter: list[str] | None = None
    target_node_id_filter: list[str] | None = None
    edge_type_filter:      list[str] | None = None
    edge_cluster_filter:   list[str] | None = None
    edge_category_filter:  list[str] | None = None
    edge_name_filter:      list[str] | None = None
    
    knowledge_id_filter:   list[str] | None = None

class EdgeNumericFilter(BaseModel):
    edge_version_min:  int | None = None
    edge_version_max:  int | None = None
    edge_status_min:   int | None = None
    edge_status_max:   int | None = None 

    edge_strength_min: float | None = None
    edge_strength_max: float | None = None

class EdgeListFilter(BaseModel):
    edge_tags_or:  list[str] | None = None
    edge_tags_and: list[str] | None = None

class EdgeDictionaryFilter(BaseModel):
    not_used_or:  list[str] | None = None
    not_used_and: list[str] | None = None

class EdgeBooleanFilter(BaseModel):
    not_used_filter: bool | None = None

class EdgeDatetimeFilter(BaseModel):
    created_at_start: datetime  | None = None
    created_at_end:   datetime  | None = None
    updated_at_start: datetime  | None = None
    updated_at_end:   datetime  | None = None

class EdgeByteFilter(BaseModel):
    not_used_filter: list[bytes] | None = None


class NodeStringFilter(BaseModel):
    node_id_filter:        list[str] | None = None
    node_traceid_filter:   list[str] | None = None

    updated_by_filter:     list[str] | None = None

    node_type_filter:      list[str] | None = None
    node_cluster_filter:   list[str] | None = None
    node_category_filter:  list[str] | None = None
    node_name_filter:      list[str] | None = None

    data_id_filter:        list[str] | None = None
    data_traceid_filter:   list[str] | None = None
    data_type_filter:      list[str] | None = None
    content_type_filter:   list[str] | None = None

    knowledge_id_filter:   list[str] | None = None

class NodeNumericFilter(BaseModel):
    node_sequence_min:  int | None = None
    node_sequence_max:  int | None = None
    node_version_min:   int | None = None
    node_version_max:   int | None = None
    node_status_min:    int | None = None
    node_status_max:    int | None = None

    node_indegree_min:  int | None = None
    node_indegree_max:  int | None = None

    node_outdegree_min: int | None = None
    node_outdegree_max: int | None = None

    data_version_min:   int | None = None
    data_version_max:   int | None = None

class NodeListFilter(BaseModel):
    data_languages_or:  list[str] | None = None
    data_languages_and: list[str] | None = None

    data_keywords_or:   list[str] | None = None
    data_keywords_and:  list[str] | None = None

    node_tags_or:       list[str] | None = None
    node_tags_and:      list[str] | None = None

class NodeDictionaryFilter(BaseModel):
    not_used_or:  list[str] | None = None
    not_used_and: list[str] | None = None

class NodeBooleanFilter(BaseModel):
    not_used_filter: bool | None = None

class NodeDatetimeFilter(BaseModel):
    created_at_start: datetime  | None = None
    created_at_end:   datetime  | None = None
    updated_at_start: datetime  | None = None
    updated_at_end:   datetime  | None = None

class NodeByteFilter(BaseModel):
    not_used_filter: list[bytes] | None = None

class EdgeFilter(BaseModel):
    string_filter:     EdgeStringFilter     | None = None
    numeric_filter:    EdgeNumericFilter    | None = None
    list_filter:       EdgeListFilter       | None = None
    dictionary_filter: EdgeDictionaryFilter | None = None
    boolean_filter:    EdgeBooleanFilter    | None = None
    datetime_filter:   EdgeDatetimeFilter   | None = None
    byte_filter:       EdgeByteFilter       | None = None
    sorting:           dict={"edge_id": "asc", "updated_at": "desc"}
    filter_no:         int=-1

class NodeFilter(BaseModel):
    string_filter:     NodeStringFilter     | None = None
    numeric_filter:    NodeNumericFilter    | None = None
    list_filter:       NodeListFilter       | None = None
    dictionary_filter: NodeDictionaryFilter | None = None
    boolean_filter:    NodeBooleanFilter    | None = None
    datetime_filter:   NodeDatetimeFilter   | None = None
    byte_filter:       NodeByteFilter       | None = None
    sorting:           dict={"node_id": "asc", "updated_at": "desc"}
    filter_no:         int=-1

class GraphFilter(BaseModel):
    partition_keys: list[str]=[] # knowledge_id
    node_filter:    NodeFilter | None = None
    edge_filter:    EdgeFilter | None = None

class FilterConditions(BaseModel):
    node_conditions: list=[]
    node_bindings:   dict=dict()
    node_sorting:    list=[]
    node_filter_no:  int=-1
    edge_conditions: list=[]
    edge_bindings:   dict=dict() 
    edge_sorting:    list=[]
    edge_filter_no:  int=-1

""" 
    Request and Resposne for System Access Graphs
"""
class SystemGraphRequest(BaseModel):
    graph_requestid: str=Field(default_factory=lambda: str(uuid.uuid4()))
    container_name:  str | None = None
    partition_key:   str # knowledge_id
    data_filter:     GraphFilter | None = None

class SystemGraphResponse(BaseModel):
    graph_requestid: str
    filtered_data:   SecretGraph = SecretGraph()
    node_count:      int=0
    edge_count:      int=0

"""
    Adjacent Node Filter
"""
class AdjacentNodeFilter(BaseModel):
    partition_key: str
    node_ids:      list[str]
    direction:     Literal['BOTH', 'IN', 'OUT']='BOTH'
    edge_types:    list[str]=[] # Relationship Filter
    filter_no:     int=-1

class AdjacentNode(BaseModel):
    node_id:        str
    
    # Specification
    node_type:      str='chunk'
    node_category:  str='default'

    # Statistics
    node_indegree:  int=0
    node_outdegree: int=0

    # Chunkg Trace Information
    data_id:        str=''

    # Chunk Category Information
    data_type:      str='' # TEXT, IMAGE, TABLE, DOCUMENT, etc.
    content_type:   str=''

    # Dependent
    knowledge_id:   str='' # Map to Original Knowledge

class AdjacentNodeRequest(BaseModel):
    graph_requestid: str=Field(default_factory=lambda: str(uuid.uuid4()))
    container_name:  str | None = None
    data_filter:     AdjacentNodeFilter | None = None

class AdjacentNodeResponse(BaseModel):
    graph_requestid: str
    filtered_data:   list[AdjacentNode]=[]
    node_count:      int=0


"""
    Data Backup / Restore Configuration
"""
class BackupConfig(BaseModel):
    format:   str | None = None
    location: str | None = None
    name:     str | None = None
    host:     str | None = None
    port:     str | None = None
    user:     str | None = None
    pswd:     str | None = None
    table:    str | None = None
    rdir:     str | None = None
    sdir:     str | None = None
    limit:    int | None = None

class GraphBackupRequest(BaseModel):
    graph_requestid: str=Field(default_factory=lambda: str(uuid.uuid4()))
    graph_filter:    GraphFilter | None = None
    backup_config:   BackupConfig | None = None

class GraphBackupListRequest(BaseModel):
    graph_requestid: str=Field(default_factory=lambda: str(uuid.uuid4()))
    backup_config:   BackupConfig | None = None

class GraphBackupListResponse(BaseModel):
    graph_requestid: str=Field(default_factory=lambda: str(uuid.uuid4()))
    table_list:      list[str]=[]

class RestoreConfig(BaseModel):
    format:   str | None = None
    location: str | None = None
    name:     str | None = None
    host:     str | None = None
    port:     str | None = None
    user:     str | None = None
    pswd:     str | None = None
    table:    str | None = None
    rdir:     str | None = None
    sdir:     str | None = None

class GraphRestoreRequest(BaseModel):
    graph_requestid: str=Field(default_factory=lambda: str(uuid.uuid4()))
    restore_config:  RestoreConfig | None = None


"""
    Data Import/Export Configuration
"""
class IOConfig(BaseModel):
    format:           str | None = None
    location:         str | None = None
    name:             str | None = None
    host:             str | None = None
    port:             str | None = None
    user:             str | None = None
    pswd:             str | None = None
    table:            str | None = None
    rdir:             str | None = None
    sdir:             str | None = None
    file_rdir:        str | None = None
    file_sdir:        str | None = None
    file_name:        str | None = None

class GraphImportRequest(BaseModel):
    graph_requestid: str=Field(default_factory=lambda: str(uuid.uuid4()))
    io_config:       IOConfig | None = None
    backup:          bool=True

class GraphExportRequest(BaseModel):
    graph_requestid:  str=Field(default_factory=lambda: str(uuid.uuid4()))
    container_name:   str | None = None
    partition_key:    str # knowledge_id
    data_filter:      GraphFilter | None = None
    io_config:        IOConfig | None = None
    include_datetime: bool = True


"""" GraphServiceManager """
"""
    Request and Response for User Access Permitted Graphs
"""
# User-level Access
class Edge(BaseModel):
    edge_id:        str | None = None
    edge_traceid:   str | None = None
    edge_version:   int | None = None
    edge_status:    int | None = None

    updated_by:     str | None = None

    source_node_id: str | None = None
    target_node_id: str | None = None
    edge_type:      str | None = None
    edge_category:  str | None = None
    edge_name:      str | None = None
    edge_strength:  float | None = None

    knowledge_id:   str | None = None
    
    edge_tags:      List[str] | None = None

    created_at:     datetime | None = None
    updated_at:     datetime | None = None

class Node(BaseModel):
    # Trace Information
    node_sequence:  int | None = None
    node_id:        str | None = None
    node_traceid:   str | None = None
    node_version:   int | None = None
    node_status:    int | None = None

    # Control Information
    updated_by:     str | None = None

    # Specification
    node_type:      str | None = None
    node_cluster:   str | None = None
    node_category:  str | None = None
    node_name:      str | None = None

    # Statistics
    node_indegree:  int | None = None
    node_outdegree: int | None = None

    # Chunkg Trace Information
    data_id:        str | None = None
    data_traceid:   str | None = None
    data_version:   int | None = None

    # Chunk Category Information
    data_type:      str | None = None # TEXT, IMAGE, TABLE, DOCUMENT, etc.
    content_type:   str | None = None

    # Chunk Tags
    data_languages: List[str] | None = None
    data_keywords:  List[str] | None = None

    # Dependent
    knowledge_id:   str | None = None # Map to Original Knowledge

    # Tags
    node_tags:      List[str] | None = None

    # Time Information
    created_at:     datetime | None = None
    updated_at:     datetime | None = None

class Graph(BaseModel):
    nodes: list[Node] | None = None
    edges: list[Edge] | None = None

class UserGraphRequest(BaseModel):
    graph_requestid: str=Field(default_factory=lambda: str(uuid.uuid4()))
    container_name:  str | None = None
    partition_key:   str # knowledge_id
    data_filter:     GraphFilter

class UserGraphResponse(BaseModel):
    graph_requestid: str
    filtered_data:   list[Graph]=[]