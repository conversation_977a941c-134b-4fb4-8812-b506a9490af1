from pydantic import BaseModel, <PERSON>
from typing import Literal
import uuid
from datetime import datetime

from ..settings import SETTINGS

from ..database.graph.schemas.graph import *

"""
    Graph Search
"""
class GraphSearchInfo(BaseModel):
    container_name: str | None = None
    knowledge_id:   str | None = None  # Optional - will be auto-extracted from data_ids if not provided
    node_ids:       list[str] = []
    data_ids:       list[str] = []  # Support input data_ids (chunk IDs)

class GraphSearchRequest(BaseModel):
    retrieval_requestid: str=Field(default_factory=lambda: str(uuid.uuid4()))
    search_info:         list[GraphSearchInfo]=[]
    search_method:       str='default'
    search_config:       dict=Field(default=dict(), description="Graph search configuration")

class GraphSearchResponse(BaseModel):
    retrieval_requestid:   str
    retrieved_data:        list[SecretGraph] = Field(default=[], description="List of Graph Objects")
    graph_retrieval_count: int=0
    graph_retrieval_time:  float=0.0