import urllib.parse
from contextlib import contextmanager

from ....settings import SETTINGS

from ....logger.log_handler import get_logger

logger = get_logger(__name__)

if SETTINGS.DATB.FORM.upper() == "MODB":
    from mongoengine import connect, disconnect
    from pymongo.errors import ServerSelectionTimeoutError
    from ..models.registry_models import QAFlowCitationLog, KnowledgeDB,KeywordMapping

    model_mapper = {
        SETTINGS.DATB.QACITATION_TABLE: QAFlowCitationLog,
        SETTINGS.DATB.REGISTRY_TABLE:   KnowledgeDB,
        SETTINGS.DATB.KM_TABLE: KeywordMapping
    }
    
    if SETTINGS.DATB.LOCA.lower() == "azure":
        # Access Database Settings
        DB_URL = '{user}:{pswd}@{host}:{port}/?{other}'.format(
            host=urllib.parse.quote_plus(SETTINGS.DATB.HOST),  # DB host
            port=urllib.parse.quote_plus(SETTINGS.DATB.PORT),  # DB port
            other=SETTINGS.DATB.CONFIG.get("OTHER", ""),  # DB name
            user=urllib.parse.quote_plus(SETTINGS.DATB.USER),  # DB user
            pswd=urllib.parse.quote_plus(SETTINGS.DATB.PSWD)   # DB pswd
        )

    elif SETTINGS.DATB.LOCA.lower() == "server":
        
        # Access Database Settings
        if SETTINGS.DATB.USER and SETTINGS.DATB.PSWD:
            DB_URL = '{user}:{pswd}@{host}:{port}/{name}'.format(
                host=urllib.parse.quote_plus(SETTINGS.DATB.HOST),  # DB host
                port=urllib.parse.quote_plus(SETTINGS.DATB.PORT),  # DB port
                name=urllib.parse.quote_plus(SETTINGS.DATB.NAME),  # DB name
                user=urllib.parse.quote_plus(SETTINGS.DATB.USER),  # DB user
                pswd=urllib.parse.quote_plus(SETTINGS.DATB.PSWD)   # DB pswd
            )
        
        else:
            DB_URL = '{host}:{port}/{name}'.format(
                host=urllib.parse.quote_plus(SETTINGS.DATB.HOST),  # DB host
                port=urllib.parse.quote_plus(SETTINGS.DATB.PORT),  # DB port
                name=urllib.parse.quote_plus(SETTINGS.DATB.NAME)   # DB name
            )
    
    DATABASE_URL = f"mongodb://{DB_URL}"

    # Test DB Connection
    try:
        connect(SETTINGS.DATB.NAME, host=DATABASE_URL)
        logger.info(f"Connected : <{SETTINGS.BASE.APP_NAME}> <{SETTINGS.DATB.NAME}> Database")

    except ServerSelectionTimeoutError:
        err_msg = f"DB Connection Timeout Error : <{SETTINGS.BASE.APP_NAME}> <{SETTINGS.DATB.NAME}> Database"
        logger.error(err_msg)
        raise Exception(err_msg)
    
    # Handle any other exceptions that might occur
    except:
        err_msg = f"Unexpected Error : <{SETTINGS.BASE.APP_NAME}> <{SETTINGS.DATB.NAME}> Database"
        logger.error(err_msg)
        raise Exception(err_msg)

    # DB via Function Call
    @contextmanager
    def get_db_func(collection_name: str):
        ModelDB = model_mapper.get(collection_name, None)
        if not ModelDB:
            raise Exception("MongoDB Error : Invalid Collection Name")

        try:
            session = ModelDB._get_db().client.start_session()
            session.start_transaction()
            yield session
            session.commit_transaction()

        except Exception as e:
            session.abort_transaction()  # Abort the transaction on error
            raise e
        
        finally:
            session.end_session()

    # DB via API Call
    @contextmanager
    def get_db_api(collection_name: str):
        ModelDB = model_mapper.get(collection_name, None)
        if not ModelDB:
            raise Exception("MongoDB Error : Invalid Collection Name")
        
        try:
            session = ModelDB._get_db().client.start_session()
            session.start_transaction()
            yield session
            session.commit_transaction()

        except Exception as e:
            session.abort_transaction()  # Abort the transaction on error
            raise e
        
        finally:
            session.end_session()

else:
    err_msg = f"Unknown DB Error : <{SETTINGS.DATB.NAME}> Database"
    logger.error(err_msg)
    raise Exception(err_msg)