import os
from typing import List, NamedTuple
from pydantic import BaseSettings, validator, Field
import json

CURRENT_DIR = os.path.dirname(os.path.abspath(__file__))
SERVICE_DIR = os.path.dirname(CURRENT_DIR)
PROJECT_DIR = os.path.dirname(SERVICE_DIR)
SETTING_FILE_ENCODING = 'utf-8'
LOAD_SECRET_FROM_FILE = os.environ.get("LOAD_SECRET_FROM_FILE", "TRUE")

# Load Global Config File if Any
GLOBAL_SETTING_KEY = 'share_'
GLOBAL_LOGGING_KEY = 'log_'
GLOBAL_BACKUP_KEY  = 'backup_'
GLOBAL_EXPORT_KEY  = 'export_'

# Load App Setting File
SETTING_FILE = os.path.join(SERVICE_DIR, 'src', 'config', 'settings.env')
SETTING_FILE_AKS = os.path.join(SERVICE_DIR, 'src', 'config', 'aks.env')

# Load Default DB Template if Any
INIT_DB_FILE = os.path.join(SERVICE_DIR, 'src', 'config', 'init_config.py')

# Load Global Setting File (will override local app setting)
GLOBAL_SETTING_FILE = os.path.join(PROJECT_DIR, 'config', 'settings.json')

def read_file_to_dict(file_path: str) -> dict:
    try:
        if not file_path.startswith("/"):
            # load secret from local path
            file_path = os.path.join(SERVICE_DIR, "files", file_path)
            
        if os.path.isfile(file_path) and os.path.exists(file_path):
            with open(file_path, "r") as file:
                data_dict = json.load(file)
                return data_dict
    except Exception as e:
            raise IOError(f"Error reading file: {str(e)}")

def read_string_to_dict(input_str: str) -> dict:
    try:
        if not input_str or input_str.strip() == "{}":
            return {}
        return json.loads(input_str)
    except Exception as e:
        return {}
    
def read_string_to_list(input_str: str) -> list:
    try:
        if not input_str or input_str.strip() == "[]":
            return []
        return json.loads(input_str)
    except Exception as e:
        return[]
    

class AppSettings(BaseSettings):
    APP_NAME:        str
    APP_HOST:        str 
    APP_PORT:        int 
    APP_INIT_CHECK:  bool 
    APP_API:         bool 
    APP_FUNC:        bool 
    APP_TIMEOUT:     float 
    APP_STANDALONE:  bool 
    APP_PRODUCTION:  bool 
    APP_ENCRYPTION:  bool 
    APP_VER:         str
    ORIGINS:         List[str] = ["*"]
    ENDPOINT_PREFIX: str
    APP_AUTH:       str = ""
    
    class Config:
        env_file = SETTING_FILE_AKS
        env_prefix = ''
        env_file_encoding = SETTING_FILE_ENCODING


class ConstraintSettings(BaseSettings):
    NAMING: list = []

    class Config:
        env_file = SETTING_FILE
        env_prefix = 'CSTR_'
        env_file_encoding = SETTING_FILE_ENCODING


class DatabaseSettings(BaseSettings):
    DB_FORM: list = []
    DB_FORM_STR: str
    FORM:    str
    LOCA:    str
    NAME:    str
    HOST:    str
    PORT:    str
    USER:    str
    PSWD: str = ""  # DB Pswd from validator, removed
    PSWD_PATH: str
    PSWD_KEY_NAME: str
    CONFIG:  dict = {}
    OTHER: str
    RDIR:    str
    SDIR:    str
    REGISTRY_TABLE:   str
    QACITATION_TABLE: str
    KM_TABLE: str
    SEP:     str # Separator for converting list to string in db

    def __init__(self, **args):
        super().__init__(**args)
        self.DB_FORM = read_string_to_list(self.DB_FORM_STR)
        self.CONFIG = {"OTHER": self.OTHER}

        datb_dict = read_file_to_dict(self.PSWD_PATH)
        if LOAD_SECRET_FROM_FILE == "TRUE":
            self.PSWD = datb_dict[self.PSWD_KEY_NAME]
        
    ### TODO: Should update to field_validator('RDIR', 'SDIR', mode='before')
    @validator('RDIR', 'SDIR', pre=True)
    def validate_dir(cls, value, field):
        # Check if global settings
        if os.path.isfile(GLOBAL_SETTING_FILE):
            with open(GLOBAL_SETTING_FILE) as global_setting_file:
                global_settings = json.load(global_setting_file)
                key = GLOBAL_SETTING_KEY + field.name.lower()
                if key in global_settings.keys():
                    value = global_settings[key]

        # Check if value is an executable function
        if isinstance(value, str) and "os.path" in value:
            # Use eval to convert it to a full path
            try:
                value = eval(value)
            except:
                raise ValueError(f"Error in Evaluating {value}")

        return value

    class Config:
        env_file = SETTING_FILE_AKS
        env_prefix = 'DATB_'
        env_file_encoding = SETTING_FILE_ENCODING

class BackupSettings(BaseSettings):
    DB_FORM:   list[str] = ['SLDB', 'PGDB']
    FORM:      str       = 'SLDB'
    LOCA:      str       = 'local'
    NAME:      str       = 'test_backup' # DB Name
    HOST:      str       = '' # DB Host 
    PORT:      str       = '' # DB Port
    USER:      str       = '' # DB User
    PSWD:      str       = '' # DB Pswd
    RDIR:      str       = SERVICE_DIR # DB Root
    SDIR:      str       = 'sldb_backup' # DB Dir
    TABLE:     str       = 'knowledge'
    LIMIT:     int       = 100 # Backup Number
    
    ### TODO: Should update to field_validator('RDIR', 'SDIR', mode='before')
    @validator('RDIR', 'SDIR', pre=True)
    def validate_dir(cls, value, field):
        # Check if global settings
        if os.path.isfile(GLOBAL_SETTING_FILE):
            with open(GLOBAL_SETTING_FILE) as global_setting_file:
                global_settings = json.load(global_setting_file)
                key = GLOBAL_BACKUP_KEY + field.name.lower()
                if key in global_settings.keys():
                    value = global_settings[key]

        # Check if value is an executable function
        if isinstance(value, str) and "os.path" in value:
            # Use eval to convert it to a full path
            try:
                value = eval(value)
            except:
                raise ValueError(f"Error in Evaluating {value}")

        return value
    
    class Config:
        env_file = SETTING_FILE
        env_prefix = 'BKUP_'
        env_file_encoding = SETTING_FILE_ENCODING

class ImportSettings(BaseSettings):
    DB_FORM:   list[str] = ['SLDB', 'PGDB']
    FILE_FORM: list[str] = ['JSON', 'CSV']
    
    class Config:
        env_file = SETTING_FILE
        env_prefix = 'IMPT_'
        env_file_encoding = SETTING_FILE_ENCODING

class ExportSettings(BaseSettings):
    DB_FORM:   list[str] = ['SLDB', 'PGDB']
    FILE_FORM: list[str] = ['JSON', 'CSV']
    FORM:      str       = 'JSON'
    LOCA:      str       = 'local'
    NAME:      str       = 'test_export'
    HOST:      str       = ''
    PORT:      str       = ''
    USER:      str       = ''
    PSWD:      str       = ''
    TABLE:     str       = 'knowledge'
    RDIR:      str       = SERVICE_DIR
    SDIR:      str       = 'sldb_backup'
    FILE_RDIR: str       = SERVICE_DIR
    FILE_SDIR: str       = 'export'
    FILE_NAME: str       = 'knowledge'

    # @field_validator('TAG_RDIR', 'TAG_SDIR', mode='before')
    @validator('RDIR', 'SDIR', 'FILE_RDIR', 'FILE_SDIR', pre=True)
    def validate_dir(cls, value, field):
        # Check if global settings
        if os.path.isfile(GLOBAL_SETTING_FILE):
            with open(GLOBAL_SETTING_FILE) as global_setting_file:
                global_settings = json.load(global_setting_file)
                key = GLOBAL_EXPORT_KEY + field.name.lower()
                if key in global_settings.keys():
                    value = global_settings[key]

        # Check if value is an executable function
        if isinstance(value, str) and "os.path" in value:
            # Use eval to convert it to a full path
            try:
                value = eval(value)
            except:
                raise ValueError(f"Error in Evaluating {value}")

        return value
    
    class Config:
        env_file = SETTING_FILE
        env_prefix = 'EXPT_'
        env_file_encoding = SETTING_FILE_ENCODING

class VectorDatabaseSettings(BaseSettings):
    DB_FORM: list = []
    DB_FORM_STR: str
    FORM: str
    LOCA: str
    NAME: str # DB Name
    HOST: str # DB Host
    PORT: str # DB Port
    USER: str # DB User
    PSWD: str = ""  # DB Pswd from validator, removed
    PSWD_PATH: str  # from vault
    PSWD_PATH_NAME: str
    RDIR: str  # DB Root
    SDIR: str  # DB Dir
    TABLE: str
    CONFIG: dict = {}
    CONFIG_KEY_NAME: str
    CONFIG_KEY_VERSION: str
    CONFIG_VAULT_URI: str
    DIMENSION: int
    PERMITTED_BATCH_SIZE: int = 5000

    def __init__(self, **args):
        super().__init__(**args)
        self.DB_FORM = read_string_to_list(self.DB_FORM_STR)
        self.CONFIG = {
            "encryption_config":
                {
                    "key_name": self.CONFIG_KEY_NAME,
                    "key_version": self.CONFIG_KEY_VERSION,
                    "vault_uri": self.CONFIG_VAULT_URI
                }
        }
        secret_dict = read_file_to_dict(self.PSWD_PATH)
        if LOAD_SECRET_FROM_FILE == "TRUE":
            self.PSWD = secret_dict[self.PSWD_PATH_NAME]

    ### TODO: Should update to field_validator('RDIR', 'SDIR', mode='before')
    @validator('RDIR', 'SDIR', pre=True)
    def validate_dir(cls, value, field):
        # Check if global settings
        if os.path.isfile(GLOBAL_SETTING_FILE):
            with open(GLOBAL_SETTING_FILE) as global_setting_file:
                global_settings = json.load(global_setting_file)
                key = GLOBAL_SETTING_KEY + field.name.lower()
                if key in global_settings.keys():
                    value = global_settings[key]

        # Check if value is an executable function
        if isinstance(value, str) and "os.path" in value:
            # Use eval to convert it to a full path
            try:
                value = eval(value)
            except:
                raise ValueError(f"Error in Evaluating {value}")

        return value

    class Config:
        env_file = SETTING_FILE_AKS
        env_prefix = 'VTDB_'
        env_file_encoding = SETTING_FILE_ENCODING


class GenAISettings(BaseSettings):
    QUERY_TOKEN_MAX:  int=1800
    
    MODEL_ID:         str
    MODEL_VERSION:    int
    MODEL_NAME:       str
    MODEL_TYPE:       str
    MODEL_CLUSTER:    str
    MODEL_LOCATION:   str
    MODEL_HOST:       str
    MODEL_PORT:       str
    MODEL_API:        str
    MODEL_ENGINE:     str
    MODEL_BASE:       str
    MODEL_PARAMETERS: dict = {}
    MODEL_PARAMETERS_STR: str
    MODEL_SECRETS:    dict = {}
    MODEL_SECRETS_PATH: str
    MODEL_SECRETS_KEY_NAME: str
    MODEL_SECRETS_KEY_VALUE: str
    MODEL_SECRETS_API_VERSION: str
    MODEL_SECRETS_REGION: str
    MODEL_FILE_PATH:  str
    MODEL_KEY:        str

    EMBED_ID:         str
    EMBED_HOST:       str
    EMBED_PORT:       str
    EMBED_API:        str
    EMBED_LOCATION:   str
    EMBED_ENGINE:     str
    EMBED_BASE:       str
    EMBED_MODEL:      str
    EMBED_PARAMETERS: dict = {}
    EMBED_PARAMETERS_STR: str
    EMBED_SECRETS:    dict = {}
    EMBED_SECRETS_PATH: str
    EMBED_SECRETS_KEY_NAME: str
    EMBED_SECRETS_KEY_VALUE: str
    EMBED_SECRETS_API_VERSION: str
    EMBED_SECRETS_REGION: str
    EMBED_KEY:        str
    EMBED_TIMEOUT:    int

    KEYWD_ID:         str 
    KEYWD_HOST:       str 
    KEYWD_PORT:       str 
    KEYWD_API:        str 
    KEYWD_LOCATION:   str 
    KEYWD_ENGINE:     str 
    KEYWD_BASE:       str 
    KEYWD_MODEL:      str 
    KEYWD_PARAMETERS: dict = {}
    KEYWD_PARAMETERS_STR: str
    KEYWD_SECRETS:    dict = {}
    KEYWD_KEY: str = ""
    KEYWD_KEY_PATH: str 
    KEYWD_KEY_NAME:   str
    KEYWD_TIMEOUT:    int

    def __init__(self, **args):
        super().__init__(**args)

        self.MODEL_PARAMETERS = read_string_to_dict(self.MODEL_PARAMETERS_STR)
        self.EMBED_PARAMETERS = read_string_to_dict(self.EMBED_PARAMETERS_STR)
        self.KEYWD_PARAMETERS = read_string_to_dict(self.KEYWD_PARAMETERS_STR)

        if LOAD_SECRET_FROM_FILE == "TRUE":
            self.MODEL_SECRETS = {
                "header": {"x-api-key": None, "app-id": None},
                "api_version": self.MODEL_SECRETS_API_VERSION
            }
            model_dict = read_file_to_dict(self.MODEL_SECRETS_PATH)
            self.MODEL_SECRETS["header"]["app-id"] = model_dict[self.MODEL_SECRETS_KEY_NAME]
            self.MODEL_SECRETS["header"]["x-api-key"] = model_dict[self.MODEL_SECRETS_KEY_VALUE]
            if self.MODEL_SECRETS_REGION != "":
                self.MODEL_SECRETS["header"]["x-ms-region"] = self.MODEL_SECRETS_REGION

            self.EMBED_SECRETS = {
                "header": {"x-api-key": None, "app-id": None},
                "api_version": self.EMBED_SECRETS_API_VERSION
            }
            embd_dict = read_file_to_dict(self.EMBED_SECRETS_PATH)
            self.EMBED_SECRETS["header"]["app-id"] = embd_dict[self.EMBED_SECRETS_KEY_NAME]
            self.EMBED_SECRETS["header"]["x-api-key"] = embd_dict[self.EMBED_SECRETS_KEY_VALUE]
            if self.EMBED_SECRETS_REGION != "":
                self.EMBED_SECRETS["header"]["x-ms-region"] = self.EMBED_SECRETS_REGION
        else:
            self.MODEL_SECRETS = read_string_to_dict(self.MODEL_KEY)
            self.EMBED_SECRETS = read_string_to_dict(self.EMBED_KEY)

        cog_dict = read_file_to_dict(self.KEYWD_KEY_PATH)
        self.KEYWD_KEY = cog_dict[self.KEYWD_KEY_NAME]


    class Config:
        env_file = SETTING_FILE_AKS
        env_prefix = 'GEAI_'
        env_file_encoding = SETTING_FILE_ENCODING

class InferenceSettings(BaseSettings):
    ENGINE_TIMEOUT:        float
    ENGINE_STREAM_TIMEOUT: float
    STATUS_CODE:           dict
    RETRY_LIMIT:           int=3

    class Config:
        env_file = SETTING_FILE
        env_prefix = 'INFR_'
        env_file_encoding = SETTING_FILE_ENCODING

class RetrievalSettings(BaseSettings):
    DEFAULT_KEYWORD_CONFIG: dict = {}
    DEFAULT_KEYWORD_LIMIT:  int = 25
    DEFAULT_VECTOR_CONFIG:  dict = {}
    DEFAULT_GRAPH_CONFIG:   dict = {}
    DEFAULT_HYBRID_CONFIG: dict = {}
    DEFAULT_RERANK_THRESHOLD: float
    LANGUAGE_MAPPING:       dict = {}

    class Config:
        env_file = SETTING_FILE
        env_prefix = 'RETR_'
        env_file_encoding = SETTING_FILE_ENCODING    


class LoggerSettings(BaseSettings):
    DB_FORM:   list = []
    FORM:      str 
    LOCA:      str 
    NAME:      str 
    HOST:      str 
    PORT:      str 
    USER:      str 
    PSWD:      str 
    RDIR:      str 
    SDIR:      str 
    TABLE:     str 
    LIMIT:     int 
    FILE_RDIR: str 
    FILE_SDIR: str 
    FILE_NAME: str 
    FILE_BYTE: int   # Backup file max bytes
    FILE_NUM:  int    # Backup file number
    SAVE_DB:   bool
    SAVE_FILE: bool
    LEVEL:     str 

    ### TODO: Should update to field_validator('RDIR', 'SDIR', mode='before')
    @validator('RDIR', 'SDIR', 'FILE_RDIR', 'FILE_SDIR', pre=True)
    def validate_dir(cls, value, field):
        # Check if global settings
        if os.path.isfile(GLOBAL_SETTING_FILE):
            with open(GLOBAL_SETTING_FILE) as global_setting_file:
                global_settings = json.load(global_setting_file)
                key = GLOBAL_LOGGING_KEY + field.name.lower()
                if key in global_settings.keys():
                    value = global_settings[key]

        # Check if value is an executable function
        if isinstance(value, str) and "os.path" in value:
            # Use eval to convert it to a full path
            try:
                value = eval(value)
            except:
                raise ValueError(f"Error in Evaluating {value}")

        return value

    class Config:
        env_file = SETTING_FILE
        env_prefix = 'LOG_'
        env_file_encoding = SETTING_FILE_ENCODING


class StatusSettings(BaseSettings):
    INFO_CODE_START:    int = 100
    INFO_CODE_END:      int = 200
    SUCC_CODE_START:    int = 200
    SUCC_CODE_END:      int = 300
    REDIR_CODE_START:   int = 300
    REDIR_CODE_END:     int = 400
    CLIENT_CODE_START:  int = 400
    CLIENT_CODE_END:    int = 500
    SERERR_CODE_START:  int = 500
    SERERR_CODE_END:    int = 600    

class AzureStorageSettings(BaseSettings):
    ACCOUNT_NAME:     str
    ACCOUNT_KEY:      str = "" # removed
    ACCOUNT_KEY_PATH: str
    ACCOUNT_KEY_NAME: str
    SOURCE_CONTAINER: str
    SHARED_CONTAINER: str
    SHARE_DOC_ERR:    str
    SHARE_MED_ERR:    str

    def __init__(self, **args):
        super().__init__(**args)
        secret_dict = read_file_to_dict(self.ACCOUNT_KEY_PATH)
        if LOAD_SECRET_FROM_FILE == "TRUE":
            self.ACCOUNT_KEY = secret_dict[self.ACCOUNT_KEY_NAME]

    class Config:
        env_file = SETTING_FILE_AKS
        env_prefix = 'AZST_'
        env_file_encoding = SETTING_FILE_ENCODING

class GraphDatabaseSettings(BaseSettings):
    FORM:             str
    LOCA:             str
    NAME:             str
    HOST:             str
    PORT:             str
    PSWD:             str = "" # removed
    PSWD_PATH:        str
    PSWD_NAME:        str
    CONFIG:           dict={}
    ENDPOINT:         str
    CONTAINER_PREFIX: str='Test'
    CONTAINER:        str  
    PARTITION_KEY:    str

    def __init__(self, **args):
        super().__init__(**args)
        secret_dict = read_file_to_dict(self.PSWD_PATH)
        if LOAD_SECRET_FROM_FILE == "TRUE":
            self.PSWD = secret_dict[self.PSWD_NAME]

    class Config:
        env_file = SETTING_FILE_AKS
        env_prefix = 'GPDB_'
        env_file_encoding = SETTING_FILE_ENCODING

class Config(NamedTuple):
    BASE: AppSettings = AppSettings()
    CSTR: ConstraintSettings = ConstraintSettings()
    DATB: DatabaseSettings = DatabaseSettings()
    BKUP: BackupSettings = BackupSettings()
    IMPT: ImportSettings = ImportSettings()
    EXPT: ExportSettings = ExportSettings()
    
    VTDB: VectorDatabaseSettings = VectorDatabaseSettings()

    AZST: AzureStorageSettings = AzureStorageSettings()

    GEAI: GenAISettings = GenAISettings()
    INFR: InferenceSettings = InferenceSettings()
    RETR: RetrievalSettings = RetrievalSettings()

    LOG:  LoggerSettings = LoggerSettings()
    STAT: StatusSettings = StatusSettings()

    GPDB: GraphDatabaseSettings = GraphDatabaseSettings()

SETTINGS = Config()