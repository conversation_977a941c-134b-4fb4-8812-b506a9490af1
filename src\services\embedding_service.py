from datetime import datetime
import time
import uuid
import inspect
import httpx

from ..settings import SETTINGS

from ..schemas.format import (
    ResponseFormatter,
    Response
)

from ..schemas.embedding import (
    EmbeddingEngine,
    EmbeddingRequest,  
    EmbeddingResponse
)

from ..logger.log_handler import get_logger

logger = get_logger(__name__)

class EmbeddingServiceManager:
    response_format = ResponseFormatter(suffix = f"{inspect.stack()[0][3]}")

    default_engine = EmbeddingEngine(
        embedding_id=SETTINGS.GEAI.EMBED_ID,
        embedding_host=SETTINGS.GEAI.EMBED_HOST,
        embedding_port=SETTINGS.GEAI.EMBED_PORT,
        embedding_api=SETTINGS.GEAI.EMBED_API,
        embedding_location=SETTINGS.GEAI.EMBED_LOCATION,
        embedding_engine=SETTINGS.GEAI.EMBED_ENGINE,
        embedding_base=SETTINGS.GEAI.EMBED_BASE,
        embedding_model=SETTINGS.GEAI.EMBED_MODEL,
        embedding_parameters=SETTINGS.GEAI.EMBED_PARAMETERS,
        embedding_secrets=SETTINGS.GEAI.EMBED_SECRETS,
        embedding_key=SETTINGS.GEAI.EMBED_KEY,
        embedding_timeout=SETTINGS.GEAI.EMBED_TIMEOUT
    )

    def __init__(self, api_call: bool, engine: EmbeddingEngine | None = None):
        self.api_call = api_call
        self.engine   = engine

    """
        Request Operation
    """
    def init_engine(self, engine: EmbeddingEngine | None = None) -> Response:
        if not engine:
            self.engine = self.default_engine
        else:
            self.engine = engine

        if self.engine.embedding_location.lower() == "azure":
            response  = Response(status_code=200, detail=self.response_format.ok(f"Init Embedding Completed : <{SETTINGS.BASE.APP_NAME}> Initiated Embedding Engine in Azure"))

        elif self.engine.embedding_location.lower() == "server":
            response = Response(status_code=200, detail=self.response_format.ok(f"Init Embedding Completed : <{SETTINGS.BASE.APP_NAME}> Initiated Embedding Engine in Server"))

        else:
            response = Response(status_code=404, detail=self.response_format.error(f"Init Embedding Engine Error : <{SETTINGS.BASE.APP_NAME}> Cannot Recognie Embedding Engine Location <{self.engine.embedding_location}>"))

        if response.status_code >= SETTINGS.STAT.SUCC_CODE_END:
            logger.error(response.detail)

        return response

    def text_embedding(self, request: EmbeddingRequest) -> tuple[EmbeddingResponse, Response]:
        logger.info(f"Processing : <{SETTINGS.BASE.APP_NAME}> Embedding Text")
        response_data = EmbeddingResponse(**request.__dict__)
        start_at      = time.time()

        tool_tokens   = 0

        self.init_engine()

        ### TODO: Add Token Limit Check
        """ 1. Perform Token Limit Check for the data_input """
        
        """ 2. Embed Data """
        if self.engine.embedding_location.lower() == "azure":
            processed_data, tool_tokens, response = self.azure_server(raw_data=request.data_input)

        elif self.engine.embedding_location.lower() == "server":
            
            if self.engine.embedding_engine.lower() == "ollama":
                processed_data, tool_tokens, response = self.ollama_server(raw_data=request.data_input)
                
            else:
                response = Response(status_code=404, detail=self.response_format.error(f"Embedding Processing Error : <{SETTINGS.BASE.APP_NAME}> Failed to Perform Embedding <{self.engine.embedding_engine}>"))

        else:
            response = Response(status_code=404, detail=self.response_format.error(f"Embedding Processing Error : <{SETTINGS.BASE.APP_NAME}> Failed to Perform Embedding <{self.engine.embedding_engine}>"))

        """ 4. Updated Result """
        # 4.1. Embedding Fail
        if response.status_code >= SETTINGS.STAT.SUCC_CODE_END:
            logger.error(response.detail)
            return response_data, response

        # 4.2. Embedding Completed but Encountered Different Lengths between Raw Data and Processed Data
        elif len(request.data_input) != len(processed_data):
            response  = Response(status_code=500, detail=self.response_format.error(f"Embedding Processing Error : <{SETTINGS.BASE.APP_NAME}> Found Unequal Number between Raw Data and Processed Data"))
            logger.error(response.detail)
            return response_data, response
        
        # 4.3. Embedding Success 
        else:
            response = Response(status_code=200, detail=self.response_format.ok(f"Embedding Completed : <{SETTINGS.BASE.APP_NAME}> Completed Embedding Process"))
            logger.info(response.detail)

        ### TODO: Add tokens count
        """ 5. Process Data """
        response_data.__dict__.update(
            data_output      = processed_data,
            embedding_model  = self.engine.embedding_model,
            embedding_tokens = tool_tokens,
            embedding_time   = time.time() - start_at,
            response_at      = datetime.now()
        )

        return response_data, response

    def ollama_server(self, raw_data: list[str]) -> tuple[list[list[float]], int, Response]:
        processed_data = []
        tool_tokens    = 0

        payload = dict()

        # Model Options
        options = dict()
        if self.engine.embedding_parameters:
            options.update(self.engine.embedding_parameters)
        if self.engine.embedding_secrets:
            options.update(self.engine.embedding_secrets)
        if options:
            payload["options"] = options
            
        payload["model"] = self.engine.embedding_base
        payload["input"] = raw_data

        api_url = f"http://{self.engine.embedding_host}:{self.engine.embedding_port}/{self.engine.embedding_api}"
        
        # Post Request to Server
        try:
            resp   = httpx.post(api_url, json=payload, timeout=self.engine.embedding_timeout)
            result = resp.json()

            # Error during Calling Server
            if not resp.status_code == httpx.codes.ok:
                response = Response(status_code=resp.status_code, detail=result["error"])
                logger.error(response.detail)
                return processed_data, tool_tokens, response

            # Update Ouptut
            processed_data = result["embeddings"]
            tool_tokens    = result.get("prompt_eval_count", 0)
            response = Response(status_code=200, detail=self.response_format.ok(f"Embedding Success : <{SETTINGS.BASE.APP_NAME}> Completed Embedding"))

        except httpx.TimeoutException as e:
            response = Response(status_code=502, detail=self.response_format.error(f"Timeout Error : <{SETTINGS.BASE.APP_NAME}> Encountered Timeout Error when Connecting to Ollama Server", str(e)))
            logger.error(response.detail)

        except httpx.HTTPError as e:
            response = Response(status_code=502, detail=self.response_format.error(f"Connection Error : <{SETTINGS.BASE.APP_NAME}> Encountered Connection Error when Connecting to Ollama Server", str(e)))
            logger.error(response.detail)

        # Handle common exceptions that might occur
        except (BaseException, Exception) as e:
            response = Response(status_code=500, detail=self.response_format.error(f"Common Error : <{SETTINGS.BASE.APP_NAME}> Encountered Common Error when Calling Ollama Server", str(e)))
            logger.error(response.detail)

        # Handle any other exceptions that might occur
        except:
            response = Response(status_code=500, detail=self.response_format.error(f"Unexpected Error : <{SETTINGS.BASE.APP_NAME}> Encountered Unexpected Error when Calling Ollama Server", str(e)))
            logger.error(response.detail)

        return processed_data, tool_tokens, response


    def azure_server(self, raw_data: list[str]) -> tuple[list[list[float]], int, Response]:
        from openai import AzureOpenAI

        processed_data = []
        tool_tokens    = 0

        payload = dict()

        # Model Options
        options = dict()
        if self.engine.embedding_parameters:
            options.update(self.engine.embedding_parameters)
        if options:
            payload["options"] = options
    
        payload["model"] = self.engine.embedding_base
        payload["input"] = raw_data

        api_url = f"{self.engine.embedding_host}/{self.engine.embedding_port}"

        embedding_client = AzureOpenAI(
            api_key         = self.engine.embedding_key,
            api_version     = self.engine.embedding_secrets.get("api_version", ""),
            azure_endpoint  = api_url,
            default_headers = self.engine.embedding_secrets.get("header", {})
        )

        # Post Request to Server
        try:
            resp = embedding_client.embeddings.create(
                model = payload["model"],
                input = payload["input"]
            )

            # Error during Calling Server
            if not resp or not resp.data:
                response = Response(status_code=500, detail=self.response_format.error(f"Azure Embedding Error : <{SETTINGS.BASE.APP_NAME}> Failed to Retrieve Embedding Data from Azure Server"))
                logger.error(response.detail)
                return processed_data, tool_tokens, response

            # Update Ouptut
            processed_data = [_data.embedding for _data in resp.data]
            tool_tokens    = resp.usage.total_tokens
            response = Response(status_code=200, detail=self.response_format.ok(f"Embedding Success : <{SETTINGS.BASE.APP_NAME}> Completed Embedding"))

        except httpx.TimeoutException as e:
            response = Response(status_code=502, detail=self.response_format.error(f"Timeout Error : <{SETTINGS.BASE.APP_NAME}> Encountered Timeout Error when Connecting to Azure Server", str(e)))
            logger.error(response.detail)

        except httpx.HTTPError as e:
            response = Response(status_code=502, detail=self.response_format.error(f"Connection Error : <{SETTINGS.BASE.APP_NAME}> Encountered Connection Error when Connecting to Azure Server", str(e)))
            logger.error(response.detail)

        # Handle common exceptions that might occur
        except (BaseException, Exception) as e:
            response = Response(status_code=500, detail=self.response_format.error(f"Common Error : <{SETTINGS.BASE.APP_NAME}> Encountered Common Error when Calling Azure Server", str(e)))
            logger.error(response.detail)

        # Handle any other exceptions that might occur
        except:
            response = Response(status_code=500, detail=self.response_format.error(f"Unexpected Error : <{SETTINGS.BASE.APP_NAME}> Encountered Unexpected Error when Calling Azure Server", str(e)))
            logger.error(response.detail)

        return processed_data, tool_tokens, response


    def api_call_static(self, data, service: str, api_url: str, method: str, timeout: float | None) -> tuple[httpx.Response | None, Response]:
        response_data = None

        try:
            if method.lower() == "post":

                if isinstance(data, str):
                    if timeout:
                        resp = httpx.post(api_url, data=data, timeout=timeout)
                    else:
                        resp = httpx.post(api_url, data=data)

                else:
                    if timeout:
                        resp = httpx.post(api_url, json=data, timeout=timeout)
                    else:
                        resp = httpx.post(api_url, json=data)

            else:
                response = Response(status_code=500, detail=self.response_format.error(f"API Method Error : Unknown API Method <{method}>"))
                logger.error(response.detail)
                return response_data, response

            if not resp.status_code == httpx.codes.ok:
                response = Response(status_code=resp.status_code, detail=self.response_format.error(f"Response Error : Retrieving Data from <{service}> API Server", resp["detail"]))
                logger.error(response.detail)
            
            else:
                response = Response(status_code=resp.status_code, detail=self.response_format.ok(f"Success : Retrieved Data from <{service}> API Server"))
                response_data = resp

        except httpx.TimeoutException as e:
            response = Response(status_code=502, detail=self.response_format.error(f"Timeout Error : Retrieving Data <{service}> API Server", str(e)))
            logger.error(response.detail)

        except httpx.HTTPError as e:
            response = Response(status_code=502, detail=self.response_format.error(f"Connection Error : Retrieving Data <{service}> API Server", str(e)))
            logger.error(response.detail)

        # Handle common exceptions that might occur
        except (BaseException, Exception) as e:
            response = Response(status_code=500, detail=self.response_format.error(f"Common Error : Connecting to <{service}> API Server", str(e)))
            logger.error(response.detail)

        # Handle any other exceptions that might occur
        except:
            response = Response(status_code=500, detail=self.response_format.error(f"Unexpected Error : Connecting to <{service}> API Server"))
            logger.error(response.detail)

        return response_data, response


