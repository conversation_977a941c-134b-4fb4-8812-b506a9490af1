from fastapi import APIRouter, Depends, status
from typing import Any

from ..settings import SETTINGS
from ..utils import router_response_handler

from ..database.registry.services.knowledge_data import (
        DataManager as KnowledgeDataManager,
        SystemDataRequest as SystemKnowledgeRequest, 
        SystemDataResponse as SystemKnowledgeResponse,
    )

from ..database.registry.schemas.format import Response
from ..database.graph.services.graph_data import (
    DataManager as GraphDataManager,
    SystemDataRequest as SystemGraphRequest,
    SystemDataResponse as SystemGraphResponse
    
)

from ..services.graph_service import GraphServiceManager

from ..services.refinement_service import (
    RefinementKeyResponse,
    RefinementServiceManager,
    RefinementRequest,
    RefinementResponse
)

from ..services.keyword_service import (
    KeywordServiceManager, 
    KeywordExtractionRequest, 
    KeywordExtractionResponse,
    MappingRequest,
    MappingResult,

)

from ..services.embedding_service import (
    EmbeddingServiceManager,
    EmbeddingRequest,
    EmbeddingResponse
)

from ..services.vector_service import (
    VectorServiceManager,
    KeywordSearchRequest,
    KeywordSearchResponse,
    VectorSearchRequest,
    VectorSearchResponse,
    KnowledgeVectorSearchRequest,
    BatchMappingResult,
    BatchMappingRequest,
    KeywordExtractRequest,
    KeywordExtractReponse

    
)

from ..services.rerank_service import (
    RerankServiceManager,
    RerankRequest,
    RerankResponse
)

from ..schemas.graph import (
    GraphSearchRequest,
    GraphSearchResponse,
)

router = APIRouter(tags=["Request"])

# API DB Session
if SETTINGS.BASE.APP_API == True:
    default_api_call = True
else:
    default_api_call = False


@router.post("/request/query_refinement", include_in_schema=not(SETTINGS.BASE.APP_PRODUCTION), status_code=status.HTTP_200_OK, response_model=RefinementResponse)
def request_query_refinement(request: RefinementRequest, api_call: bool = default_api_call) -> RefinementResponse:
    request = RefinementRequest(**request.__dict__)
    response_data, response = RefinementServiceManager(api_call=api_call).refine_query(request=request)
    router_response_handler(response=response, api_call=api_call)
    return response_data


@router.post("/request/query_refinement", include_in_schema=not(SETTINGS.BASE.APP_PRODUCTION), status_code=status.HTTP_200_OK, response_model=RefinementResponse)
def request_query_refinement_with_key(request: RefinementRequest, api_call: bool = default_api_call) -> RefinementKeyResponse:
    request = RefinementRequest(**request.__dict__)
    response_data, response = RefinementServiceManager(api_call=api_call).refine_query_with_key(request=request)
    router_response_handler(response=response, api_call=api_call)
    return response_data

@router.post("/request/keyword_extraction", include_in_schema=not(SETTINGS.BASE.APP_PRODUCTION), status_code=status.HTTP_200_OK, response_model=KeywordExtractionResponse)
def request_keyword_extraction(request: KeywordExtractionRequest, api_call: bool = default_api_call) -> KeywordExtractionResponse:
    request = KeywordExtractionRequest(**request.__dict__)
    response_data, response = KeywordServiceManager(api_call=api_call).text_keyword(request=request)
    router_response_handler(response=response, api_call=api_call)
    return response_data

@router.post("/request/query_embedding", include_in_schema=not(SETTINGS.BASE.APP_PRODUCTION), status_code=status.HTTP_200_OK, response_model=EmbeddingResponse)
def request_query_embedding(request: EmbeddingRequest, api_call: bool = default_api_call) -> EmbeddingResponse:
    request = EmbeddingRequest(**request.__dict__)
    response_data, response = EmbeddingServiceManager(api_call=api_call).text_embedding(request=request)
    router_response_handler(response=response, api_call=api_call)
    return response_data

@router.post("/request/knowledge_search", include_in_schema=not(SETTINGS.BASE.APP_PRODUCTION), status_code=status.HTTP_200_OK, response_model=SystemKnowledgeResponse)
def system_query_knowledge(request: SystemKnowledgeRequest, api_call: bool = default_api_call) -> SystemKnowledgeResponse:
    request = SystemKnowledgeRequest(**request.__dict__)
    response_data, response = KnowledgeDataManager(api_call=api_call).query_data_by_system(request=request)
    router_response_handler(response=response, api_call=api_call)
    return response_data

@router.post("/request/keyword_search", include_in_schema=not(SETTINGS.BASE.APP_PRODUCTION), status_code=status.HTTP_200_OK, response_model=KeywordSearchResponse)
def request_keyword_search(request: KeywordSearchRequest, api_call: bool=default_api_call) -> KeywordSearchResponse:
    request = KeywordSearchRequest(**request.__dict__)
    response_data, response = VectorServiceManager(api_call=api_call).search_by_keywords(request=request)
    router_response_handler(response=response, api_call=api_call)
    return response_data

@router.post("/request/vector_search", include_in_schema=not(SETTINGS.BASE.APP_PRODUCTION), status_code=status.HTTP_200_OK, response_model=VectorSearchResponse)
def request_vector_search(request: VectorSearchRequest, api_call: bool=default_api_call) -> VectorSearchResponse:
    request = VectorSearchRequest(**request.__dict__)
    response_data, response = VectorServiceManager(api_call=api_call).search_by_vector(request=request)
    router_response_handler(response=response, api_call=api_call)
    return response_data

@router.post("/request/graph_search", include_in_schema=not(SETTINGS.BASE.APP_PRODUCTION), status_code=status.HTTP_200_OK, response_model=GraphSearchResponse)
def request_graph_search(request: GraphSearchRequest, api_call: bool = default_api_call) -> GraphSearchResponse:
    request = GraphSearchRequest(**request.__dict__)
    response_data, response = GraphServiceManager(api_call=api_call).search_by_graph(request=request)
    router_response_handler(response=response, api_call=api_call)
    return response_data

# @router.post("/request/deep_graph_search", include_in_schema=not(SETTINGS.BASE.APP_PRODUCTION), status_code=status.HTTP_200_OK, response_model=GraphSearchResponse)
# def request_deep_graph_search(request: GraphSearchRequest, api_call: bool = default_api_call) -> GraphSearchResponse:
#     request = GraphSearchRequest(**request.__dict__)
#     response_data, response = GraphServiceManager(api_call=api_call).deep_search_by_graph(request=request)
#     router_response_handler(response=response, api_call=api_call)
#     return response_data

@router.post("/request/rerank", include_in_schema=not(SETTINGS.BASE.APP_PRODUCTION), status_code=status.HTTP_200_OK, response_model=RerankResponse)
def request_rerank(request: RerankRequest, api_call: bool = default_api_call) -> RerankResponse:
    request = RerankRequest(**request.__dict__)
    response_data, response = RerankServiceManager(api_call=api_call).rerank_data(request=request)
    router_response_handler(response=response, api_call=api_call)
    return response_data





@router.post("/request/get_chunks_by_knowledge_id", include_in_schema=not(SETTINGS.BASE.APP_PRODUCTION), status_code=status.HTTP_200_OK, response_model=VectorSearchResponse)
def get_chunks_by_knowledge_id(request:KnowledgeVectorSearchRequest, api_call: bool = default_api_call) -> VectorSearchResponse:
    request = KnowledgeVectorSearchRequest(**request.__dict__)
    response_data, response = VectorServiceManager(api_call=api_call).get_chunks_by_knowledge_id(request=request)
    router_response_handler(response=response, api_call=api_call)
    return response_data

@router.post("/request/map_keyword_knowledge", include_in_schema=not(SETTINGS.BASE.APP_PRODUCTION), status_code=status.HTTP_200_OK, response_model=MappingResult)
def map_keyword_knowledge(request:MappingRequest, api_call: bool = default_api_call) -> MappingResult:
    request = MappingRequest(**request.__dict__)
    response_data, response = VectorServiceManager(api_call=api_call).map_keyword_knowledge(request=request)
    router_response_handler(response=response, api_call=api_call)
    return response_data


@router.post("/request/create_map_keyword_knowledge", include_in_schema=not(SETTINGS.BASE.APP_PRODUCTION), status_code=status.HTTP_200_OK, response_model=MappingResult)
def batch_create_map_keyword_knowledge(request: MappingResult,api_call: bool = default_api_call) -> MappingResult:
    response_data, response = VectorServiceManager(api_call=api_call).batch_create_mapping(request=request)
    router_response_handler(response=response, api_call=api_call)
    return response_data

@router.post("/request/batch_create_map_keyword_knowledge", include_in_schema=not(SETTINGS.BASE.APP_PRODUCTION), status_code=status.HTTP_200_OK, response_model=BatchMappingResult)
def batch_create_map_keyword_knowledge(api_call: bool = default_api_call) -> BatchMappingResult:
    response_data, response = VectorServiceManager(api_call=api_call).batch_map_active_knowledge()
    router_response_handler(response=response, api_call=api_call)
    return response_data


@router.post("/request/extract_keyword_knowledge", include_in_schema=not(SETTINGS.BASE.APP_PRODUCTION), status_code=status.HTTP_200_OK, response_model=KeywordExtractReponse)
def extract_keyword_knowledge(request:KeywordExtractRequest, api_call: bool = default_api_call) -> KeywordExtractReponse:
    request = KeywordExtractRequest(**request.__dict__)
    response_data, response = VectorServiceManager(api_call=api_call).extract_keyword_knowledge(request=request)
    router_response_handler(response=response, api_call=api_call)
    return response_data