from sqlalchemy import Column, String, DateTime
from sqlalchemy.sql import func
from sqlalchemy.orm import declarative_base

import uuid
from datetime import datetime

from ..settings import SETTINGS

Base = declarative_base()

if SETTINGS.LOG.SAVE_DB and SETTINGS.LOG.FORM.upper() in SETTINGS.LOG.DB_FORM:
    class Logs(Base):
        __tablename__ = SETTINGS.LOG.TABLE

        log_id     = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()), index=True, unique=True)
        app_module = Column(String, default=SETTINGS.BASE.APP_NAME)
        log_level  = Column(String)
        log_module = Column(String)
        log_func   = Column(String)
        log_msg    = Column(String)
        log_at     = Column(DateTime(timezone=True), server_default=func.now(), default=datetime.now())