from fastapi import APIRouter
from ..settings import SETTINGS

from .request  import router as request_router

from .registry.general import router as general_router
from .registry.user    import router as user_router
from .registry.system  import router as system_router
from .registry.io      import router as io_router

from .vector.system import router as vector_system_router
from .graph.system import router as graph_system_router

from .chatflow import router as chatflow_router
from .share import router as share_router

router = APIRouter()

@router.get("/", status_code=200)
def index():
    """
    For health check

    :return:
    """
    return {"message": "Welcome to api service!"}

api_routers = [
    request_router,
    chatflow_router,
    share_router,
    general_router,
    user_router,
    system_router,
    io_router,
    vector_system_router,
    graph_system_router
]

for api_router in api_routers:
    router.include_router(api_router, prefix=SETTINGS.BASE.ENDPOINT_PREFIX)