from fastapi import APIRouter, Depends, status

from ..settings import SETTINGS
from ..utils import router_response_handler

from ..schemas.utils  import BlobShareServiceRequest, BlobShareServiceResponse
from ..services.utils import AzureStorageServiceManager

router = APIRouter(tags=["Share"])

if SETTINGS.BASE.APP_API == True:
    default_api_call = True
else:
    default_api_call = False

@router.post("/share/azure_blob_object", include_in_schema=not(SETTINGS.BASE.APP_PRODUCTION), status_code=status.HTTP_200_OK, response_model=BlobShareServiceResponse)
def share_azure_blob_object(request: BlobShareServiceRequest) -> BlobShareServiceResponse:
    response_data, response = AzureStorageServiceManager().generate_share_blob(request=request)
    router_response_handler(response=response, api_call=default_api_call)
    return response_data