"""
    Graph Data Manager - Azure Cosmos DB Gremlin
"""

"""
    Custom Config
"""
from typing import get_type_hints

from ....settings import SETTINGS
from ..schemas import graph as schema

NODE_LABEL             = "node_type"
NODE_UID               = "node_id"
NODE_STATUS            = "node_status"
NODE_VERSION           = "node_version"
NODE_OBJECT            = "Node"

EDGE_LABEL             = "edge_type"
EDGE_SOURCE_ID         = "source_node_id"
EDGE_TARGET_ID         = "target_node_id"
EDGE_UID               = "edge_id"
EDGE_STATUS            = "edge_status"
EDGE_VERSION           = "edge_version"
EDGE_OBJECT            = "Edge"

GRAPH_PARTITION_KEY   = SETTINGS.GPDB.PARTITION_KEY

SORTING_MAPPER = {
    "asc":  "incr",
    "desc": "decr"
}

NodeCreate             = schema.NodeCreate
EdgeCreate             = schema.EdgeCreate

DataCreate             = schema.GraphCreate
CreateRequest          = schema.GraphCreateRequest
BatchCreateRequest     = schema.GraphBatchCreateRequest

NodeUpdate             = schema.NodeUpdate
EdgeUpdate             = schema.EdgeUpdate
UpdateRequest          = schema.GraphUpdateRequest
CommonRequest          = schema.GraphRequest
BatchCommonRequest     = schema.GraphBatchRequest

SystemDataRequest      = schema.SystemGraphRequest
SystemDataResponse     = schema.SystemGraphResponse

NodeFilter             = schema.NodeFilter
EdgeFilter             = schema.EdgeFilter
DataFilter             = schema.GraphFilter
NodeStringFilter       = schema.NodeStringFilter
NodeNumericFilter      = schema.NodeNumericFilter
EdgeStringFilter       = schema.EdgeStringFilter
EdgeNumericFilter      = schema.EdgeNumericFilter
FilterConditions       = schema.FilterConditions

AdjacentNode           = schema.AdjacentNode
AdjacentNodeFilter     = schema.AdjacentNodeFilter
AdjacentNodeRequest    = schema.AdjacentNodeRequest
AdjacentNodeResponse   = schema.AdjacentNodeResponse

SecretNode             = schema.SecretNode
SecretEdge             = schema.SecretEdge
SecretData             = schema.SecretGraph

DataImportRequest      = schema.GraphImportRequest
DataBackupRequest      = schema.GraphBackupRequest
DataExportRequest      = schema.GraphExportRequest
DataBackupListRequest  = schema.GraphBackupListRequest
DataBackupListResponse = schema.GraphBackupListResponse
DBRestoreRequest       = schema.GraphRestoreRequest

UserDataRequest        = schema.UserGraphRequest
UserDataResponse       = schema.UserGraphResponse
UserNode               = schema.Node
UserEdge               = schema.Edge
UserData               = schema.Graph

DEFAULT_BACKUP_CONFIG = schema.BackupConfig(
    format=SETTINGS.BKUP.FORM,
    location=SETTINGS.BKUP.LOCA,
    name=SETTINGS.BKUP.NAME,
    host=SETTINGS.BKUP.HOST,
    port=SETTINGS.BKUP.PORT,
    user=SETTINGS.BKUP.USER,
    pswd=SETTINGS.BKUP.PSWD,
    table=SETTINGS.BKUP.TABLE,
    rdir=SETTINGS.BKUP.RDIR,
    sdir=SETTINGS.BKUP.SDIR,
    limit=SETTINGS.BKUP.LIMIT
)

DEFAULT_EXPORT_CONFIG = schema.IOConfig(
    format=SETTINGS.EXPT.FORM,
    location=SETTINGS.EXPT.LOCA,
    name=SETTINGS.EXPT.NAME,
    host=SETTINGS.EXPT.HOST,
    port=SETTINGS.EXPT.PORT,
    user=SETTINGS.EXPT.USER,
    pswd=SETTINGS.EXPT.PSWD,
    table=SETTINGS.EXPT.TABLE,
    rdir=SETTINGS.EXPT.RDIR,
    sdir=SETTINGS.EXPT.SDIR,
    file_rdir=SETTINGS.EXPT.FILE_RDIR,
    file_sdir=SETTINGS.EXPT.FILE_SDIR,
    file_name=SETTINGS.EXPT.FILE_NAME
)


from ..connections.graph_connection import (
    get_gb_func,
    get_gb_api,
    create_gb_client
)

# API DB Session
if SETTINGS.BASE.APP_API == True:
    gb_api = get_gb_api
    default_api_call = True
else:
    gb_api = None
    default_api_call = False

# Function DB Session
if SETTINGS.BASE.APP_FUNC == True:
    gb_func = get_gb_func
else:
    gb_func = None

def value_converter_to_db(value):
    if isinstance(value, datetime):
        return value.isoformat()
    elif isinstance(value, list):
        return str(value)
    else:
        return value

def value_converter_to_op(data, model):
    if isinstance(data, dict):
        for key, value in data.items():
            field_lists = get_type_hints(model).get(key)
            if hasattr(field_lists, '__args__') and field_lists and hasattr(field_lists, '__origin__') and field_lists.__args__[0].__origin__ is list:
                if isinstance(value, str):
                    data[key] = eval(value)
    return data

"""
    End of Custom Config
"""

from typing import Callable, Any
from datetime import datetime, timezone
import uuid
import inspect
import os
import time

import json
import csv
import httpx

import asyncio
import sys

if sys.platform == "win32":
    asyncio.set_event_loop_policy(asyncio.WindowsSelectorEventLoopPolicy())

from azure.cosmos import PartitionKey, exceptions

from ..schemas.format import (
    ResponseFormatter,
    Response,
    ComplexEncoder
)

if SETTINGS.BASE.APP_ENCRYPTION == True:
    from ....security.services.crypto_service import CryptoServiceManager

from ....logger.log_handler import get_logger

logger = get_logger(__name__)


class DataManager:
    response_format = ResponseFormatter(suffix = f"{inspect.stack()[0][3]}")

    def __init__(
            self, 
            api_call:       bool = default_api_call,
            gb_api:         Any | None = gb_api,
            gb_func:        Any | None = gb_func,
            graph_storage:  str='COSMOS', 
            graph_location: str='azure', 
            graph_config:   dict={},
        ):
        self.api_call       = api_call
        self.gb_api         = gb_api
        self.gb_func        = gb_func
        self.graph_storage  = graph_storage
        self.graph_location = graph_location
        self.graph_config   = graph_config

    """
        General Operation
    """
    def partition_formatter(self, name: str) -> str:
        formated_name = name.replace(' ', '_')
        formated_name = formated_name.replace('.', '_')
        return f'{SETTINGS.GPDB.CONTAINER_PREFIX}{formated_name}'

    def check_if_container_exists(self, container_name: str='', partition_key: str='') -> tuple[str, Response]:
        matched_container_name = ""
        if container_name:
            try:
                gb_client = create_gb_client().get_container_client(container_name)
                response = Response(status_code=200, detail=self.response_format.ok(f"Found Container <container_name: {container_name}>"))
                logger.info(response.detail)
                matched_container_name = container_name
                return matched_container_name, response
                
            except Exception as e:
                if 'not found' in str(e).lower():
                    response = Response(status_code=404, detail=self.response_format.error(f"Unfound Container <container_name: {container_name}>"))
                    logger.error(response.detail)
                else:
                    response = Response(status_code=500, detail=self.response_format.error(f"Reading Container Failed : <{SETTINGS.BASE.APP_NAME}> Failed to Read Container for Graph DB"))
                    logger.error(response.detail)
                    return matched_container_name, response
                
        elif not matched_container_name and partition_key:
            container_name = self.partition_formatter(partition_key)
            try:
                gb_client = create_gb_client().get_container_client(container_name)
                response = Response(status_code=200, detail=self.response_format.ok(f"Found Container <container_name: {container_name}>"))
                logger.info(response.detail)
                matched_container_name = container_name
                return matched_container_name, response
            
            except Exception as e:
                if 'not found' in str(e).lower():
                    response = Response(status_code=404, detail=self.response_format.error(f"Unfound Container <container_name: {container_name}>"))
                    logger.error(response.detail)
                else:
                    response = Response(status_code=500, detail=self.response_format.error(f"Reading Container Failed : <{SETTINGS.BASE.APP_NAME}> Failed to Read Container for Graph DB"))
                    logger.error(response.detail)
                    return matched_container_name, response

        else:
            response = Response(status_code=404, detail=self.response_format.error(f"Unfound Container Name / Knowledge ID : <{SETTINGS.BASE.APP_NAME}> Found Empty Container Name / Knowledge ID"))

        return matched_container_name, response

    def check_or_create_container(self, container_name: str) -> Response:
        try:
            gb_client = create_gb_client().create_container_if_not_exists(
                id=container_name,
                partition_key=PartitionKey(path=f"/{GRAPH_PARTITION_KEY}"),
                offer_throughput=SETTINGS.GPDB.CONTAINER_THROUGHPUT
            )
            return Response(status_code=201, detail=self.response_format.ok(f"Successfully Initiated Container <container_name: {container_name}>"))
        except:
            response = Response(status_code=500, detail=self.response_format.error(f"Container Creation Failed : <{SETTINGS.BASE.APP_NAME}> Failed to Create Container for Graph DB"))
            return response

    def drop_container(self, container_name: str) -> Response:
        try:
            gb_client = create_gb_client()
            gb_client.get_container_client(container_name)
            gb_client.delete_container(container_name)
            response = Response(status_code=200, detail=self.response_format.ok(f"Graph Cotaniner <{container_name}> Dropped Successfully"))
            logger.info(response.detail)
        
        except exceptions.CosmosResourceNotFoundError:
            response = Response(status_code=200, detail=self.response_format.ok(f"Graph Cotaniner <{container_name}> Does Not Exist. No Actions have been Taken"))
            logger.info(response.detail)
        
        return response

    def add_node_formatter(self, node: NodeCreate) -> tuple[str, dict]:
        property_list = []
        message  = ""
        bindings = dict()
    
        node_dict  = node.__dict__
        node_label = node_dict.pop(NODE_LABEL, "unknown")

        for _key, _value in node_dict.items():
            prop_key = f"prop_{_key}"
            property_list.append(f".property(\'{_key}\', {prop_key})")
            bindings[prop_key] = value_converter_to_db(_value)

        property_list.append(f".property(\'id\', prop_{NODE_UID})")

        message = f"g.addV(\'{node_label}\')" + "".join(property_list)
        
        return message, bindings
        
    def add_edge_formatter(self, edge: EdgeCreate) -> tuple[str, dict]:
        property_list = []
        message  = ""
        bindings = dict()   

        edge_dict  = edge.__dict__
        edge_label = edge_dict.pop(EDGE_LABEL, "unknown")
        partition_key = edge_dict.get(GRAPH_PARTITION_KEY, None)

        if not partition_key:
            response = Response(status_code=404, detail=self.response_format.error("Partition Key is Not Found for Operation"))
            logger.error(response.detail)
            raise Exception(response.detail)

        for _key, _value in edge_dict.items():
            prop_key = f"prop_{_key}"
            property_list.append(f".property(\'{_key}\', {prop_key})")
            bindings[prop_key] = value_converter_to_db(_value)

        property_list.append(f".property(\'id\', prop_{EDGE_UID})")

        message = (
            f"g.V([prop_partition_key, prop_{EDGE_SOURCE_ID}])"
            f".addE(prop_{EDGE_LABEL})"
            f".to(g.V([prop_partition_key, prop_{EDGE_TARGET_ID}]))"
        )
        message += "".join(property_list)
        
        bindings.update(
            {
                f"prop_partition_key":    partition_key,
                f"prop_{EDGE_SOURCE_ID}": getattr(edge, EDGE_SOURCE_ID),
                f"prop_{EDGE_TARGET_ID}": getattr(edge, EDGE_TARGET_ID),
                f"prop_{EDGE_LABEL}":     edge_label
            }
        )
        return message, bindings

    def update_node_formatter(self, node_id: str, partition_key: str, node: NodeUpdate) -> tuple[str, dict]:
        property_list = []
        message  = ""
        bindings = dict()
    
        node_dict  = {key: value for key, value in node.__dict__.items() if value is not None}
        _node_id   = node_dict.pop(NODE_UID, None)
        node_label = node_dict.pop(NODE_LABEL, None)

        if not node_dict and not node_label:
            return message, bindings

        bindings["prop_partition_key"] = partition_key

        for _key, _value in node_dict.items():
            prop_key = f"prop_{_key}"
            property_list.append(f".property(\'{_key}\', {prop_key})")
            bindings[prop_key] = value_converter_to_db(_value)

        message = f"g.V([prop_partition_key, prop_{NODE_UID}])" + "".join(property_list)
        bindings[f"prop_{NODE_UID}"] = node_id

        if node_label:
            message += f".sideEffect(__.property(\'label\', prop_{NODE_LABEL}))"
            bindings[f"prop_{NODE_LABEL}"] = node_label

        # message += '.next()'
        
        return message, bindings


    def update_edge_formatter(self, edge_id: str, partition_key: str, edge: EdgeUpdate) -> tuple[str, dict]:
        property_list = []
        message  = ""
        bindings = dict()
    
        edge_dict  = {key: value for key, value in edge.__dict__.items() if value is not None}
        _edge_id   = edge_dict.pop(EDGE_UID, None)
        edge_label = edge_dict.pop(EDGE_LABEL, None)

        if not edge_dict and not edge_label:
            return message, bindings
        
        bindings["prop_partition_key"] = partition_key

        for _key, _value in edge_dict.items():
            prop_key = f"prop_{_key}"
            property_list.append(f".property(\'{_key}\', {prop_key})")
            bindings[prop_key] = value_converter_to_db(_value)

        message = f"g.E(prop_{EDGE_UID}).has(\'{GRAPH_PARTITION_KEY}\', prop_partition_key)" + "".join(property_list)
        bindings[f"prop_{EDGE_UID}"] = edge_id

        if edge_label:
            message += f".sideEffect(__.property(\'label\', prop_{EDGE_LABEL}))"
            bindings[f"prop_{EDGE_LABEL}"] = edge_label

        # message += '.next()'
        return message, bindings
    
    def drop_node_formatter(self, node_ids: list[str], partition_key: str) -> tuple[str, dict]:
        bindings = dict()
        
        bindings["prop_partition_key"] = partition_key

        id_message = ', '.join([f"ID{i}" for i in range(len(node_ids))])
        message    = f"g.V({id_message}).has(\'{GRAPH_PARTITION_KEY}\', prop_partition_key).drop()"
        bindings   = {f"ID{i}": _id for i, _id in enumerate(node_ids)}
        
        return message, bindings

    def drop_edge_formatter(self, edge_ids: list[str], partition_key: str) -> tuple[str, dict]:
        bindings = dict()

        bindings["prop_partition_key"] = partition_key

        id_message = ', '.join([f"ID{i}" for i in range(len(edge_ids))])
        message    = f"g.E({id_message}).has(\'{GRAPH_PARTITION_KEY}\', prop_partition_key).drop()"
        bindings   = {f"ID{i}": _id for i, _id in enumerate(edge_ids)}
        
        return message, bindings

    # Create
    def create(self, request: CreateRequest) -> Response:
        
        if not request.data or (not request.data.nodes and not request.data.edges):
            response = Response(status_code=404, detail=self.response_format.error(f"Data Unfound Error : <{SETTINGS.BASE.APP_NAME}> Found Empty Data during Registering Graph"))
            return response
        
        if request.container_name:
            container_name = request.container_name
        elif request.data.nodes:
            container_name = self.partition_formatter(str(request.data.nodes[0].knowledge_id))
        else:
            response = Response(status_code=404, detail=self.response_format.error(f"Container Unfound Error : <{SETTINGS.BASE.APP_NAME}> Found Empty Container Name during Registering Graph"))
            return response

        response = self.check_or_create_container(container_name)
        if response.status_code >= SETTINGS.STAT.SUCC_CODE_END:
            return response

        try:
            if self.api_call == True:
                with self.gb_api(container_name) as gremlin_client:
                    for node in request.data.nodes:
                        message, bindings = self.add_node_formatter(node)
                        gremlin_client.submit(
                            message=message,
                            bindings=bindings,
                        )
                        time.sleep(SETTINGS.GPDB.CREATE_WAIT_SEC)
            else:
                with self.gb_func(container_name) as gremlin_client:
                    for node in request.data.nodes:
                        message, bindings = self.add_node_formatter(node)
                        gremlin_client.submit(
                            message=message,
                            bindings=bindings,
                        )
                        time.sleep(SETTINGS.GPDB.CREATE_WAIT_SEC)

        # Handle common exceptions that might occur
        except (BaseException, Exception) as e:
            response = Response(status_code=500, detail=self.response_format.error(f"Common Error : Registering Node <node_id: {node.node_id}>", str(e)))
            logger.error(response.detail)

        # Handle any other exceptions that might occur
        except:
            response = Response(status_code=500, detail=self.response_format.error(f"Unexpected Error : Registering Node <node_id: {node.node_id}>"))
            logger.error(response.detail)

        try:
            if self.api_call == True:
                with self.gb_api(container_name) as gremlin_client:
                    for edge in request.data.edges:
                        message, bindings = self.add_edge_formatter(edge)
                        gremlin_client.submit(
                            message=message,
                            bindings=bindings,
                        )
                        time.sleep(SETTINGS.GPDB.CREATE_WAIT_SEC)
            else:
                with self.gb_func(container_name) as gremlin_client:
                    for edge in request.data.edges:
                        message, bindings = self.add_edge_formatter(edge)
                        gremlin_client.submit(
                            message=message,
                            bindings=bindings,
                        )
                        time.sleep(SETTINGS.GPDB.CREATE_WAIT_SEC)

        # Handle common exceptions that might occur
        except (BaseException, Exception) as e:
            response = Response(status_code=500, detail=self.response_format.error(f"Common Error : Registering Node <node_id: {node.node_id}>", str(e)))
            logger.error(response.detail)

        # Handle any other exceptions that might occur
        except:
            response = Response(status_code=500, detail=self.response_format.error(f"Unexpected Error : Registering Node <node_id: {node.node_id}>"))
            logger.error(response.detail)        

        response = Response(status_code=201, detail=self.response_format.ok(f"Success : Registered Relationship <container_name: {container_name}>"))
        logger.info(response.detail)

        return response
    
    def batch_create(self, request: BatchCreateRequest) -> Response:
        for i, request in enumerate(request.create_requests, start=1):
            logger.info(f"Processing : <{SETTINGS.BASE.APP_NAME}> Batch Registering <{i} / {len(request.create_requests)}> Graph")
            response = self.create(request=request)
            if response.status_code >= SETTINGS.STAT.SUCC_CODE_END:
                logger.error(f"Failed in Registering <{i} / {len(request.create_requests)}> Graph")
                return response
        return response

    def update(self, request: UpdateRequest) -> Response:
        update_node_ids      = []
        update_node_messages = []
        update_node_bindings = []
        add_node_messages    = []
        add_node_bindings    = []

        update_edge_ids      = []
        update_edge_messages = []
        update_edge_bindings = []
        add_edge_messages    = []
        add_edge_bindings    = []

        if not request.update_data:
            response = Response(status_code=404, detail=self.response_format.error(f"Update Error : <{SETTINGS.BASE.APP_NAME}> Found Invalid Update Input"))
            return response

        # Verify Container Exists
        container_name, response = self.check_if_container_exists(
            container_name=request.container_name, 
            partition_key=request.partition_key
        )
        if response.status_code >= SETTINGS.STAT.SUCC_CODE_END:
            response = Response(status_code=404, detail=self.response_format.error(f"Update Error : <{SETTINGS.BASE.APP_NAME}> Cannot Find Valid Container"))
            return response
        
        # Update Node Parsing
        for update_data in request.update_data:
            if update_data.node_id and update_data.node:
                update_node = update_data.node

                # Search for Existing Node
                data_filter = DataFilter(
                    node_filter=NodeFilter(
                        string_filter=NodeStringFilter(**{f"{NODE_UID}_filter": [getattr(update_data, NODE_UID)]})
                    ),
                    partition_keys=[request.partition_key]
                )
                
                filter_conditions      = self.filter_formatter(data_filter=data_filter)
                secret_graph, response = self.query_with_conditions(container_name=container_name, filter_conditions=filter_conditions)

                if not secret_graph.nodes:
                    response = Response(status_code=404, detail=self.response_format.error(f"Unfound Error : Updating {NODE_OBJECT} <{NODE_UID}: {getattr(update_data, NODE_UID)}>"))
                    logger.error(response.detail)
                    return response

                # Update Version
                if NODE_VERSION not in update_node.__dict__.keys() or not getattr(update_node, NODE_VERSION):
                    update_node.__dict__.update(**{NODE_VERSION: getattr(secret_graph.nodes[0], NODE_VERSION)+1})
                update_node.updated_at = datetime.now(timezone.utc)
                
                # Format Update Message
                _update_node_message, _update_node_bindings = self.update_node_formatter(
                    node_id=secret_graph.nodes[0].node_id, 
                    partition_key=request.partition_key,
                    node=update_node
                )

                if not _update_node_message:
                    response = Response(status_code=404, detail=self.response_format.error(f"Unfound Error : Invalid Update {NODE_OBJECT}"))
                    logger.error(response.detail)
                    return response
                else:
                    update_node_ids.append(getattr(update_data, NODE_UID))
                    update_node_messages.append(_update_node_message)
                    update_node_bindings.append(_update_node_bindings)

                # Perserve Old Node Property if Requested
                if request.overwrite == False:
                    old_node = NodeCreate(**secret_graph.nodes[0].__dict__)
                    old_node.__dict__.update(
                        **{
                            NODE_UID:    str(uuid.uuid4()),
                            NODE_STATUS: 0
                        }
                    )
                    _add_node_message, _add_node_bindings = self.add_node_formatter(node=old_node)
                    add_node_messages.append(_add_node_message)
                    add_node_bindings.append(_add_node_bindings)

        # Update Edge Parsing
        for update_data in request.update_data:
            if update_data.edge_id and update_data.edge:
                update_edge = update_data.edge

                # Search for Existing Node
                data_filter = DataFilter(
                    edge_filter=EdgeFilter(
                        string_filter=EdgeStringFilter(**{f"{EDGE_UID}_filter": [getattr(update_data, EDGE_UID)]})
                    ),
                    partition_keys=[request.partition_key]
                )
                
                filter_conditions      = self.filter_formatter(data_filter=data_filter)
                secret_graph, response = self.query_with_conditions(container_name=container_name, filter_conditions=filter_conditions)

                if not secret_graph.edges:
                    response = Response(status_code=404, detail=self.response_format.error(f"Unfound Error : Updating {EDGE_OBJECT} <{EDGE_UID}: {getattr(update_data, EDGE_UID)}>"))
                    logger.error(response.detail)
                    return response

                # Update Version
                if EDGE_VERSION not in update_edge.__dict__.keys() or not getattr(update_edge, EDGE_VERSION):
                    update_edge.__dict__.update(**{EDGE_VERSION: getattr(secret_graph.edges[0], EDGE_VERSION)+1})
                update_edge.updated_at = datetime.now(timezone.utc)
                
                # Format Update Message
                _update_edge_message, _update_edge_bindings = self.update_edge_formatter(
                    edge_id=secret_graph.edges[0].edge_id, 
                    partition_key=request.partition_key,
                    edge=update_edge
                )

                if not _update_edge_message:
                    response = Response(status_code=404, detail=self.response_format.error(f"Unfound Error : Invalid Update {EDGE_OBJECT}"))
                    logger.error(response.detail)
                    return response
                else:
                    update_edge_ids.append(getattr(update_data, EDGE_UID))
                    update_edge_messages.append(_update_edge_message)
                    update_edge_bindings.append(_update_edge_bindings)

                # Perserve Old Edge Property if Requested
                if request.overwrite == False:
                    old_edge = EdgeCreate(**secret_graph.edges[0].__dict__)
                    old_edge.__dict__.update(
                        **{
                            EDGE_UID:    str(uuid.uuid4()),
                            EDGE_STATUS: 0
                        }
                    )
                    _add_edge_message, _add_edge_bindings = self.add_edge_formatter(edge=old_edge)
                    add_edge_messages.append(_add_edge_message)
                    add_edge_bindings.append(_add_edge_bindings)

        # Update Node
        try:
            if self.api_call == True:
                with self.gb_api(container_name) as gremlin_client:
                    for _update_node_id, _update_node_message, _update_node_bindings in zip(update_node_ids, update_node_messages, update_node_bindings): 
                        gremlin_client.submit(
                            message=_update_node_message,
                            bindings=_update_node_bindings,
                        )
                        time.sleep(SETTINGS.GPDB.CREATE_WAIT_SEC)
                        if request.overwrite == False:
                            for _add_node_message, _add_node_bindings in zip(add_node_messages, add_node_bindings):
                                gremlin_client.submit(
                                    message=_add_node_message,
                                    bindings=_add_node_bindings,
                                )
                        response = Response(status_code=200, detail=self.response_format.ok(f"Success : Updated {NODE_OBJECT} <{NODE_UID}: {_update_node_id}>"))
                        logger.info(response.detail)

            else:
                with self.gb_api(container_name) as gremlin_client:
                    for _update_node_id, _update_node_message, _update_node_bindings in zip(update_node_ids, update_node_messages, update_node_bindings): 
                        gremlin_client.submit(
                            message=_update_node_message,
                            bindings=_update_node_bindings,
                        )
                        time.sleep(SETTINGS.GPDB.CREATE_WAIT_SEC)
                        if request.overwrite == False:
                            for _add_node_message, _add_node_bindings in zip(add_node_messages, add_node_bindings):
                                gremlin_client.submit(
                                    message=_add_node_message,
                                    bindings=_add_node_bindings,
                                )

                        response = Response(status_code=200, detail=self.response_format.ok(f"Success : Updated {NODE_OBJECT} <{NODE_UID}: {_update_node_id}>"))
                        logger.info(response.detail)

        # Handle common exceptions that might occur
        except (BaseException, Exception) as e:
            response = Response(status_code=500, detail=self.response_format.error(f"Common Error : Updating {NODE_OBJECT} <{NODE_UID}: {_update_node_id}>", str(e)))
            logger.error(response.detail)

        # Handle any other exceptions that might occur
        except:
            response = Response(status_code=500, detail=self.response_format.error(f"Unexpected Error : Updating {NODE_OBJECT} <{NODE_UID}: {_update_node_id}>"))
            logger.error(response.detail)

        # Update Edge
        try:
            if self.api_call == True:
                with self.gb_api(container_name) as gremlin_client:
                    for _update_edge_id, _update_edge_message, _update_edge_bindings in zip(update_edge_ids, update_edge_messages, update_edge_bindings): 
                        gremlin_client.submit(
                            message=_update_edge_message,
                            bindings=_update_edge_bindings,
                        )
                        time.sleep(SETTINGS.GPDB.CREATE_WAIT_SEC)
                        if request.overwrite == False:
                            for _add_edge_message, _add_edge_bindings in zip(add_edge_messages, add_edge_bindings):
                                gremlin_client.submit(
                                    message=_add_edge_message,
                                    bindings=_add_edge_bindings,
                                )
                        response = Response(status_code=200, detail=self.response_format.ok(f"Success : Updated {EDGE_OBJECT} <{EDGE_UID}: {_update_edge_id}>"))
                        logger.info(response.detail)

            else:
                with self.gb_api(container_name) as gremlin_client:
                    for _update_edge_id, _update_edge_message, _update_edge_bindings in zip(update_edge_ids, update_edge_messages, update_edge_bindings): 
                        gremlin_client.submit(
                            message=_update_edge_message,
                            bindings=_update_edge_bindings,
                        )
                        time.sleep(SETTINGS.GPDB.CREATE_WAIT_SEC)
                        if request.overwrite == False:
                            for _add_edge_message, _add_edge_bindings in zip(add_edge_messages, add_edge_bindings):
                                gremlin_client.submit(
                                    message=_add_edge_message,
                                    bindings=_add_edge_bindings,
                                )

                        response = Response(status_code=200, detail=self.response_format.ok(f"Success : Updated {EDGE_OBJECT} <{EDGE_UID}: {_update_edge_id}>"))
                        logger.info(response.detail)

        # Handle common exceptions that might occur
        except (BaseException, Exception) as e:
            response = Response(status_code=500, detail=self.response_format.error(f"Common Error : Updating {EDGE_OBJECT} <{EDGE_UID}: {_update_edge_id}>", str(e)))
            logger.error(response.detail)

        # Handle any other exceptions that might occur
        except:
            response = Response(status_code=500, detail=self.response_format.error(f"Unexpected Error : Updating {EDGE_OBJECT} <{EDGE_UID}: {_update_edge_id}>"))
            logger.error(response.detail)

        response = Response(status_code=200, detail=self.response_format.ok(f"Update Completed : <{SETTINGS.BASE.APP_NAME}> Completed Update Node/Edge"))
        logger.info(response.detail)

        return response

    def activate(self, request: CommonRequest) -> Response:
        update_node_message  = ""
        update_node_bindings = dict()
        update_edge_message  = ""
        update_edge_bindings = dict()

        if not request.node_id and not request.edge_id:
            response = Response(status_code=404, detail=self.response_format.error(f"Activation Error : <{SETTINGS.BASE.APP_NAME}> Found Invalid Activation Input"))
            return response

        # Verify Container Exists
        container_name, response = self.check_if_container_exists(
            container_name=request.container_name, 
            partition_key=request.partition_key
        )
        if response.status_code >= SETTINGS.STAT.SUCC_CODE_END:
            response = Response(status_code=404, detail=self.response_format.error(f"Activation Error : <{SETTINGS.BASE.APP_NAME}> Cannot Find Valid Container"))
            return response

        # Update Node
        if request.node_id:

            # Search for Existing Node
            data_filter = DataFilter(
                node_filter=NodeFilter(
                    string_filter=NodeStringFilter(**{f"{NODE_UID}_filter": [getattr(request, NODE_UID)]})
                    # numeric_filter=NodeNumericFilter(**{f"{NODE_STATUS}_max": 0, f"{NODE_STATUS}_min": 0})
                ),
                partition_keys=[request.partition_key]
            )
            
            filter_conditions      = self.filter_formatter(data_filter=data_filter)
            secret_graph, response = self.query_with_conditions(container_name=container_name, filter_conditions=filter_conditions)

            if not secret_graph.nodes:
                response = Response(status_code=404, detail=self.response_format.error(f"Unfound Error : Activating {NODE_OBJECT} <{NODE_UID}: {getattr(request, NODE_UID)}>"))
                logger.error(response.detail)
                return response
            
            else:
                node = NodeUpdate(node_status=0, updated_at=datetime.now(timezone.utc))
                update_node_message, update_node_bindings = self.update_node_formatter(
                    node_id=request.node_id, 
                    partition_key=request.partition_key,
                    node=node
                )

        if request.edge_id:
            # Search for Existing Edge
            data_filter = DataFilter(
                edge_filter=EdgeFilter(
                    string_filter=EdgeStringFilter(**{f"{EDGE_UID}_filter": [getattr(request, EDGE_UID)]})
                    # numeric_filter=EdgeNumericFilter(**{f"{EDGE_STATUS}_max": 0, f"{EDGE_STATUS}_min": 0})
                ),
                partition_keys=[request.partition_key]
            )
            
            filter_conditions      = self.filter_formatter(data_filter=data_filter)
            secret_graph, response = self.query_with_conditions(container_name=container_name, filter_conditions=filter_conditions)

            if not secret_graph.edges:
                response = Response(status_code=404, detail=self.response_format.error(f"Unfound Error : Activating {EDGE_OBJECT} <{EDGE_UID}: {getattr(request, EDGE_UID)}>"))
                logger.error(response.detail)
                return response

            else:
                edge = EdgeUpdate(edge_status=0, updated_at=datetime.now(timezone.utc))
                update_edge_message, update_edge_bindings = self.update_edge_formatter(
                    edge_id=request.edge_id, 
                    partition_key=request.partition_key,
                    edge=edge
                )

        if update_node_message:
            try:
                if self.api_call == True:
                    with self.gb_api(container_name) as gremlin_client:
                        gremlin_client.submit(
                            message=update_node_message,
                            bindings=update_node_bindings,
                        )
                        time.sleep(SETTINGS.GPDB.CREATE_WAIT_SEC)

                else:
                    with self.gb_func(container_name) as gremlin_client:
                        gremlin_client.submit(
                            message=update_node_message,
                            bindings=update_node_bindings,
                        )
                        time.sleep(SETTINGS.GPDB.CREATE_WAIT_SEC)
                
                response = Response(status_code=200, detail=self.response_format.ok(f"Success : Activated {NODE_OBJECT} <{NODE_UID}: {getattr(request, NODE_UID)}>"))
                logger.info(response.detail)

            # Handle common exceptions that might occur
            except (BaseException, Exception) as e:
                response = Response(status_code=500, detail=self.response_format.error(f"Common Error : Activating {NODE_OBJECT} <{NODE_UID}: {getattr(request, NODE_UID)}>", str(e)))
                logger.error(response.detail)
                return response

            # Handle any other exceptions that might occur
            except:
                response = Response(status_code=500, detail=self.response_format.error(f"Unexpected Error : Activating {NODE_OBJECT} <{NODE_UID}: {getattr(request, NODE_UID)}>"))
                logger.error(response.detail)
                return response

        if update_edge_message:
            try:
                if self.api_call == True:
                    with self.gb_api(container_name) as gremlin_client:
                        gremlin_client.submit(
                            message=update_edge_message,
                            bindings=update_edge_bindings,
                        )
                        time.sleep(SETTINGS.GPDB.CREATE_WAIT_SEC)

                else:
                    with self.gb_func(container_name) as gremlin_client:
                        gremlin_client.submit(
                            message=update_edge_message,
                            bindings=update_edge_bindings,
                        )
                        time.sleep(SETTINGS.GPDB.CREATE_WAIT_SEC)
                
                response = Response(status_code=200, detail=self.response_format.ok(f"Success : Activated {EDGE_OBJECT} <{EDGE_UID}: {getattr(request, EDGE_UID)}>"))
                logger.info(response.detail)

            # Handle common exceptions that might occur
            except (BaseException, Exception) as e:
                response = Response(status_code=500, detail=self.response_format.error(f"Common Error : Activating {EDGE_OBJECT} <{EDGE_UID}: {getattr(request, EDGE_UID)}>", str(e)))
                logger.error(response.detail)
                return response

            # Handle any other exceptions that might occur
            except:
                response = Response(status_code=500, detail=self.response_format.error(f"Unexpected Error : Activating {EDGE_OBJECT} <{EDGE_UID}: {getattr(request, EDGE_UID)}>"))
                logger.error(response.detail)
                return response


        response = Response(status_code=200, detail=self.response_format.ok(f"Activation Completed : <{SETTINGS.BASE.APP_NAME}> Completed Activating Node/Edge"))
        logger.info(response.detail)
        return response

    def batch_activate(self, request: BatchCommonRequest) -> Response:
        for i, request in enumerate(request.graph_requests, start=1):
            logger.info(f"Processing : <{SETTINGS.BASE.APP_NAME}> Batch Activating <{i} / {len(request.graph_requests)}> Graph")
            response = self.activate(request=request)
            if response.status_code >= SETTINGS.STAT.SUCC_CODE_END:
                logger.error(f"Failed in Activating <{i} / {len(request.graph_requests)}> Graph")
                return response
        return response

    def deactivate(self, request: CommonRequest) -> Response:
        update_node_message  = ""
        update_node_bindings = dict()
        update_edge_message  = ""
        update_edge_bindings = dict()

        if not request.node_id and not request.edge_id:
            response = Response(status_code=404, detail=self.response_format.error(f"Deactivation Error : <{SETTINGS.BASE.APP_NAME}> Found Invalid Deactivation Input"))
            return response

        # Verify Container Exists
        container_name, response = self.check_if_container_exists(
            container_name=request.container_name, 
            partition_key=request.partition_key
        )
        if response.status_code >= SETTINGS.STAT.SUCC_CODE_END:
            response = Response(status_code=404, detail=self.response_format.error(f"Deactivation Error : <{SETTINGS.BASE.APP_NAME}> Cannot Find Valid Container"))
            return response

        # Update Node
        if request.node_id:

            # Search for Existing Node
            data_filter = DataFilter(
                node_filter=NodeFilter(
                    string_filter=NodeStringFilter(**{f"{NODE_UID}_filter": [getattr(request, NODE_UID)]})
                    # numeric_filter=NodeNumericFilter(**{f"{NODE_STATUS}_min": 1})
                ),
                partition_keys=[request.partition_key]
            )
            
            filter_conditions      = self.filter_formatter(data_filter=data_filter)
            secret_graph, response = self.query_with_conditions(container_name=container_name, filter_conditions=filter_conditions)

            if not secret_graph.nodes:
                response = Response(status_code=404, detail=self.response_format.error(f"Unfound Error : Deactivating {NODE_OBJECT} <{NODE_UID}: {getattr(request, NODE_UID)}>"))
                logger.error(response.detail)
                return response
            
            else:
                node = NodeUpdate(node_status=1, updated_at=datetime.now(timezone.utc))
                update_node_message, update_node_bindings = self.update_node_formatter(
                    node_id=request.node_id, 
                    partition_key=request.partition_key,
                    node=node
                )

        if request.edge_id:
            # Search for Existing Edge
            data_filter = DataFilter(
                edge_filter=EdgeFilter(
                    string_filter=EdgeStringFilter(**{f"{EDGE_UID}_filter": [getattr(request, EDGE_UID)]})
                    # numeric_filter=EdgeNumericFilter(**{f"{EDGE_STATUS}_min": 1})
                ),
                partition_keys=[request.partition_key]
            )
            
            filter_conditions      = self.filter_formatter(data_filter=data_filter)
            secret_graph, response = self.query_with_conditions(container_name=container_name, filter_conditions=filter_conditions)

            if not secret_graph.edges:
                response = Response(status_code=404, detail=self.response_format.error(f"Unfound Error : Deactivating {EDGE_OBJECT} <{EDGE_UID}: {getattr(request, EDGE_UID)}>"))
                logger.error(response.detail)
                return response

            else:
                edge = EdgeUpdate(edge_status=1, updated_at=datetime.now(timezone.utc))
                update_edge_message, update_edge_bindings = self.update_edge_formatter(
                    edge_id=request.edge_id, 
                    partition_key=request.partition_key,
                    edge=edge
                )

        if update_node_message:
            try:
                if self.api_call == True:
                    with self.gb_api(container_name) as gremlin_client:
                        gremlin_client.submit(
                            message=update_node_message,
                            bindings=update_node_bindings,
                        )
                        time.sleep(SETTINGS.GPDB.CREATE_WAIT_SEC)

                else:
                    with self.gb_func(container_name) as gremlin_client:
                        gremlin_client.submit(
                            message=update_node_message,
                            bindings=update_node_bindings,
                        )
                        time.sleep(SETTINGS.GPDB.CREATE_WAIT_SEC)
                
                response = Response(status_code=200, detail=self.response_format.ok(f"Success : Deactivated {NODE_OBJECT} <{NODE_UID}: {getattr(request, NODE_UID)}>"))
                logger.info(response.detail)

            # Handle common exceptions that might occur
            except (BaseException, Exception) as e:
                response = Response(status_code=500, detail=self.response_format.error(f"Common Error : Deactivating {NODE_OBJECT} <{NODE_UID}: {getattr(request, NODE_UID)}>", str(e)))
                logger.error(response.detail)
                return response

            # Handle any other exceptions that might occur
            except:
                response = Response(status_code=500, detail=self.response_format.error(f"Unexpected Error : Deactivating {NODE_OBJECT} <{NODE_UID}: {getattr(request, NODE_UID)}>"))
                logger.error(response.detail)
                return response

        if update_edge_message:
            try:
                if self.api_call == True:
                    with self.gb_api(container_name) as gremlin_client:
                        gremlin_client.submit(
                            message=update_edge_message,
                            bindings=update_edge_bindings,
                        )
                        time.sleep(SETTINGS.GPDB.CREATE_WAIT_SEC)

                else:
                    with self.gb_func(container_name) as gremlin_client:
                        gremlin_client.submit(
                            message=update_edge_message,
                            bindings=update_edge_bindings,
                        )
                        time.sleep(SETTINGS.GPDB.CREATE_WAIT_SEC)
                
                response = Response(status_code=200, detail=self.response_format.ok(f"Success : Deactivated {EDGE_OBJECT} <{EDGE_UID}: {getattr(request, EDGE_UID)}>"))
                logger.info(response.detail)

            # Handle common exceptions that might occur
            except (BaseException, Exception) as e:
                response = Response(status_code=500, detail=self.response_format.error(f"Common Error : Deactivating {EDGE_OBJECT} <{EDGE_UID}: {getattr(request, EDGE_UID)}>", str(e)))
                logger.error(response.detail)
                return response

            # Handle any other exceptions that might occur
            except:
                response = Response(status_code=500, detail=self.response_format.error(f"Unexpected Error : Deactivating {EDGE_OBJECT} <{EDGE_UID}: {getattr(request, EDGE_UID)}>"))
                logger.error(response.detail)
                return response

        response = Response(status_code=200, detail=self.response_format.ok(f"Deactivation Completed : <{SETTINGS.BASE.APP_NAME}> Completed Deactivating Node/Edge"))
        logger.info(response.detail)
        return response

    def batch_deactivate(self, request: BatchCommonRequest) -> Response:
        for i, request in enumerate(request.graph_requests, start=1):
            logger.info(f"Processing : <{SETTINGS.BASE.APP_NAME}> Batch Deactivating <{i} / {len(request.graph_requests)}> Graph")
            response = self.activate(request=request)
            if response.status_code >= SETTINGS.STAT.SUCC_CODE_END:
                logger.error(f"Failed in Deactivating <{i} / {len(request.graph_requests)}> Graph")
                return response
        return response
    
    # Delete
    def delete(self, request: CommonRequest) -> Response:
        response = self.deactivate(request=request)
        return response

    def batch_delete(self, request: BatchCommonRequest) -> Response:
        for i, request in enumerate(request.graph_requests, start=1):
            logger.info(f"Processing : <{SETTINGS.BASE.APP_NAME}> Batch Deleting <{i} / {len(request.graph_requests)}> Graph")
            response = self.activate(request=request)
            if response.status_code >= SETTINGS.STAT.SUCC_CODE_END:
                logger.error(f"Failed in Deleting <{i} / {len(request.graph_requests)}> Graph")
                return response
        return response

    # Drop
    def drop(self, request: CommonRequest) -> Response:
        drop_node_message  = ""
        drop_node_bindings = dict()
        drop_edge_message  = ""
        drop_edge_bindings = dict()

        # Verify Container Exists
        container_name, response = self.check_if_container_exists(
            container_name=request.container_name, 
            partition_key=request.partition_key
        )
        if response.status_code >= SETTINGS.STAT.SUCC_CODE_END:
            response = Response(status_code=404, detail=self.response_format.error(f"Drop Error : <{SETTINGS.BASE.APP_NAME}> Cannot Find Valid Container"))
            return response

        # Drop Edge
        if request.edge_id:
            # Search for Existing Edge
            data_filter = DataFilter(
                edge_filter=EdgeFilter(
                    string_filter=EdgeStringFilter(**{f"{EDGE_UID}_filter": [getattr(request, EDGE_UID)]})
                    # numeric_filter=EdgeNumericFilter(**{f"{EDGE_STATUS}_max": 0, f"{EDGE_STATUS}_min": 0})
                ),
                partition_keys=[request.partition_key]
            )
            
            filter_conditions      = self.filter_formatter(data_filter=data_filter)
            secret_graph, response = self.query_with_conditions(container_name=container_name, filter_conditions=filter_conditions)

            if not secret_graph.edges:
                response = Response(status_code=404, detail=self.response_format.error(f"Unfound Error : Dropping {EDGE_OBJECT} <{EDGE_UID}: {getattr(request, EDGE_UID)}>"))
                logger.error(response.detail)
                return response

            else:
                drop_edge_message, drop_edge_bindings = self.drop_edge_formatter(
                    edge_ids=[request.edge_id],
                    partition_key=request.partition_key
                )

        # Update Node
        if request.node_id:

            # Search for Existing Node
            data_filter = DataFilter(
                node_filter=NodeFilter(
                    string_filter=NodeStringFilter(**{f"{NODE_UID}_filter": [getattr(request, NODE_UID)]})
                    # numeric_filter=NodeNumericFilter(**{f"{NODE_STATUS}_max": 0, f"{NODE_STATUS}_min": 0})
                ),
                partition_keys=[request.partition_key]
            )
            
            filter_conditions      = self.filter_formatter(data_filter=data_filter)
            secret_graph, response = self.query_with_conditions(container_name=container_name, filter_conditions=filter_conditions)

            if not secret_graph.nodes:
                response = Response(status_code=404, detail=self.response_format.error(f"Unfound Error : Dropping {NODE_OBJECT} <{NODE_UID}: {getattr(request, NODE_UID)}>"))
                logger.error(response.detail)
                return response
            
            else:
                drop_node_message, drop_node_bindings = self.drop_node_formatter(
                    node_ids=[request.node_id],
                    partition_key=request.partition_key
                )

        if drop_edge_message:
            try:
                if self.api_call == True:
                    with self.gb_api(container_name) as gremlin_client:
                        gremlin_client.submit(
                            message=drop_edge_message,
                            bindings=drop_edge_bindings,
                        )
                        time.sleep(SETTINGS.GPDB.CREATE_WAIT_SEC)

                else:
                    with self.gb_func(container_name) as gremlin_client:
                        gremlin_client.submit(
                            message=drop_edge_message,
                            bindings=drop_edge_bindings,
                        )
                        time.sleep(SETTINGS.GPDB.CREATE_WAIT_SEC)
                
                response = Response(status_code=200, detail=self.response_format.ok(f"Success : Dropped {EDGE_OBJECT} <{EDGE_UID}: {getattr(request, EDGE_UID)}>"))
                logger.info(response.detail)

            # Handle common exceptions that might occur
            except (BaseException, Exception) as e:
                response = Response(status_code=500, detail=self.response_format.error(f"Common Error : Dropping {EDGE_OBJECT} <{EDGE_UID}: {getattr(request, EDGE_UID)}>", str(e)))
                logger.error(response.detail)
                return response

            # Handle any other exceptions that might occur
            except:
                response = Response(status_code=500, detail=self.response_format.error(f"Unexpected Error : Dropping {EDGE_OBJECT} <{EDGE_UID}: {getattr(request, EDGE_UID)}>"))
                logger.error(response.detail)
                return response

        if drop_node_message:
            try:
                if self.api_call == True:
                    with self.gb_api(container_name) as gremlin_client:
                        gremlin_client.submit(
                            message=drop_node_message,
                            bindings=drop_node_bindings,
                        )
                        time.sleep(SETTINGS.GPDB.CREATE_WAIT_SEC)

                else:
                    with self.gb_func(container_name) as gremlin_client:
                        gremlin_client.submit(
                            message=drop_node_message,
                            bindings=drop_node_bindings,
                        )
                        time.sleep(SETTINGS.GPDB.CREATE_WAIT_SEC)
                
                response = Response(status_code=200, detail=self.response_format.ok(f"Success : Dropped {NODE_OBJECT} <{NODE_UID}: {getattr(request, NODE_UID)}>"))
                logger.info(response.detail)

            # Handle common exceptions that might occur
            except (BaseException, Exception) as e:
                response = Response(status_code=500, detail=self.response_format.error(f"Common Error : Dropping {NODE_OBJECT} <{NODE_UID}: {getattr(request, NODE_UID)}>", str(e)))
                logger.error(response.detail)
                return response

            # Handle any other exceptions that might occur
            except:
                response = Response(status_code=500, detail=self.response_format.error(f"Unexpected Error : Dropping {NODE_OBJECT} <{NODE_UID}: {getattr(request, NODE_UID)}>"))
                logger.error(response.detail)
                return response

        response = Response(status_code=200, detail=self.response_format.ok(f"Drop Completed : <{SETTINGS.BASE.APP_NAME}> Completed Dropping Node/Edge"))
        logger.info(response.detail)
        return response

    def batch_drop(self, request: BatchCommonRequest) -> Response:
        for i, request in enumerate(request.graph_requests, start=1):
            logger.info(f"Processing : <{SETTINGS.BASE.APP_NAME}> Batch Dropping <{i} / {len(request.graph_requests)}> Graph")
            response = self.drop(request=request)
            if response.status_code >= SETTINGS.STAT.SUCC_CODE_END:
                logger.error(f"Failed in Dropping <{i} / {len(request.graph_requests)}> Graph")
                return response
        return response

    def condition_drop_node(self, request: UpdateRequest) -> Response:
        if not request.partition_key:
            response = Response(status_code=404, detail=self.response_format.error(f"Condition Drop Error : <{SETTINGS.BASE.APP_NAME}> Found Invalid Drop Input"))
            return response

        time.sleep(SETTINGS.GPDB.CREATE_WAIT_SEC)
        _response_data, response = self.query_data_by_system(
            SystemDataRequest(
                container_name=request.container_name,
                partition_key=request.partition_key,
                data_filter=DataFilter(
                    node_filter=NodeFilter(
                        numeric_filter=NodeNumericFilter(
                            node_status_min=0
                        ),
                        filter_no=1
                    ),
                    partition_keys=[request.partition_key]
                )
            )
        )

        if response.status_code >= SETTINGS.STAT.SUCC_CODE_END or _response_data.node_count == 0:
            response = Response(status_code=200, detail=self.response_format.info(f"Drop Complete : <{SETTINGS.BASE.APP_NAME}> Cannot Find Matched Container"))
            return response

        message  = f"g.V().has(\'{GRAPH_PARTITION_KEY}\', prop_partition_key).drop()"
        bindings = dict()
        bindings["prop_partition_key"] = request.partition_key

        try:
            if self.api_call == True:
                with self.gb_api(request.container_name) as gremlin_client:
                    gremlin_client.submit(
                        message=message,
                        bindings=bindings,
                    )
                    time.sleep(SETTINGS.GPDB.CREATE_WAIT_SEC)

            else:
                with self.gb_func(request.container_name) as gremlin_client:
                    gremlin_client.submit(
                        message=message,
                        bindings=bindings,
                    )
                    time.sleep(SETTINGS.GPDB.CREATE_WAIT_SEC) 
            
            response = Response(status_code=200, detail=self.response_format.ok(f"Condition Drop Completed : <{SETTINGS.BASE.APP_NAME}> Dropping Node Completed"))
        except:
            response = Response(status_code=500, detail=self.response_format.error(f"Condition Drop Error : <{SETTINGS.BASE.APP_NAME}> Dropping Node Failed"))
        
        return response


    """
        System Operation
    """
    def read_node_by_system(self, container_name: str, partition_key: str, node_id: str) -> tuple[SecretData, Response]:
        response_data = SecretData()
        node_request  = SystemDataRequest(
            container_name  = container_name,
            partition_key   = partition_key,
            data_filter     = DataFilter(
                node_filter = NodeFilter(
                    string_filter = NodeStringFilter(
                        node_id_filter = [node_id]
                    )
                ),
                partition_keys=[partition_key]
            )
        )
        response_graph, response = self.query_data_by_system(request=node_request)
        if response.status_code >= SETTINGS.STAT.SUCC_CODE_END:
            return response
        else:
            response_data.nodes = response_graph.filtered_data.nodes

        if response_data.nodes:
            edge_request = SystemDataRequest(
                container_name  = container_name,
                partition_key   = partition_key,
                data_filter     = DataFilter(
                    edge_filter = EdgeFilter(
                        string_filter = EdgeStringFilter(
                            source_node_id_filter = [node.node_id for node in response_data.nodes],
                            target_node_id_filter = [node.node_id for node in response_data.nodes]
                        )
                    ),
                    partition_keys=[partition_key]
                )
            )
            
            response_graph, response = self.query_data_by_system(request=edge_request)
            if response.status_code >= SETTINGS.STAT.SUCC_CODE_END:
                return response
            else:
                response_data.edges = response_graph.filtered_data.edges
            
        response = Response(status_code=200, detail=self.response_format.ok(f"Read Node Completed : <{SETTINGS.BASE.APP_NAME}> Read Node Completed"))
        return response_data, response

    def read_edge_by_system(self, container_name: str, partition_key: str, edge_id: str) -> tuple[SecretData, Response]:
        response_data = SecretData()
        edge_request = SystemDataRequest(
            container_name  = container_name,
            partition_key   = partition_key,
            data_filter     = DataFilter(
                edge_filter = EdgeFilter(
                    string_filter = EdgeStringFilter(
                        edge_id_filter = [edge_id],
                    )
                ),
                partition_keys=[partition_key]
            )
        )
        
        response_graph, response = self.query_data_by_system(request=edge_request)
        if response.status_code >= SETTINGS.STAT.SUCC_CODE_END:
            return response
        else:
            response_data.edges = response_graph.filtered_data.edges
        
        if response_data.edges:
            node_ids = {edge.source_node_id for edge in response_data.edges}
            node_ids = list(node_ids.update(edge.target_node_id for edge in response_data.edges))

            node_request  = SystemDataRequest(
                container_name  = container_name,
                partition_key   = partition_key,
                data_filter     = DataFilter(
                    node_filter = NodeFilter(
                        string_filter = NodeStringFilter(
                            node_id_filter = node_ids
                        )
                    ),
                    partition_keys=[partition_key]
                )
            )
            response_graph, response = self.query_data_by_system(request=node_request)
            if response.status_code >= SETTINGS.STAT.SUCC_CODE_END:
                return response
            else:
                response_data.nodes = response_graph.filtered_data.nodes
            
        response = Response(status_code=200, detail=self.response_format.ok(f"Read Edge Completed : <{SETTINGS.BASE.APP_NAME}> Read Edge Completed"))
        return response_data, response

    # System Query Data
    def query_data_by_system(self, request: SystemDataRequest) -> tuple[SystemDataResponse, Response]:
        response_data = SystemDataResponse(**request.__dict__)

        filtered_nodes = []
        filtered_edges = []

        # Verify Container Exists
        container_name, response = self.check_if_container_exists(
            container_name=request.container_name, 
            partition_key=request.partition_key
        )
        if response.status_code >= SETTINGS.STAT.SUCC_CODE_END:
            response = Response(status_code=404, detail=self.response_format.error(f"Query Error : <{SETTINGS.BASE.APP_NAME}> Cannot Find Valid Container"))
            return response_data, response

        if request.data_filter.node_filter:
            data_filter = DataFilter(
                node_filter=NodeFilter(**request.data_filter.node_filter.__dict__),
                partition_keys=[request.partition_key]
            )
            
            filter_conditions      = self.filter_formatter(data_filter=data_filter)
            secret_graph, response = self.query_with_conditions(container_name=container_name, filter_conditions=filter_conditions)

            if secret_graph.nodes:
                filtered_nodes = secret_graph.nodes
        
        if request.data_filter.edge_filter:
            data_filter = DataFilter(
                edge_filter=EdgeFilter(**request.data_filter.edge_filter.__dict__),
                partition_keys=[request.partition_key]
            )
            
            filter_conditions      = self.filter_formatter(data_filter=data_filter)
            secret_graph, response = self.query_with_conditions(container_name=container_name, filter_conditions=filter_conditions)
            
            if secret_graph.edges:
                filtered_edges = secret_graph.edges

        output_nodes = [SecretNode(**_data.__dict__) for _data in filtered_nodes]
        output_edges = [SecretEdge(**_data.__dict__) for _data in filtered_edges]

        response_data.__dict__.update(
            filtered_data=SecretData(
                nodes = output_nodes, 
                edges = output_edges
            ),
            node_count = len(output_nodes),
            edge_count = len(output_edges)
        )
        response = Response(status_code=200, detail=self.response_format.ok("Success : Get Filtered Data for System"))
    
        return response_data, response


    # System Export Data
    def export_data_by_system(self, request: DataExportRequest) -> Response:

        data_request = SystemDataRequest(**request.__dict__)
        response_data, response = self.query_data_by_system(request=data_request)

        if not response_data.filtered_data.nodes and not response_data.filtered_data.edges:
            response = Response(status_code=200, detail=self.response_format.ok("Success : No Matched Data for the Given Filter"))
            return response # Return No Data

        response = self.export_data(
            config=request.io_config, 
            data=response_data.filtered_data, 
            include_datetime=request.include_datetime
        )

        return response

    """
        User Operation
    """
    def query_data_by_user(self, request: UserDataRequest) -> tuple[UserDataResponse, Response]:
        response_data = UserDataResponse(**request.__dict__)

        filtered_nodes = []
        filtered_edges = []

        # Verify Container Exists
        container_name, response = self.check_if_container_exists(
            container_name=request.container_name, 
            partition_key=request.partition_key
        )
        if response.status_code >= SETTINGS.STAT.SUCC_CODE_END:
            response = Response(status_code=404, detail=self.response_format.error(f"Query Error : <{SETTINGS.BASE.APP_NAME}> Cannot Find Valid Container"))
            return response_data, response

        if request.data_filter.node_filter:
            data_filter = DataFilter(
                node_filter=NodeFilter(**request.data_filter.node_filter.__dict__),
                partition_keys=[request.partition_key]
            )
            
            filter_conditions      = self.filter_formatter(data_filter=data_filter)
            secret_graph, response = self.query_with_conditions(container_name=container_name, filter_conditions=filter_conditions)

            if secret_graph.nodes:
                filtered_nodes = secret_graph.nodes
        
        if request.data_filter.edge_filter:
            data_filter = DataFilter(
                edge_filter=EdgeFilter(**request.data_filter.edge_filter.__dict__),
                partition_keys=[request.partition_key]
            )
            
            filter_conditions      = self.filter_formatter(data_filter=data_filter)
            secret_graph, response = self.query_with_conditions(container_name=container_name, filter_conditions=filter_conditions)
            
            if secret_graph.edges:
                filtered_edges = secret_graph.edges

        response_data.__dict__.update(
            filtered_data=UserData(
                nodes=[UserNode(**_data.__dict__) for _data in filtered_nodes], 
                edges=[UserEdge(**_data.__dict__) for _data in filtered_edges]
            )
        )        
        response = Response(status_code=200, detail=self.response_format.ok("Success : Get Permitted Data for User"))
       
        return response_data, response


    """
        Class Operation
    """
    # Import Data
    def import_data(self, config: schema.IOConfig | None) -> Response:         
        
        # 1. Detect IO Configuration
        if config is None:
            response = Response(status_code=500, detail=self.response_format.ok("Import Error : Empty Import Configuration"))
            logger.error(response.detail)
            return response

        # 2.1. Init Import File
        if config.format.upper() in SETTINGS.IMPT.FILE_FORM:
            try:
                file_extn = '.' + config.format.lower()
                if file_extn in config.file_name.lower() and len(config.file_name.lower().split(file_extn)) == 2:
                    file_name = config.file_name
                else:
                    file_name = config.file_name + file_extn

                if not config.file_sdir:
                    file_root = config.file_rdir
                else:
                    file_root = os.path.join(config.file_rdir, config.file_sdir)

                file_path = os.path.join(file_root, file_name)

            # Handle missing parameters
            except TypeError as e:
                response = Response(status_code=500, detail=self.response_format.error(f"Missing Parameter Error : Importing Data", str(e)))
                logger.error(response.detail)
                return response

            # Handle any other exceptions that might occur
            except:
                response = Response(status_code=500, detail=self.response_format.error(f"Unexpected Error : Importing Data"))
                logger.error(response.detail)
                return response

            if not os.path.isfile(file_path):
                response = Response(status_code=500, detail=self.response_format.error(f"Import Error : Cannot Find Import File {file_path}"))
                logger.error(response.detail)
                return response
        
        # # 2.2. Init Import DB
        # elif config.format.upper() in SETTINGS.IMPT.DB_FORM:
        #     try:
        #         db_config = IODatabaseConfiguration(**config.__dict__)
        #         engine = init_io_db_engine(db_config) # init engine

        #     except:
        #         response = Response(status_code=500, detail=self.response_format.error(f"Import Error : Failed to Init Import DB <{db_config.name}>"))
        #         logger.error(response.detail)
        #         return response

        # 3. Import Data
        # 3.1. Import to JSON
        if config.format.upper() == 'JSON':
            try:
                with open(file_path, 'r', encoding='utf-8') as json_file:
                    io_data = json.load(json_file)
            
                response = Response(status_code=200, detail=self.response_format.ok(f"Import in Progress : Retrieved Data from <{file_path}>"))
                logger.info(response.detail)

            except:
                response = Response(status_code=500, detail=self.response_format.error(f"Data Import Error : Failed to Retrieve Data from <{file_path}>"))
                logger.error(response.detail)
                return response
            
        # 3.2. Import to CSV
        elif config.format.upper() == 'CSV':
            try:
                with open(file_path, mode='r', newline='', encoding='utf-8-sig') as csv_file:
                    reader = csv.DictReader(csv_file)  # Use DictReader to create dictionaries
                    io_data = [row for row in reader] 

                response = Response(status_code=200, detail=self.response_format.ok(f"Import in Progress : Retrieved Data from <{file_path}>"))
                logger.info(response.detail)

            except:
                response = Response(status_code=500, detail=self.response_format.error(f"Data Import Error : Failed to Retrieve Data from <{file_path}>"))
                logger.error(response.detail)    
                return response
    
        # # 3.3. Import to DB
        # elif config.format.upper() in SETTINGS.IMPT.DB_FORM: # Valid DB
        #     _table_list = sqlalchemy.inspect(engine).get_table_names()
        #     _table_name = [table for table in _table_list if table == config.table]
            
        #     # Check if Table Exists
        #     if not _table_name:
        #         response = Response(status_code=500, detail=self.response_format.error("Data Import Error : Cannot Find the Table in Import DB"))
        #         logger.error(response.detail)
        #         return response
        
        #     # Retrieve Import Data
        #     try:
        #         table_name = _table_name[0]
        #         IODBClass = set_io_db_table(table_name=table_name, engine=engine)
        #         with get_io_db_func(engine=engine) as db:
        #             _io_data = db.query(IODBClass).all()
        #         io_data = [CreateRequest(**_data.__dict__) for _data in _io_data]
        #         response = Response(status_code=200, detail=self.response_format.ok("Import in Progress : Retrieved Data from Import DB"))
        #         logger.info(response.detail)

        #     except:
        #         response = Response(status_code=500, detail=self.response_format.error(f"Unexpected Error : Failed to Get Data from Import DB <{db_config.name}>"))
        #         logger.error(response.detail)
        #         return response

        # 3.X. Unknown Import Format
        else:
            response = Response(status_code=500, detail=self.response_format.error("Data Import Error : Invalid Data Import Format"))
            logger.error(response.detail)       
            return response 

        ### TODO: To be updated
        # 4. Remove Current Data from DB
        # try:
        #     if self.api_call == True:
        #         with self.db_api(collection_name=COLLECTION_NAME):
        #             DBClass.objects().delete()
        #         # db = self.db_api
        #         # db.query(DBClass).delete()
        #         # db.commit()
        #     else:
        #         with self.db_func(collection_name=COLLECTION_NAME) as db:
        #             DBClass.objects().delete()
        #             # db.query(DBClass).delete()
        #             # db.commit()

        #     response = Response(status_code=200, detail=self.response_format.ok("Import in Progress : Removed Current Data from DB"))
        #     logger.info(response.detail)

        # # 4.X. Handle common exceptions that might occur
        # except (BaseException, Exception) as e:
        #     response = Response(status_code=500, detail=self.response_format.error(f"Common Error : Removing Current Data from DB during Importing Data", str(e)))
        #     logger.error(response.detail)
        #     return response

        # # 4.X. Handle any other exceptions that might occur
        # except:
        #     response = Response(status_code=500, detail=self.response_format.error(f"Unexpected Error : Removing Current Data from DB during Importing Data"))
        #     logger.error(response.detail)
        #     return response

        # 5. Add Data to Current DB
        batch_create_request = BatchCreateRequest(
            create_requests=[
                CreateRequest(
                    data=DataCreate(
                        **_data
                        )
                ) for _data in io_data
            ]
        )
        response = self.batch_create(request=batch_create_request)
        if response.status_code < SETTINGS.STAT.SUCC_CODE_END:
            response = Response(status_code=200, detail=self.response_format.ok(f"Success : Imported Data from Import DB <{config.table}>"))
            logger.info(response.detail)
        else:
            response = Response(status_code=500, detail=self.response_format.error(f"Unexpected Error : Importing Data to Current DB"))
            logger.error(response.detail)

        return response

    # Export Data
    def export_data(self, config: schema.IOConfig | None, data: SecretData, include_datetime: bool=True) -> Response:         
        
        # 1. Detect Export Configuration
        if config is None:
            config = DEFAULT_EXPORT_CONFIG.copy()
            response = Response(status_code=200, detail=self.response_format.ok("Info : Empty Export Configuration >>> Use System Default Export Configuration"))
            logger.info(response.detail)

        # 2.1. Init Export File
        if config.format.upper() in SETTINGS.EXPT.FILE_FORM:
            try:
                if include_datetime == True:
                    file_name = config.file_name + '_' + datetime.now(timezone.utc).strftime("%Y-%m-%d-%H-%M-%S")
                file_name = '.'.join([file_name, config.format.lower()])

                if not config.file_sdir:
                    file_root = config.file_rdir
                else:
                    file_root = os.path.join(config.file_rdir, config.file_sdir)

                file_path = os.path.join(file_root, file_name)

            # Handle missing parameters
            except TypeError as e:
                response = Response(status_code=500, detail=self.response_format.error(f"Missing Parameter Error : Exporting Data", str(e)))
                logger.error(response.detail)
                return response

            # Handle any other exceptions that might occur
            except:
                response = Response(status_code=500, detail=self.response_format.error(f"Unexpected Error : Exporting Data"))
                logger.error(response.detail)
                return response

            try:
                os.makedirs(file_root, exist_ok=True)
            except:
                response = Response(status_code=500, detail=self.response_format.error(f"Unexpected Error : Failed to Create Directory when Exporting Data"))
                logger.error(response.detail)
                return response
        
        # # 2.2. Init Export DB
        # elif config.format.upper() in SETTINGS.EXPT.DB_FORM:
        #     try:
        #         if include_datetime == True:
        #             config.table = config.table + '_' + datetime.now(timezone.utc).strftime("%Y-%m-%d-%H-%M-%S")
            
        #     # Handle missing parameters
        #     except TypeError as e:
        #         response = Response(status_code=500, detail=self.response_format.error(f"Missing Parameter Error : Exporting Data", str(e)))
        #         logger.error(response.detail)
        #         return response

        #     # Handle any other exceptions that might occur
        #     except:
        #         response = Response(status_code=500, detail=self.response_format.error(f"Unexpected Error : Exporting Data"))
        #         logger.error(response.detail)
        #         return response
 
        # 2.3. Handle Unknown Format
        else:
            response = Response(status_code=500, detail=self.response_format.error("Data Export Error : Invalid Data Export Format"))
            logger.error(response.detail)       
            return response 

        # 3. Export Data
        # 3.1. Export to JSON
        if config.format.upper() == 'JSON':
            try:
                with open(file_path, 'w', encoding="utf-8") as json_file:
                    json.dump(data, json_file, cls=ComplexEncoder, ensure_ascii=False, indent=4)
            
                response = Response(status_code=200, detail=self.response_format.ok(f"Success : Exported Data to <{file_path}>"))
                logger.info(response.detail)

            except:
                response = Response(status_code=500, detail=self.response_format.error(f"Data Export Error : Failed to Export Data to <{file_path}>"))
                logger.error(response.detail)
            
        # 3.2. Export to CSV
        elif config.format.upper() == 'CSV':
            try:
                with open(file_path, 'w',  newline='', encoding="utf-8-sig") as csv_file:
                    fieldnames = data[0].keys()
                    writer = csv.DictWriter(csv_file, fieldnames=fieldnames)
                    writer.writeheader()    # Write the header
                    writer.writerows(data)  # Write the data
                response = Response(status_code=200, detail=self.response_format.ok(f"Success : Exported Data to <{file_path}>"))
                logger.info(response.detail)

            except:
                response = Response(status_code=500, detail=self.response_format.error(f"Data Export Error : Failed to Export Data to <{file_path}>"))
                logger.error(response.detail)      
            
        # # 3.3. Export to DB
        # elif config.format.upper() in SETTINGS.EXPT.DB_FORM: # Valid DB
        #     db_config = IODatabaseConfiguration(**config.__dict__)

        #     try:
        #         engine = init_io_db_engine(db_config) # init engine
        #         IODBClass = set_io_db_table(table_name=db_config.table, engine=engine) # set table name
        #         db_data = [IODBClass(**CreateRequest(**_data).__dict__) for _data in data]
            
        #     except:
        #         response = Response(status_code=500, detail=self.response_format.error(f"Unexpected Error : Failed to Init Export DB <{db_config.name}>"))
        #         logger.error(response.detail)
        #         return response

        #     try:
        #         with get_io_db_func(engine=engine) as db:
        #             db.add_all(db_data)
        #             db.commit()
        #         response = Response(status_code=201, detail=self.response_format.ok(f"Success : Exported Data to DB <{db_config.table}>"))
        #         logger.info(response.detail)
            
        #     except:
        #         response = Response(status_code=500, detail=self.response_format.error(f"Unexpected Error : Failed to Export Data to DB <{db_config.table}>"))
        #         logger.error(response.detail)

        # 3.X. Unknown Export Format
        else:
            response = Response(status_code=500, detail=self.response_format.error("Data Export Error : Invalid Data Export Format"))
            logger.error(response.detail)       

        return response

    # Backup Data
    def backup_data(self, config: schema.BackupConfig | None, data: list) -> Response:         
        
        # 1. Detect Backup Configuration
        if config is None:
            config = DEFAULT_BACKUP_CONFIG.copy()
            response = Response(status_code=200, detail=self.response_format.ok("Info : Empty Backup Configuration >>> Use System Default Backup Configuration"))
            logger.info(response.detail)

        # 2. Check DB Format
        if not config.format.upper() in SETTINGS.BKUP.DB_FORM:
            response = Response(status_code=500, detail=self.response_format.error("Data Backup Error : Invalid Data Backup Format"))
            logger.error(response.detail)       
            return response 
         
        # Load Backup DB Config
        try:
            table_prefix = config.table
            config.table = config.table + '_' + datetime.now(timezone.utc).strftime("%Y-%m-%d-%H-%M-%S")
            db_config = BackupDatabaseConfiguration(**config.__dict__)
            engine = init_bkup_db_engine(db_config) # init engine
            BackupDBClass = set_bkup_db_table(table_name=db_config.table, engine=engine) # set table name
            db_data = [BackupDBClass(**DataCreate(**_data).dict()) for _data in data]
        
        except:
            response = Response(status_code=500, detail=self.response_format.error(f"Unexpected Error : Failed to Init Backup DB <{db_config.name}>"))
            logger.error(response.detail)
            return response

        # Add Data to Backup DB
        try:
            with get_bkup_db_func(engine=engine) as db:
                db.add_all(db_data)
                db.commit()
            response = Response(status_code=201, detail=self.response_format.ok(f"Success : Backuped Data to DB <{db_config.table}>"))
            logger.info(response.detail)

        except:
            response = Response(status_code=500, detail=self.response_format.error(f"Unexpected Error : Failed to Backup Data to DB <{db_config.table}>"))
            logger.error(response.detail)

            return response

        # # Check if Backup Limit is Reached
        # if config.limit and config.limit > 0:
        #     logger.info(f"Backup Progress : Backup Limit <{config.limit}> is Set. Start Dropping Previous Backup Tables")
        #     _table_list = sqlalchemy.inspect(engine).get_table_names()
        #     table_list  = [table for table in _table_list if table_prefix in table]
        #     table_list.sort(reverse=True)

        #     if len(table_list) > config.limit:
        #         table_to_drop = table_list[config.limit:]
        #         for table_name in table_to_drop:
        #             try:
        #                 with get_bkup_db_func(engine=engine) as db:
        #                     table = get_bkup_db_table(table_name=table_name, engine=engine)
        #                     table.drop(engine)
        #                     db.commit()
        #                 response = Response(status_code=200, detail=self.response_format.ok(f"Success : Dropped Backup DB Table <{table_name}>"))
        #                 logger.info(response.detail)
                    
        #             except:
        #                 response = Response(status_code=500, detail=self.response_format.error(f"Unexpected Error : Failed to Drop Backup DB Table <{table_name}>"))
        #                 logger.error(response.detail)
        #                 return response
                    
        #         response = Response(status_code=200, detail=self.response_format.ok(f"Completed Backup : Dropped Previous <{len(table_list)-config.limit}> Tables during Data Backup. <{len(sqlalchemy.inspect(engine).get_table_names())}> Backup Tables Remained"))
        #         logger.info(response.detail)    

        #     else:
        #         response = Response(status_code=200, detail=self.response_format.ok(f"Completed Backup : Number of Backup Table is Less than Backup Limit. No Actions are Required"))
        #         logger.info(response.detail)

        return response

    # Perform Query
    def query_with_conditions(
            self, 
            container_name:  str,
            filter_conditions: FilterConditions
        ) -> tuple[SecretData | None, Response]:

        db_data = None
        nodes = []
        edges = []

        if not filter_conditions.node_conditions and not filter_conditions.edge_conditions:
            response = Response(status_code=500, detail=self.response_format.error("Condition Filter Error : Condition Filter is Empty"))
            logger.error(response.detail)
            return db_data, response

        if filter_conditions.node_conditions:
            node_query = "g.V()" + "".join(filter_conditions.node_conditions)
            if filter_conditions.node_sorting:
                node_query += ".order()" + "".join(filter_conditions.node_sorting)
            if filter_conditions.node_filter_no and filter_conditions.node_filter_no >= 1:
                node_query += f".limit({filter_conditions.node_filter_no})"

            try:
                if self.api_call == True:
                    with self.gb_api(container_name) as gremlin_client:
                        db_nodes = gremlin_client.submit(node_query).all().result()
                else:
                    with self.gb_func(container_name) as gremlin_client:
                        db_nodes = gremlin_client.submit(node_query).all().result()

                parsed_nodes = []
                for db_node in db_nodes:
                    prop_dict = dict()
                    prop_dict = {NODE_LABEL: db_node.get("label", "")}
                    for prop_key, prop_values in db_node.get("properties", {}).items():
                        if prop_values:
                            prop_value = prop_values[0].get("value", None)
                            prop_dict[prop_key] = prop_value
                        prop_dict = value_converter_to_op(prop_dict, model=SecretNode)
                    parsed_nodes.append(prop_dict)

                nodes = [SecretNode(**parsed_node) for parsed_node in parsed_nodes]

                response = Response(status_code=200, detail=self.response_format.ok(f"Success : Retrieved <{len(db_nodes)}> Filtered {NODE_OBJECT} from DB"))

            # Handle common exceptions that might occur
            except (BaseException, Exception) as e:
                response = Response(status_code=500, detail=self.response_format.error(f"Common Error : Getting {NODE_OBJECT}", str(e)))
                logger.error(response.detail)

            # Handle any other exceptions that might occur
            except:
                response = Response(status_code=500, detail=self.response_format.error(f"Unexpected Error : Getting {NODE_OBJECT}"))
                logger.error(response.detail)

        if filter_conditions.edge_conditions:
            edge_query = "g.E()" + "".join(filter_conditions.edge_conditions)
            if filter_conditions.edge_sorting:
                edge_query += ".order()" + "".join(filter_conditions.edge_sorting)
            if filter_conditions.edge_filter_no and filter_conditions.edge_filter_no >= 1:
                edge_query += f".limit({filter_conditions.edge_filter_no})"

            try:
                if self.api_call == True:
                    with self.gb_api(container_name) as gremlin_client:
                        db_edges = gremlin_client.submit(edge_query).all().result()
                else:
                    with self.gb_func(container_name) as gremlin_client:
                        db_edges = gremlin_client.submit(edge_query).all().result()
                
                parsed_edges = []
                for db_edge in db_edges:
                    prop_dict = dict()
                    prop_dict = {EDGE_LABEL: db_edge.get("label", "")}
                    for prop_key, prop_value in db_edge.get("properties", {}).items():
                        prop_dict[prop_key] = prop_value
                        prop_dict = value_converter_to_op(prop_dict, modl=SecretEdge)
                    parsed_edges.append(prop_dict)

                edges = [SecretEdge(**parsed_edge) for parsed_edge in parsed_edges]

                response = Response(status_code=200, detail=self.response_format.ok(f"Success : Retrieved <{len(db_edges)}> Filtered {EDGE_OBJECT} from DB"))

            # Handle common exceptions that might occur
            except (BaseException, Exception) as e:
                response = Response(status_code=500, detail=self.response_format.error(f"Common Error : Getting {EDGE_OBJECT}", str(e)))
                logger.error(response.detail)

            # Handle any other exceptions that might occur
            except:
                response = Response(status_code=500, detail=self.response_format.error(f"Unexpected Error : Getting {EDGE_OBJECT}"))
                logger.error(response.detail)

        db_data = SecretData(nodes=nodes, edges=edges)

        return db_data, response
    
    def filter_formatter(self, data_filter: DataFilter) -> FilterConditions:
        
        def filter_by_byte(column_name: str, entity_filter: list[str]) -> list:
            conditions = []

            _encoded_values = []
            for value in entity_filter:
                encoded_value, response = CryptoServiceManager(api_call=False).encode_content(content=value)
                if response.status_code >= SETTINGS.STAT.SUCC_CODE_END:
                    logger.error(response.detail)
                    return conditions
                _encoded_values.append(encoded_value)

            conditions = []
            
            return conditions

        node_conditions = []
        node_sorting    = []
        node_bindings   = dict()
        edge_conditions = []
        edge_sorting    = []
        edge_bindings   = dict()

        # Parsing conditions

        if data_filter.node_filter:

            # String Filter
            if data_filter.node_filter.string_filter is not None:
                string_suffix = '_filter'
                string_dict = data_filter.node_filter.string_filter.__dict__

                if f"{NODE_LABEL}_filter" in string_dict.keys() and string_dict.get(f"{NODE_LABEL}_filter", None):
                    node_label_filter = string_dict.pop(f"{NODE_LABEL}_filter", [])
                    list_str = ','.join([f"g.hasLabel(\'{_value}\')" for _value in node_label_filter])
                    node_conditions.append(f".or_({list_str})")

                # Partition Key
                _partition_keys = string_dict.pop(f"{GRAPH_PARTITION_KEY}_filter", None)
                partition_keys = data_filter.partition_keys if data_filter.partition_keys else _partition_keys
                if partition_keys:
                    if len(partition_keys) == 1:
                        node_conditions.append(f".has(\'{GRAPH_PARTITION_KEY}\', \'{partition_keys[0]}\'")
                    else:
                        list_str = ','.join([f"\'{_value}\'" for _value in partition_keys])
                        node_conditions.append(f".has(\'{GRAPH_PARTITION_KEY}\', within({list_str}))")
                
                for key, value in string_dict.items():
                    if value is not None:
                        column_name = key.split(string_suffix)[0]
                        try:
                            list_str = ','.join([f"\'{_value}\'" for _value in value])
                            node_conditions.append(f".has(\'{column_name}\', within({list_str}))")
                        except AttributeError as e:
                            response = Response(status_code=404, detail=self.response_format.error(f"Attribute Error : Unrecognized Attribute <{column_name}>. The Attribute for StringFilter must be <key_filter>", str(e)))
                            logger.error(response.detail)
                        except Exception:
                            response = Response(status_code=500, detail=self.response_format.error(f"Unexpected Error : Unexpected Error during Parsing Attribute <{column_name}> in StringFilter"))
                            logger.error(response.detail)                    

            # Numeric Filter
            if data_filter.node_filter.numeric_filter is not None:
                min_suffix = '_min'
                max_suffix = '_max'
                for key, value in data_filter.node_filter.numeric_filter.__dict__.items():
                    if value is not None:
                        if min_suffix in key:
                            column_name = key.split(min_suffix)[0]
                            try:
                                node_conditions.append(f".has(\'{column_name}\', gte({value}))")
                            except AttributeError as e:
                                response = Response(status_code=404, detail=self.response_format.error(f"Attribute Error : Unrecognized Attribute <{column_name}>. The Attribute for NumericFilter must be <key_min> or <key_max>", str(e)))
                                logger.error(response.detail)
                            except Exception:
                                response = Response(status_code=500, detail=self.response_format.error(f"Unexpected Error : Unexpected Error during Parsing Attribute <{column_name}> in NumericFilter"))
                                logger.error(response.detail)              

                        elif max_suffix in key:
                            column_name = key.split(max_suffix)[0]
                            try:
                                node_conditions.append(f".has(\'{column_name}\', lte({value}))")
                            except AttributeError as e:
                                response = Response(status_code=404, detail=self.response_format.error(f"Attribute Error : Unrecognized Attribute <{column_name}>. The Attribute for NumericFilter must be <key_min> or <key_max>", str(e)))
                                logger.error(response.detail)
                            except Exception:
                                response = Response(status_code=500, detail=self.response_format.error(f"Unexpected Error : Unexpected Error during Parsing Attribute <{column_name}> in NumericFilter"))
                                logger.error(response.detail)    

                        else:
                            response = Response(status_code=404, detail=self.response_format.error(f"Attribute Error : Unrecognized Attribute <{column_name}>. The Attribute Suffix for NumericFilter must be Either _min or _max"))
                            logger.error(response.detail)

            # List Filter
            if data_filter.node_filter.list_filter is not None:
                or_suffix  = '_or'
                and_suffix = '_and'
                for key, value in data_filter.node_filter.list_filter.__dict__.items():
                    if value is not None:
                        if or_suffix in key:
                            column_name = key.split(or_suffix)[0]
                            list_str = ','.join([f"\'{_value}\'" for _value in value])
                            node_conditions.append(f".has(\'{column_name}\').unfold().has(within({list_str}))")
                            
                        elif and_suffix in key:
                            column_name = key.split(or_suffix)[0]
                            list_str = ','.join([f"\'{_value}\'" for _value in value])
                            node_conditions.append(f".has(\'{column_name}\').unfold().has(within({list_str}))")

                        else:
                            response = Response(status_code=404, detail=self.response_format.error(f"Attribute Error : Unrecognized Attribute <{column_name}>. The Attribute Suffix for ListFilter must be Either _or or _and"))
                            logger.error(response.detail)

            # Dictionary Filter
            if data_filter.node_filter.dictionary_filter is not None:
                or_suffix  = '_or'
                and_suffix = '_and'
                for key, value in data_filter.node_filter.dictionary_filter.__dict__.items():
                    if value is not None:
                        if or_suffix in key:
                            column_name = key.split(or_suffix)[0]  

                        elif and_suffix in key:
                            column_name = key.split(and_suffix)[0]

                        else:
                            response = Response(status_code=404, detail=self.response_format.error(f"Attribute Error : Unrecognized Attribute <{column_name}>. The Attribute Suffix for ListFilter must be Either _or or _and"))
                            logger.error(response.detail)

            # Boolean Filter
            if data_filter.node_filter.boolean_filter is not None:
                boolean_suffix = '_filter'
                
                for key, value in data_filter.node_filter.boolean_filter.__dict__.items():
                    if value is not None:
                        column_name = key.split(boolean_suffix)[0]
                        try:
                            node_conditions.append(f".has(\'{column_name}\', {str(value).lower()})")
                        except AttributeError as e:
                            response = Response(status_code=404, detail=self.response_format.error(f"Attribute Error : Unrecognized Attribute <{column_name}>. The Attribute for BooleanFilter must be <key_filter>", str(e)))
                            logger.error(response.detail)
                        except Exception:
                            response = Response(status_code=500, detail=self.response_format.error(f"Unexpected Error : Unexpected Error during Parsing Attribute <{column_name}> in BooleanFilter"))
                            logger.error(response.detail)

            # Datetime Filter
            if data_filter.node_filter.datetime_filter is not None:
                start_suffix = '_start'
                end_suffix   = '_end'
                
                for key, value in data_filter.node_filter.datetime_filter.__dict__.items():
                    if value is not None:
                        if start_suffix in key:
                            column_name = key.split(start_suffix)[0]        

                        if end_suffix in key:
                            column_name = key.split(end_suffix)[0]

            # Byte Filter
            if SETTINGS.BASE.APP_ENCRYPTION and data_filter.node_filter.byte_filter is not None:
                byte_suffix = '_filter'
                
                for key, value in data_filter.node_filter.byte_filter.__dict__.items():
                    if value is not None:
                        column_name = key.split(byte_suffix)[0]


        if data_filter.edge_filter:
            
            # String Filter
            if data_filter.edge_filter.string_filter is not None:
                string_suffix = '_filter'
                string_dict = data_filter.edge_filter.string_filter.__dict__
                if f"{EDGE_LABEL}_filter" in string_dict.keys() and string_dict.get(f"{EDGE_LABEL}_filter", None):
                    edge_label_filter = string_dict.pop(f"{EDGE_LABEL}_filter", [])
                    list_str = ','.join([f"g.hasLabel(\'{_value}\')" for _value in edge_label_filter])
                    edge_conditions.append(f".or_({list_str})")

                # Partition Key
                _partition_keys = string_dict.pop(f"{GRAPH_PARTITION_KEY}_filter", None)
                partition_keys = data_filter.partition_keys if data_filter.partition_keys else _partition_keys
                if partition_keys:
                    if len(partition_keys) == 1:
                        edge_conditions.append(f".has(\'{GRAPH_PARTITION_KEY}\', \'{partition_keys[0]}\'")
                    else:
                        list_str = ','.join([f"\'{_value}\'" for _value in partition_keys])
                        edge_conditions.append(f".has(\'{GRAPH_PARTITION_KEY}\', within({list_str}))")

                for key, value in string_dict.items():
                    if value is not None:
                        column_name = key.split(string_suffix)[0]
                        try:
                            list_str = ','.join([f"\'{_value}\'" for _value in value])
                            edge_conditions.append(f".has(\'{column_name}\', within({list_str}))")
                        except AttributeError as e:
                            response = Response(status_code=404, detail=self.response_format.error(f"Attribute Error : Unrecognized Attribute <{column_name}>. The Attribute for StringFilter must be <key_filter>", str(e)))
                            logger.error(response.detail)
                        except Exception:
                            response = Response(status_code=500, detail=self.response_format.error(f"Unexpected Error : Unexpected Error during Parsing Attribute <{column_name}> in StringFilter"))
                            logger.error(response.detail)    
 
            if data_filter.edge_filter.numeric_filter is not None:
                min_suffix = '_min'
                max_suffix = '_max'
                for key, value in data_filter.edge_filter.numeric_filter.__dict__.items():
                    if value is not None:
                        if min_suffix in key:
                            column_name = key.split(min_suffix)[0]
                            try:
                                edge_conditions.append(f".has(\'{column_name}\', gte({value}))")
                            except AttributeError as e:
                                response = Response(status_code=404, detail=self.response_format.error(f"Attribute Error : Unrecognized Attribute <{column_name}>. The Attribute for NumericFilter must be <key_min> or <key_max>", str(e)))
                                logger.error(response.detail)
                            except Exception:
                                response = Response(status_code=500, detail=self.response_format.error(f"Unexpected Error : Unexpected Error during Parsing Attribute <{column_name}> in NumericFilter"))
                                logger.error(response.detail)              

                        elif max_suffix in key:
                            column_name = key.split(max_suffix)[0]
                            try:
                                edge_conditions.append(f".has(\'{column_name}\', lte({value}))")
                            except AttributeError as e:
                                response = Response(status_code=404, detail=self.response_format.error(f"Attribute Error : Unrecognized Attribute <{column_name}>. The Attribute for NumericFilter must be <key_min> or <key_max>", str(e)))
                                logger.error(response.detail)
                            except Exception:
                                response = Response(status_code=500, detail=self.response_format.error(f"Unexpected Error : Unexpected Error during Parsing Attribute <{column_name}> in NumericFilter"))
                                logger.error(response.detail)    

                        else:
                            response = Response(status_code=404, detail=self.response_format.error(f"Attribute Error : Unrecognized Attribute <{column_name}>. The Attribute Suffix for NumericFilter must be Either _min or _max"))
                            logger.error(response.detail)

            if data_filter.edge_filter.list_filter is not None:
                or_suffix  = '_or'
                and_suffix = '_and'
                for key, value in data_filter.edge_filter.list_filter.__dict__.items():
                    if value is not None:
                        if or_suffix in key:
                            column_name = key.split(or_suffix)[0]
                            list_str = ','.join([f"\'{_value}\'" for _value in value])
                            edge_conditions.append(f".has(\'{column_name}\').unfold().has(within({list_str}))")
                            
                        elif and_suffix in key:
                            column_name = key.split(or_suffix)[0]
                            list_str = ','.join([f"\'{_value}\'" for _value in value])
                            edge_conditions.append(f".has(\'{column_name}\').unfold().has(within({list_str}))")

                        else:
                            response = Response(status_code=404, detail=self.response_format.error(f"Attribute Error : Unrecognized Attribute <{column_name}>. The Attribute Suffix for ListFilter must be Either _or or _and"))
                            logger.error(response.detail)

            if data_filter.edge_filter.dictionary_filter is not None:
                or_suffix  = '_or'
                and_suffix = '_and'
                for key, value in data_filter.edge_filter.dictionary_filter.__dict__.items():
                    if value is not None:
                        if or_suffix in key:
                            column_name = key.split(or_suffix)[0]  

                        elif and_suffix in key:
                            column_name = key.split(and_suffix)[0]

                        else:
                            response = Response(status_code=404, detail=self.response_format.error(f"Attribute Error : Unrecognized Attribute <{column_name}>. The Attribute Suffix for ListFilter must be Either _or or _and"))
                            logger.error(response.detail)

            # Boolean Filter
            if data_filter.edge_filter.boolean_filter is not None:
                boolean_suffix = '_filter'
                
                for key, value in data_filter.edge_filter.boolean_filter.__dict__.items():
                    if value is not None:
                        column_name = key.split(boolean_suffix)[0]
                        try:
                            edge_conditions.append(f".has(\'{column_name}\', {str(value).lower()})")
                        except AttributeError as e:
                            response = Response(status_code=404, detail=self.response_format.error(f"Attribute Error : Unrecognized Attribute <{column_name}>. The Attribute for BooleanFilter must be <key_filter>", str(e)))
                            logger.error(response.detail)
                        except Exception:
                            response = Response(status_code=500, detail=self.response_format.error(f"Unexpected Error : Unexpected Error during Parsing Attribute <{column_name}> in BooleanFilter"))
                            logger.error(response.detail)
  
            if data_filter.edge_filter.datetime_filter is not None:
                start_suffix = '_start'
                end_suffix   = '_end'
                
                for key, value in data_filter.edge_filter.datetime_filter.__dict__.items():
                    if value is not None:
                        if start_suffix in key:
                            column_name = key.split(start_suffix)[0]        

                        if end_suffix in key:
                            column_name = key.split(end_suffix)[0]

            if SETTINGS.BASE.APP_ENCRYPTION and data_filter.edge_filter.byte_filter is not None:
                byte_suffix = '_filter'
                
                for key, value in data_filter.edge_filter.byte_filter.__dict__.items():
                    if value is not None:
                        column_name = key.split(byte_suffix)[0]


        # Define sorting order
        if data_filter.node_filter:
            for col, direction in data_filter.node_filter.sorting.items():
                node_sorting.append(f".by(\'{col}\', {SORTING_MAPPER.get(direction)})")

        if data_filter.edge_filter:
            for col, direction in data_filter.edge_filter.sorting.items():
                edge_sorting.append(f".by(\'{col}\', {SORTING_MAPPER.get(direction)})")

        node_filter_no = data_filter.node_filter.filter_no if data_filter.node_filter and data_filter.node_filter.filter_no else 1
        edge_filter_no = data_filter.edge_filter.filter_no if data_filter.edge_filter and data_filter.edge_filter.filter_no else 1

        return FilterConditions(
            node_conditions=node_conditions, 
            node_bindings=node_bindings, 
            node_sorting=node_sorting, 
            node_filter_no=node_filter_no,
            edge_conditions=edge_conditions, 
            edge_bindings=edge_bindings, 
            edge_sorting=edge_sorting,
            edge_filter_no=edge_filter_no
        )
    
    def query_adjacent_nodes(self, request: AdjacentNodeRequest) -> tuple[AdjacentNodeResponse, Response]:
        response_data = AdjacentNodeResponse(**request.__dict__)
        nodes = []

        list_str = ', '.join([f"\'{_id}\'" for _id in request.data_filter.node_ids])

        message = f"g.V({list_str})"

        message += f".has(\'{GRAPH_PARTITION_KEY}\', \'{request.data_filter.partition_key}\')"

        if request.data_filter.direction.upper() == "IN":
            message += ".inE()"
            suffix  = ".outV()"
        elif request.data_filter.direction.upper() == "OUT":
            message += ".outE()"
            suffix  = ".inV()"
        else:
            message += ".bothE()"
            suffix  = ".bothV()"         

        if request.data_filter.edge_types:
            list_str = ','.join([f"\'{_value}\'" for _value in request.data_filter.edge_types])
            message +=  f".has(\'edge_type\', within({list_str}))"

        message += suffix

        if request.data_filter.filter_no and request.data_filter.filter_no >= 1:
            message += f".limit({request.data_filter.filter_no})"

        try:
            if self.api_call == True:
                with self.gb_api(request.container_name) as gremlin_client:
                    db_nodes = gremlin_client.submit(
                        message=message
                    ).all().result()
            else:
                with self.gb_func(request.container_name) as gremlin_client:
                    db_nodes = gremlin_client.submit(
                        message=message
                    ).all().result()

            parsed_nodes = []
            for db_node in db_nodes:
                prop_dict = dict()
                prop_dict = {NODE_LABEL: db_node.get("label", "")}
                for prop_key, prop_values in db_node.get("properties", {}).items():
                    if prop_values:
                        prop_value = prop_values[0].get("value", None)
                        prop_dict[prop_key] = prop_value
                    prop_dict = value_converter_to_op(prop_dict, model=SecretNode)
                parsed_nodes.append(prop_dict)

            nodes = [AdjacentNode(**parsed_node) for parsed_node in parsed_nodes]

            response = Response(status_code=200, detail=self.response_format.ok(f"Success : Retrieved <{len(db_nodes)}> Filtered {NODE_OBJECT} from DB"))

        # Handle common exceptions that might occur
        except (BaseException, Exception) as e:
            response = Response(status_code=500, detail=self.response_format.error(f"Common Error : Getting {EDGE_OBJECT}", str(e)))
            logger.error(response.detail)

        # Handle any other exceptions that might occur
        except:
            response = Response(status_code=500, detail=self.response_format.error(f"Unexpected Error : Getting {EDGE_OBJECT}"))
            logger.error(response.detail)

        response_data.__dict__.update(
            filtered_data = nodes,
            node_count    = len(nodes)
        )

        return response_data, response


    ### Not Yet Reviewed
    # def read_by_node_id(self, request: RelationshipReadNodeRequest) -> tuple[RelationshipReadRepsonse, Response]:
    #     try:
    #         node_id = request.node_id
    #         container_name = request.container_name if request.container_name else self.default_container

    #         # Query node properties
    #         query = "g.V().has('node_id', node_id).valueMap(true)"
    #         bindings = {"node_id": node_id}

    #         with self.gb_api(container_name) as gremlin_client:
    #             result_nodes = gremlin_client.submit(query, bindings).all().result()

    #             if not result_nodes:
    #                 return RelationshipReadRepsonse(data=[]), Response(status_code=404, detail=f"Node {node_id} not found")

    #             raw_node_data = result_nodes[0]
    #             # Flatten properties
    #             node_data = {
    #                 key: value[0] if isinstance(value, list) and len(value) == 1 else value
    #                 for key, value in raw_node_data.items()
    #             }
    #             extracted_id = node_data.pop('id', None)
    #             extracted_label = node_data.pop('label', None)

    #             # Query for both incoming and outgoing relationships
    #             rel_query = """
    #                 g.V().has('node_id', node_id).union(
    #                     outE().as('e').otherV().as('v')
    #                         .project('direction', 'relationship', 'target')
    #                         .by(constant('outgoing'))
    #                         .by(select('e').label())
    #                         .by(select('v').values('node_id')),
    #                     inE().as('e').otherV().as('v')
    #                         .project('direction', 'relationship', 'target')
    #                         .by(constant('incoming'))
    #                         .by(select('e').label())
    #                         .by(select('v').values('node_id'))
    #                 )
    #             """
    #             rel_bindings = {"node_id": node_id}
    #             rel_results = gremlin_client.submit(rel_query, rel_bindings).all().result()

    #             # Build relationships with direction
    #             relationships = [
    #                 ReadRelationshipModel(
    #                     direction=r['direction'],
    #                     relationship=r['relationship'],
    #                     target=r['target']
    #                 )
    #                 for r in rel_results
    #             ]

    #             node_model = NodeModel(
    #                 id=extracted_id,
    #                 label=extracted_label,
    #                 relationships=relationships,
    #                 extra_properties=node_data
    #             )

    #         # Prepare successful response
    #         data_response = RelationshipReadRepsonse(data=[node_model])
    #         success_response = Response(
    #             status_code=200,
    #             detail=f"Success: Read node {node_id}"
    #         )
    #         return data_response, success_response

    #     except httpx.TimeoutException as e:
    #         response = Response(status_code=502, detail=f"Timeout Error: Encountered Timeout Error when Connecting to Azure Server - {str(e)}")
    #         logger.error(response.detail)
    #         return None, response

    #     except httpx.HTTPError as e:
    #         response = Response(status_code=502, detail=f"Connection Error: Encountered Connection Error when Connecting to Azure Server - {str(e)}")
    #         logger.error(response.detail)
    #         return None, response

    #     except Exception as e:
    #         response = Response(status_code=500, detail=f"Common Error: Encountered Common Error when Calling Azure Server - {str(e)}")
    #         logger.error(response.detail)
    #         return None, response
        
    # def update_node(self, request: KGNodeUpdateRequest) -> Response:
    #     container_name = request.container_name
    #     node_id = request.node_id
    #     update_data = request.update_data.dict(exclude_unset=True)
        
    #     # Extract relationships to handle separately
    #     relationships = update_data.pop('relationship', [])
        
    #     # Automatically update the timestamp
    #     update_data["updated_at"] = datetime.now(timezone.utc).isoformat()

    #     try:
    #         with self.gb_api(container_name) as gremlin_client:
    #             # Check if the node exists
    #             exist_query = "g.V().has('node_id', node_id).count()"
    #             result = gremlin_client.submit(exist_query, {"node_id": node_id}).all().result()
    #             if result[0] == 0:
    #                 return Response(
    #                     status_code=404,
    #                     detail=f"Node Not Found: Node {node_id} not found in container {container_name}"
    #                 )

    #             # Build the update query for properties
    #             query = "g.V().has('node_id', node_id)"
    #             bindings = {"node_id": node_id}
    #             for i, (key, value) in enumerate(update_data.items()):
    #                 query += f".property('{key}', prop_{i})"
    #                 bindings[f"prop_{i}"] = value

    #             # Execute the property update query
    #             if update_data:
    #                 gremlin_client.submit(query, bindings)

    #             # Process relationships to add edges
    #             for rel in relationships:
    #                 rel_type = rel.get('relationship')
    #                 target_id = rel.get('target')
    #                 if not rel_type or not target_id:
    #                     continue

    #                 # Check if target node exists (parameterized)
    #                 target_exist = gremlin_client.submit(
    #                     "g.V().has('node_id', target_id).count()",
    #                     {"target_id": target_id}
    #                 ).all().result()[0]
    #                 if target_exist == 0:
    #                     return Response(
    #                         status_code=404,
    #                         detail=f"Target node {target_id} not found for relationship {rel_type}"
    #                     )

    #                 # Parameterized edge creation query
    #                 edge_query = (
    #                     "g.V().has('node_id', node_id).as('source')"
    #                     ".V().has('node_id', target_id)"
    #                     ".addE(rel_type).from('source')"  # Use parameter binding
    #                 )
    #                 bindings = {
    #                     "node_id": node_id,
    #                     "target_id": target_id,
    #                     "rel_type": rel_type  # Parameterize the relationship type
    #                 }

    #                 gremlin_client.submit(edge_query, bindings)

    #             return Response(
    #                 status_code=200,
    #                 detail=f"Success: Updated node {node_id} in container {container_name}"
    #             )
    #     except Exception as e:
    #         # Handle exceptions as before
    #         return Response(status_code=500, detail=f"Update Failed: {str(e)}")
        
    # def delete_by_node_id(self, request: RelationshipDeleteNodeRequest) -> Response:
    #     try:
    #         node_id = request.node_id
    #         container_name = request.container_name
    #         # Gremlin query to delete node based on node_id.
    #         query = "g.V().has('node_id', node_id).drop()"
    #         bindings = {"node_id": node_id}
            
    #         with self.gb_api(container_name) as gremlin_client:
    #             gremlin_client.submit(query, bindings)
            
    #         success_message = (
    #             f"Success: Deleted node with node_id '{node_id}' "
    #             f"from container '{container_name}'."
    #         )
    #         logger.info(success_message)
    #         return Response(status_code=200, detail=self.response_format.ok(success_message))
        
    #     except httpx.TimeoutException as e:
    #         error_message = (
    #             f"Timeout Error: Azure Cosmos DB did not respond in time during deletion "
    #             f"of node with node_id '{node_id}'."
    #         )
    #         logger.error(error_message + f" Exception: {str(e)}")
    #         return Response(status_code=502, detail=self.response_format.error(error_message, str(e)))
        
    #     except httpx.HTTPError as e:
    #         error_message = (
    #             f"Connection Error: An HTTP error occurred when deleting node with node_id "
    #             f"'{node_id}'."
    #         )
    #         logger.error(error_message + f" Exception: {str(e)}")
    #         return Response(status_code=502, detail=self.response_format.error(error_message, str(e)))
        
    #     except Exception as e:
    #         error_message = (
    #             f"Delete Failed: Failed to delete node with node_id '{node_id}'."
    #         )
    #         logger.error(error_message + f" Exception: {str(e)}")
    #         return Response(status_code=500, detail=self.response_format.error(error_message, str(e)))


    # def read_all_nodes(self, container_name: str = None) -> tuple[RelationshipReadRepsonse, Response]:
    #     """
    #     Retrieves all nodes from the given container.
    #     If no container_name is provided, uses a default container (adjust as needed).
    #     """
    #     # Provide a sensible default if none is given
    #     if not container_name:
    #         container_name = "your_default_container_name"

    #     try:
    #         # Gremlin query to fetch all nodes' properties
    #         query = "g.V().valueMap(true)"
            
    #         with self.gb_api(container_name) as gremlin_client:
    #             # Fetch all raw node data
    #             result_nodes = gremlin_client.submit(query).all().result()
                
    #             if not result_nodes:
    #                 # Return an empty list if there's no node at all
    #                 return RelationshipReadRepsonse(data=[]), Response(
    #                     status_code=200,
    #                     detail=f"Success: No nodes found in container '{container_name}'."
    #                 )
                
    #             node_models = []
    #             # Parse each node into a NodeModel
    #             for raw_node_data in result_nodes:
    #                 # Flatten each property returned by valueMap(true)
    #                 node_data = {
    #                     key: value[0] if isinstance(value, list) and len(value) == 1 else value
    #                     for key, value in raw_node_data.items()
    #                 }
    #                 extracted_id = node_data.pop('id', None)
    #                 extracted_label = node_data.pop('label', None)

    #                 node_model = NodeModel(
    #                     id=extracted_id,
    #                     label=extracted_label,
    #                     # If you want to retrieve edges/relationships, you'd need a separate query
    #                     relationships=[],
    #                     extra_properties=node_data
    #                 )
    #                 node_models.append(node_model)

    #         # Wrap results into your existing schemas
    #         data_response = RelationshipReadRepsonse(data=node_models)
    #         success_response = Response(
    #             status_code=200,
    #             detail=f"Success: Retrieved all nodes from container '{container_name}'."
    #         )
    #         return data_response, success_response

    #     except httpx.TimeoutException as e:
    #         error_message = (
    #             f"Timeout Error: Encountered a timeout when fetching all nodes from container '{container_name}'."
    #         )
    #         logger.error(error_message + f" Exception: {str(e)}")
    #         return None, Response(
    #             status_code=502,
    #             detail=self.response_format.error(error_message, str(e))
    #         )
    #     except httpx.HTTPError as e:
    #         error_message = (
    #             f"Connection Error: Encountered an HTTP error when fetching all nodes from container '{container_name}'."
    #         )
    #         logger.error(error_message + f" Exception: {str(e)}")
    #         return None, Response(
    #             status_code=502,
    #             detail=self.response_format.error(error_message, str(e))
    #         )
    #     except Exception as e:
    #         error_message = (
    #             f"Common Error: Encountered an error while fetching nodes from container '{container_name}'."
    #         )
    #         logger.error(error_message + f" Exception: {str(e)}")
    #         return None, Response(
    #             status_code=500,
    #             detail=self.response_format.error(error_message, str(e))
    #         )



