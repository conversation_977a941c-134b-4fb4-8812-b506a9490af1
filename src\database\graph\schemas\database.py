from pydantic import BaseModel

"""
    DB Connection
"""
class BackupDatabaseConfiguration(BaseModel):
    format:   str | None = None
    location: str | None = None
    name:     str | None = None
    host:     str | None = None
    port:     str | None = None
    user:     str | None = None
    pswd:     str | None = None
    table:    str | None = None
    rdir:     str | None = None
    sdir:     str | None = None

"""
    Import/Export DB Connection
"""
class IODatabaseConfiguration(BaseModel):
    format:   str | None = None
    location: str | None = None
    name:     str | None = None
    host:     str | None = None
    port:     str | None = None
    user:     str | None = None
    pswd:     str | None = None
    table:    str | None = None
    rdir:     str | None = None
    sdir:     str | None = None