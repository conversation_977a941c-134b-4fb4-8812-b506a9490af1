from pydantic import BaseModel
from typing import List

class GraphInfo(BaseModel):
    graph_name: str=''
    node_count: int=-1
    edge_count: int=-1

class GBGraphInfoResponse(BaseModel):
    total_graph_count: int=-1
    graph_list:        List[str]=[]
    total_node_count:  int=-1
    total_edge_count:  int=-1


"""
    DB Connection
"""
class BackupDatabaseConfiguration(BaseModel):
    format:   str | None = None
    location: str | None = None
    name:     str | None = None
    host:     str | None = None
    port:     str | None = None
    user:     str | None = None
    pswd:     str | None = None
    table:    str | None = None
    rdir:     str | None = None
    sdir:     str | None = None

"""
    Import/Export DB Connection
"""
class IODatabaseConfiguration(BaseModel):
    format:   str | None = None
    location: str | None = None
    name:     str | None = None
    host:     str | None = None
    port:     str | None = None
    user:     str | None = None
    pswd:     str | None = None
    table:    str | None = None
    rdir:     str | None = None
    sdir:     str | None = None