from sqlalchemy.orm import Session
from sqlalchemy import text
from sqlalchemy.exc import SQLAlchemyError
from datetime import datetime
import inspect

from ..schemas.format import Health, ResponseFormatter, Response

from ..settings import SETTINGS

from ..logger.log_handler import get_logger

logger = get_logger(__name__)


class HealthService:
    response_format = ResponseFormatter(suffix = f"{inspect.stack()[0][3]}")

    def __init__(self, db_api: Session, db_func: Session, api_call: bool):
        self.db_api   = db_api
        self.db_func  = db_func
        self.api_call = api_call

    def health_check(self) -> Health:
        response_primary = self.primary_database_connection_check()
   
        response_health = Health(app_name=SETTINGS.BASE.APP_NAME)
        if SETTINGS.BASE.APP_API == False:
            response_health.api_call = 'INACTIVE'
        if SETTINGS.BASE.APP_FUNC == False:
            response_health.function_call = 'INACTIVE'

        if response_primary.status_code >= SETTINGS.STAT.SUCC_CODE_END:
            response_health.primary_db = 'OFFLINE'
            response_health.primary_reason = response_primary.detail
            response_health.status_code = 404

        return response_health


    def primary_database_connection_check(self) -> Response:
        if self.api_call == True:
            call_method = "API Call"
        else:
            call_method = "Function Call"

        try:
            if self.api_call == True:
                if SETTINGS.BASE.APP_API == False:
                    response = Response(status_code=405, detail=self.response_format.ok(f"Conflict : API Call is Disabled but <{call_method}> is Used"))
                    logger.info(response.detail)
                    return response
            
            else:
                if SETTINGS.BASE.APP_FUNC == False:
                    response = Response(status_code=405, detail=self.response_format.ok(f"Conflict : Function Call is Disabled but <{call_method}> is Used"))
                    logger.info(response.detail)
                    return response

            response = Response(status_code=200, detail=self.response_format.ok(f"Success : Connected to Primary DB via <{call_method}>"))
            logger.info(response.detail)

        except SQLAlchemyError as e:
            response = Response(status_code=404, detail=self.response_format.error(f"Database Connection Error : Failed to Connect to Primary DB via <{call_method}>", str(e)))
            logger.error(response.detail)

        except:
            response = Response(status_code=500, detail=self.response_format.error(f"Unexpected Error : Failed to Connect to Primary DB via <{call_method}>"))
            logger.error(response.detail)

        return response