from pydantic import BaseModel, Field
import uuid
from datetime import datetime, timezone

"""
    User Key Retrieval Operation
"""
class UserKeyRetrievalRequest(BaseModel):
    key_requestid:   str=Field(default_factory=lambda: str(uuid.uuid4()))
    user_id:         str=''
    
    userkey_type:    str='default'
    request_origin:  str='USERHUB'
    requested_keys:  list[str]=[] # password_key, interaction_key, text_key, image_key, audio_key, video_key, prompt_key, tool_key
    
    request_at:      datetime=Field(default_factory=lambda: datetime.now(timezone.utc))

class UserKeyRetrievalResponse(BaseModel):
    key_requestid:      str=''
    user_id:            str=''

    userkey_type:       str='default'
    userkey_encryption: str='DEFAULT'

    password_key:       bytes | None = None
    interaction_key:    bytes | None = None
    text_key:           bytes | None = None
    image_key:          bytes | None = None
    audio_key:          bytes | None = None
    video_key:          bytes | None = None
    prompt_key:         bytes | None = None
    tool_key:           bytes | None = None

    key_retrieval_time: float | None =0.0
    response_at:        datetime | None = None