import unittest


# Function to be tested
def add_numbers(a, b):
    return a + b


class TestAddNumbers(unittest.TestCase):
    def test_add_positive_numbers(self):
        # Test case for adding two positive numbers
        result = add_numbers(3, 5)
        self.assertEqual(result, 8)

    def test_add_negative_numbers(self):
        # Test case for adding two negative numbers
        result = add_numbers(-2, -7)
        self.assertEqual(result, -9)

    def test_add_mixed_numbers(self):
        # Test case for adding positive and negative numbers
        result = add_numbers(5, -3)
        self.assertEqual(result, 2)

    def test_add_zero(self):
        # Test case for adding zero
        result = add_numbers(0, 10)
        self.assertEqual(result, 10)

        result = add_numbers(10, 0)
        self.assertEqual(result, 10)


if __name__ == '__main__':
    unittest.main()
