from datetime import datetime
import time
import uuid
import inspect
import httpx
import json
import re
from ..settings import SETTINGS

from ..schemas.format import (
    ResponseFormatter,
    Response
)

from ..services.inference_service import (
    InferenceServiceManager,
    InferenceInput,
    InferenceRequest
)
from ..schemas.vector import (
    KeywordSearchRequest,
    KeywordSearchResponse,
    VectorSearchRequest,
    VectorSearchResponse,
    VectorData,
    KnowledgeVectorSearchRequest
)

from ..schemas.keyword import (
    SystemKeywordMappingRequest,
    KeywordMappingFilter,
    KeywordMappingNumericFilter,
    KeywordMappingListFilter,
    KeywordMappingModel,
    KeywordEngine,
    KeywordExtractionRequest,  
    KeywordExtractionResponse,
    MappingRequest,
    MappingResult,
    
)

from ..logger.log_handler import get_logger
logger = get_logger(__name__)

class KeywordServiceManager:
    response_format = ResponseFormatter(suffix = f"{inspect.stack()[0][3]}")

    default_engine = KeywordEngine(
        keyword_id=SETTINGS.GEAI.KEYWD_ID,
        keyword_host=SETTINGS.GEAI.KEYWD_HOST,
        keyword_port=SETTINGS.GEAI.KEYWD_PORT,
        keyword_api=SETTINGS.GEAI.KEYWD_API,
        keyword_location=SETTINGS.GEAI.KEYWD_LOCATION,
        keyword_engine=SETTINGS.GEAI.KEYWD_ENGINE,
        keyword_base=SETTINGS.GEAI.KEYWD_BASE,
        keyword_model=SETTINGS.GEAI.KEYWD_MODEL,
        keyword_parameters=SETTINGS.GEAI.KEYWD_PARAMETERS,
        keyword_secrets=SETTINGS.GEAI.KEYWD_SECRETS,
        keyword_key=SETTINGS.GEAI.KEYWD_KEY,
        keyword_timeout=SETTINGS.GEAI.KEYWD_TIMEOUT
    )

    def __init__(self, api_call: bool, engine: KeywordEngine | None = None):
        self.api_call = api_call
        self.engine   = engine

    """
        Request Operation
    """
    def init_engine(self, engine: KeywordEngine | None = None) -> Response:
        if not engine:
            self.engine = self.default_engine
        else:
            self.engine = engine

        if self.engine.keyword_location.lower() == "azure":
            response  = Response(status_code=200, detail=self.response_format.ok(f"Init Keyword Completed : <{SETTINGS.BASE.APP_NAME}> Initiated Keyword Engine in Azure"))

        elif self.engine.keyword_location.lower() == "server":
            response = Response(status_code=200, detail=self.response_format.ok(f"Init Keyword Completed : <{SETTINGS.BASE.APP_NAME}> Initiated Keyword Engine in Server"))

        else:
            response = Response(status_code=404, detail=self.response_format.error(f"Init Keyword Engine Error : <{SETTINGS.BASE.APP_NAME}> Cannot Recognie Keyword Engine Location <{self.engine.keyword_location}>"))

        if response.status_code >= SETTINGS.STAT.SUCC_CODE_END:
            logger.error(response.detail)

        return response
    

    def text_keyword(self, request: KeywordExtractionRequest) -> tuple[KeywordExtractionResponse, Response]:
        logger.info(f"Processing : <{SETTINGS.BASE.APP_NAME}> Extracting Keywords")
        response_data = KeywordExtractionResponse(**request.__dict__)
        start_at      = time.time()

        tool_tokens   = 0

        self.init_engine()
        
        """ 1. Raw Data """
        if self.engine.keyword_location.lower() == "azure":
            processed_data, tool_tokens, response = self.azure_server(raw_data=request.data_input)

        else:
            response = Response(status_code=404, detail=self.response_format.error(f"Keyword Processing Error : <{SETTINGS.BASE.APP_NAME}> Failed to Perform Keyword <{self.engine.keyword_engine}>"))

        """ 2. Updated Result """
        # 2.1. Keyword Fail
        if response.status_code >= SETTINGS.STAT.SUCC_CODE_END:
            logger.error(response.detail)
            return response_data, response

        # 2.2. Keyword Completed but Encountered Different Lengths between Raw Data and Processed Data
        elif len(request.data_input) != len(processed_data):
            response  = Response(status_code=500, detail=self.response_format.error(f"Keyword Processing Error : <{SETTINGS.BASE.APP_NAME}> Found Unequal Number between Raw Data and Processed Data"))
            logger.error(response.detail)
            return response_data, response
        
        # 2.3. Keyword Success 
        else:
            response = Response(status_code=200, detail=self.response_format.ok(f"Keyword Completed : <{SETTINGS.BASE.APP_NAME}> Completed Keyword Process"))
            logger.info(response.detail)

        ### TODO: Add tokens count
        """ 3. Process Data """
        response_data.__dict__.update(
            data_output    = processed_data,
            keyword_model  = self.engine.keyword_model,
            keyword_tokens = tool_tokens,
            keyword_time   = time.time() - start_at,
            response_at    = datetime.now()
        )

        return response_data, response

    def azure_server(self, raw_data: list[str]) -> tuple[list[list[str]], int, Response]:
        from azure.ai.textanalytics import TextAnalyticsClient
        from azure.core.credentials import AzureKeyCredential
        
        processed_data = []
        tool_tokens = 0

        try:
            # Initialize Azure Text Analytics client
            text_analytics_client = TextAnalyticsClient(
                endpoint=self.engine.keyword_host,
                credential=AzureKeyCredential(self.engine.keyword_key)
            )

            # Extract keyphrases
            batch_size = 10
            response   = []
            for i in range(0, len(raw_data), batch_size):
                batch_data = raw_data[i : i+batch_size]
                language_response = text_analytics_client.detect_language(documents=batch_data)
                documents_with_language = []
                for j, data in enumerate(batch_data):
                    language = language_response[j]
                    if not language.is_error:
                        detected_language = language.primary_language.iso6391_name
                        if detected_language in ['zh_cht', 'zh_chs']:
                            language = 'zh_cht'
                        else:
                            language = 'en'
                    else:
                        language = 'en'
                        logger.error(f"Azure Keyword Error : <{SETTINGS.BASE.APP_NAME}> Failed to Detect Language for <{data}>, Set to <{language}>")

                    documents_with_language.append({'id': str(uuid.uuid4()), 'language': language, 'text': data})

                _response = text_analytics_client.extract_key_phrases(documents=documents_with_language)

                if not _response:
                    response = Response(status_code=500, detail=self.response_format.error(f"Azure Keyword Error : <{SETTINGS.BASE.APP_NAME}> Failed to Extract Keywords"))
                    logger.error(response.detail)
                    return processed_data, tool_tokens, response
                else:
                    response += _response

            # Process results
            for doc in response:
                if not doc.is_error:
                    processed_data.append(doc.key_phrases)
                    # Count words in each key phrase for better token approximation
                    phrase_tokens = sum(len(phrase) for phrase in doc.key_phrases)
                    tool_tokens += phrase_tokens  # More accurate token count
                else:
                    processed_data.append([])
                    logger.error(f"Error in keyword extraction: {doc.error}")

            response = Response(status_code=200, detail=self.response_format.ok(f"Azure Keyword Success : <{SETTINGS.BASE.APP_NAME}> Completed Azure Keyword"))
        
        except httpx.TimeoutException as e:
            response = Response(status_code=502, detail=self.response_format.error(f"Timeout Error : <{SETTINGS.BASE.APP_NAME}> Encountered Timeout Error when Connecting to Azure Server", str(e)))
            logger.error(response.detail)

        except httpx.HTTPError as e:
            response = Response(status_code=502, detail=self.response_format.error(f"Connection Error : <{SETTINGS.BASE.APP_NAME}> Encountered Connection Error when Connecting to Azure Server", str(e)))
            logger.error(response.detail)

        # Handle common exceptions that might occur
        except (BaseException, Exception) as e:
            response = Response(status_code=500, detail=self.response_format.error(f"Common Error : <{SETTINGS.BASE.APP_NAME}> Encountered Common Error when Calling Azure Server", str(e)))
            logger.error(response.detail)

        # Handle any other exceptions that might occur
        except:
            response = Response(status_code=500, detail=self.response_format.error(f"Unexpected Error : <{SETTINGS.BASE.APP_NAME}> Encountered Unexpected Error when Calling Azure Server", str(e)))
            logger.error(response.detail)

        return processed_data, tool_tokens, response


    def api_call_static(self, data, service: str, api_url: str, method: str, timeout: float | None) -> tuple[httpx.Response | None, Response]:
        response_data = None

        try:
            if method.lower() == "post":

                if isinstance(data, str):
                    if timeout:
                        resp = httpx.post(api_url, data=data, timeout=timeout)
                    else:
                        resp = httpx.post(api_url, data=data)

                else:
                    if timeout:
                        resp = httpx.post(api_url, json=data, timeout=timeout)
                    else:
                        resp = httpx.post(api_url, json=data)

            else:
                response = Response(status_code=500, detail=self.response_format.error(f"API Method Error : Unknown API Method <{method}>"))
                logger.error(response.detail)
                return response_data, response

            if not resp.status_code == httpx.codes.ok:
                response = Response(status_code=resp.status_code, detail=self.response_format.error(f"Response Error : Retrieving Data from <{service}> API Server", resp["detail"]))
                logger.error(response.detail)
            
            else:
                response = Response(status_code=resp.status_code, detail=self.response_format.ok(f"Success : Retrieved Data from <{service}> API Server"))
                response_data = resp

        except httpx.TimeoutException as e:
            response = Response(status_code=502, detail=self.response_format.error(f"Timeout Error : Retrieving Data <{service}> API Server", str(e)))
            logger.error(response.detail)

        except httpx.HTTPError as e:
            response = Response(status_code=502, detail=self.response_format.error(f"Connection Error : Retrieving Data <{service}> API Server", str(e)))
            logger.error(response.detail)

        # Handle common exceptions that might occur
        except (BaseException, Exception) as e:
            response = Response(status_code=500, detail=self.response_format.error(f"Common Error : Connecting to <{service}> API Server", str(e)))
            logger.error(response.detail)

        # Handle any other exceptions that might occur
        except:
            response = Response(status_code=500, detail=self.response_format.error(f"Unexpected Error : Connecting to <{service}> API Server"))
            logger.error(response.detail)

        return response_data, response
