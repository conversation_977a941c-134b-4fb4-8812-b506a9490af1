from fastapi import APIRouter, Depends, status
from typing import Any

from ...settings import SETTINGS
from ...schemas.format import Response
from ...utils import router_response_handler

from ...database.registry.services.qaflow_citation_log_data import (
        DataManager as QAFlowCitationLogDataManager,
        UserDataRequest as UserQAFlowCitationLogRequest, 
        UserDataResponse as UserQAFlowCitationLogResponse,
    )

router = APIRouter(tags=["Registry-User"])

# API DB Session
if SETTINGS.BASE.APP_API == True:
    default_api_call = True
else:
    default_api_call = False


@router.post("/user/qaflow_citation_log/query", include_in_schema=not(SETTINGS.BASE.APP_PRODUCTION), status_code=status.HTTP_200_OK, response_model=UserQAFlowCitationLogResponse)
def user_query_qaflow_citation_log(request: UserQAFlowCitationLogRequest, api_call: bool=default_api_call) -> UserQAFlowCitationLogResponse:
    request = UserQAFlowCitationLogRequest(**request.__dict__)
    response_data, response = QAFlowCitationLogDataManager(api_call=api_call).query_data_by_user(request=request)
    router_response_handler(response=response, api_call=api_call)
    return response_data
