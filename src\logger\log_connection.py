import os
import urllib.parse
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from sqlalchemy.exc import OperationalError
from contextlib import contextmanager

import logging

from ..settings import SETTINGS

# Check if DB saving into DB
if SETTINGS.LOG.SAVE_DB and SETTINGS.LOG.FORM.upper() in SETTINGS.LOG.DB_FORM:
    from ..logger.log_model import Base

    logger = logging.getLogger(__name__)

    if SETTINGS.LOG.LEVEL.upper() == "INFO":
        logger.setLevel(logging.INFO)
    elif SETTINGS.LOG.LEVEL.upper() == "DEBUG":
        logger.setLevel(logging.DEBUG)
    elif SETTINGS.LOG.LEVEL.upper() == "NOTSET":
        logger.setLevel(logging.NOTSET)
    elif SETTINGS.LOG.LEVEL.upper() == "ERROR":
        logger.setLevel(logging.ERROR)
    elif SETTINGS.LOG.LEVEL.upper() in ["WARN", "WARNING"]:
        logger.setLevel(logging.WARNING)
    elif SETTINGS.LOG.LEVEL.upper() in ["CRITICAL", "FATAL"]:
        logger.setLevel(logging.CRITICAL)
    else:
        logger.setLevel(logging.INFO)


    # Retrieving DB URL
    if SETTINGS.LOG.FORM.upper() == "PGDB":
        # Access Database Settings
        DB_URL = '{user}:{pswd}@{host}:{port}/{name}'.format(
            host=urllib.parse.quote_plus(SETTINGS.LOG.HOST),  # DB host
            port=urllib.parse.quote_plus(SETTINGS.LOG.PORT),  # DB port
            name=urllib.parse.quote_plus(SETTINGS.LOG.NAME),  # DB name
            user=urllib.parse.quote_plus(SETTINGS.LOG.USER),  # DB user
            pswd=urllib.parse.quote_plus(SETTINGS.LOG.PSWD)   # DB pswd
        )
        DATABASE_URL = f"postgresql://{DB_URL}" 

    elif SETTINGS.LOG.FORM.upper() == "SLDB":
        if SETTINGS.LOG.LOCA.lower() == "local":
            # Check/Create Local Database Directory
            db_dir = os.path.join(SETTINGS.LOG.RDIR, SETTINGS.LOG.SDIR)
            if not os.path.exists(db_dir):
                try:
                    os.makedirs(db_dir)
                except:
                    err_msg = f"Local DB Creation Error : <{SETTINGS.BASE.APP_NAME}> <{SETTINGS.LOG.NAME}> Database"
                    logger.error(err_msg)
                    raise Exception(err_msg)
                
            # Access Database Settings
            DB_URL = os.path.join(SETTINGS.LOG.RDIR, SETTINGS.LOG.SDIR, SETTINGS.LOG.NAME)

            DATABASE_URL = f"sqlite:///{DB_URL}??check_same_thread=False"

    else:
        err_msg = f"Unknown DB Error : <{SETTINGS.LOG.NAME}> Database"
        logger.error(err_msg)
        raise Exception(err_msg)

    # Test DB Connection
    try:
        engine = create_engine(DATABASE_URL, pool_pre_ping=True)
        with engine.connect():
            logger.info(f"Connected : <{SETTINGS.BASE.APP_NAME}> <{SETTINGS.LOG.NAME}> Database")

    # Handle any operational errors that might occur
    except OperationalError as e:
        err_msg = f"Connection Error : <{SETTINGS.BASE.APP_NAME}> <{SETTINGS.LOG.NAME}> Database"
        logger.error(err_msg)
        raise Exception(err_msg)

    # Handle any other exceptions that might occur
    except:
        err_msg = f"Unexpected Error : <{SETTINGS.BASE.APP_NAME}> <{SETTINGS.LOG.NAME}> Database"
        logger.error(err_msg)
        raise Exception(err_msg)

    Base.metadata.create_all(engine)

    SessionLocal = sessionmaker(
        autocommit=False, 
        autoflush=False, 
        bind=engine
    )

    @contextmanager
    def get_db():
        db = SessionLocal()
        try:
            yield db
        finally:
            db.close()