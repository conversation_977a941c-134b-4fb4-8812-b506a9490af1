# Constraint Settings
CSTR_NAMING=[",", ";"]

# Backup Setting
BKUP_DB_FORM=["SLDB", "PGDB"]
BKUP_FORM=SLDB
BKUP_LOCA=local
BKUP_NAME=prudential_windows_backup
BKUP_HOST=
BKUP_PORT=
BKUP_USER=
BKUP_PSWD=
BKUP_RDIR=os.path.join(os.path.dirname(os.path.expanduser("~")), "Public")
BKUP_SDIR=.share
BKUP_TABLE=qaflowlog_backup
BKUP_LIMIT=100 # 0 denotes no limit

# Import Setting
IMPT_DB_FORM=["SLDB", "PGDB"]
IMPT_FILE_FORM=["JSON", "CSV"]

# Export Setting
EXPT_DB_FORM=["SLDB", "PGDB"]
EXPT_FILE_FORM=["JSON", "CSV"]
EXPT_FORM=JSON
EXPT_LOCA=local
EXPT_NAME=prudential_export
EXPT_HOST=
EXPT_PORT=
EXPT_USER=
EXPT_PSWD=
EXPT_TABLE=tool_export
EXPT_RDIR=os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
EXPT_SDIR=.prudential_export
EXPT_FILE_RDIR=os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
EXPT_FILE_SDIR=.prudential_export
EXPT_FILE_NAME=qaflowlog_export

# Logger Setting
LOG_DB_FORM=["SLDB", "PGDB"]
LOG_FORM=SLDB
LOG_LOCA=local
LOG_NAME=prudential_log
LOG_HOST=
LOG_PORT=
LOG_USER=
LOG_PSWD=
LOG_RDIR=
LOG_SDIR=.share
LOG_TABLE=app_log
LOG_LIMIT=10000
LOG_FILE_RDIR=
LOG_FILE_SDIR=log
LOG_FILE_NAME=app.log
LOG_FILE_BYTE=1000
LOG_FILE_NUM=2
LOG_SAVE_DB=False        # Log into DB or not
LOG_SAVE_FILE=False      # Log into File or not
LOG_LEVEL=DEBUG

# Inference Configuration
INFR_ENGINE_TIMEOUT=60.0
INFR_ENGINE_STREAM_TIMEOUT=60.0
INFR_STATUS_CODE={"SUCCESS": "200", "FAIL": "500"}
INFR_RETRY_LIMIT=3

# Retrieval Setting
RETR_DEFAULT_KEYWORD_CONFIG={"primary": 50, "secondary":35, "top":50}
RETR_DEFAULT_KEYWORD_LIMIT=50
RETR_DEFAULT_VECTOR_CONFIG={"knn": {"knn": 10, "top_k": "10"}}
RETR_DEFAULT_HYBRID_CONFIG={"knn": {"knn": 50, "top_k": "150"}}
RETR_DEFAULT_GRAPH_CONFIG={"depth": 1, "edge_type_filter": [], "top_k": 5}
RETR_DEFAULT_RERANK_THRESHOLD=0.0
RETR_LANGUAGE_MAPPING={"en": "en", "zh": "zh_cht"}

