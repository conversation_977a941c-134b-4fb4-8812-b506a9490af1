import pytest
from src.services.refinement_service import RefinementServiceManager

@pytest.fixture
def refinement_service():
    return RefinementServiceManager(api_call=True)

def test_chinese_queries(refinement_service):
    queries = [
        "我想了解PRUlife的保障范围和保费计算",
        "如何申请医疗险理赔？需要准备哪些文件？",
        "我的PRUwealth保单现在价值多少？",
        "PRUlink的基金表现如何？可以转换投资组合吗？",
        "保单续期提醒服务怎么设置？"
    ]
    
    for query in queries:
        result = refinement_service.jsonlize(query)
        print(f"\nOriginal Query (Chinese): {query}")
        print(f"Translated Query: {result['response']}")
        print(f"Extracted Keywords: {result['keywords']}")
        print("-" * 50)
        
        # Basic assertions
        assert result['response'] != "", "Translation should not be empty"
        assert len(result['keywords']) > 0, "Should extract at least one keyword"
        assert isinstance(result['keywords'], list), "Keywords should be a list"

def test_english_queries(refinement_service):
    queries = [
        "What are the coverage details and premium calculation for PRUlife?",
        "How to file a medical insurance claim? What documents are required?",
        "What is the current value of my PRUwealth policy?",
        "How is the fund performance of PRUlink? Can I switch investment portfolios?",
        "How to set up policy renewal reminders?"
    ]
    
    for query in queries:
        result = refinement_service.jsonlize(query)
        print(f"\nOriginal Query (English): {query}")
        print(f"Translated Query: {result['response']}")
        print(f"Extracted Keywords: {result['keywords']}")
        print("-" * 50)
        
        # Basic assertions
        assert result['response'] != "", "Translation should not be empty"
        assert len(result['keywords']) > 0, "Should extract at least one keyword"
        assert isinstance(result['keywords'], list), "Keywords should be a list"

def test_mixed_language_queries(refinement_service):
    queries = [
        "PRUlife的waiting period是多久？",
        "我的PRUwealth保单的surrender value怎么查？",
        "PRUlink的fund switching需要什么条件？"
    ]
    
    for query in queries:
        result = refinement_service.jsonlize(query)
        print(f"\nOriginal Query (Mixed): {query}")
        print(f"Translated Query: {result['response']}")
        print(f"Extracted Keywords: {result['keywords']}")
        print("-" * 50)
        
        # Basic assertions
        assert result['response'] != "", "Translation should not be empty"
        assert len(result['keywords']) > 0, "Should extract at least one keyword"
        assert isinstance(result['keywords'], list), "Keywords should be a list"

def test_specific_insurance_terms(refinement_service):
    queries = [
        "PRUlife的critical illness coverage包括哪些疾病？",
        "PRUwealth的premium holiday如何申请？",
        "PRUlink的fund switching fee是多少？"
    ]
    
    for query in queries:
        result = refinement_service.jsonlize(query)
        print(f"\nOriginal Query (Insurance Terms): {query}")
        print(f"Translated Query: {result['response']}")
        print(f"Extracted Keywords: {result['keywords']}")
        print("-" * 50)
        
        # Basic assertions
        assert result['response'] != "", "Translation should not be empty"
        assert len(result['keywords']) > 0, "Should extract at least one keyword"
        assert isinstance(result['keywords'], list), "Keywords should be a list"
        # Verify that insurance product names are preserved
        assert any(product in result['keywords'] for product in ['PRUlife', 'PRUwealth', 'PRUlink']), "Should preserve insurance product names" 