from pydantic import BaseModel, Field
import uuid
from datetime import datetime, timezone

""" Refinement Request """
class RefinementRequest(BaseModel):
    refinement_requestid: str=Field(default_factory=lambda: str(uuid.uuid4()))
    refinement_type:      str='translation'
    system_prompt:        str=Field(default='', description="A system prompt in plaintext to generate completion.")
    query:                str=Field(description="The original query")
    config:               dict | None = None
    request_at:           datetime=Field(default_factory=lambda: datetime.now(timezone.utc))

class RefinementResponse(BaseModel):
    refinement_requestid:     str
    refined_queries:          list[str]=Field(default=[], description="The refined query")
    refinement_model:         str=''
    refinement_input_tokens:  int=-1
    refinement_output_tokens: int=-1
    refinement_time:          float=0.0
    response_at:              datetime | None = None



class RefinementKeyResponse(BaseModel):
    refinement_requestid:     str
    refined_queries:          list[str]=Field(default=[], description="The refined query")
    keywords:                 list[list[str]]=[[]]
    blacklist:                 list[str]=[]
    refinement_model:         str=''
    refinement_input_tokens:  int=-1
    refinement_output_tokens: int=-1
    refinement_time:          float=0.0
    response_at:              datetime | None = None