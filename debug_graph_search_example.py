#!/usr/bin/env python3
"""
Debug example for graph search with data_ids and edge_type_filter
"""

from src.schemas.graph import GraphSearchRequest, GraphSearchInfo

# Example of how to construct the request correctly
def create_debug_request():
    """
    Create a GraphSearchRequest with data_ids and edge_type_filter
    """
    
    # Your data
    data_ids = ["873e5240-0738-40e8-b2cc-d2b25a9a4fb4"]
    edge_type_filter = ["APPLICATION_PROCESS"]
    
    # Construct the request
    request = GraphSearchRequest(
        search_info=[
            GraphSearchInfo(
                data_ids=data_ids,
                # container_name can be auto-detected from knowledge_id
                # knowledge_id will be extracted from the chunks found by data_ids
            )
        ],
        search_config={
            "edge_type_filter": edge_type_filter,
            "depth": 1,  # Search depth
            "top_k": None  # No limit on results
        }
    )
    
    return request

if __name__ == "__main__":
    request = create_debug_request()
    print("GraphSearchRequest:")
    print(f"  search_info: {request.search_info}")
    print(f"  search_config: {request.search_config}")
    
    # Expected flow:
    print("\nExpected flow:")
    print("1. Find chunks with data_id '873e5240-0738-40e8-b2cc-d2b25a9a4fb4'")
    print("2. Extract node_ids and knowledge_id from those chunks")
    print("3. Find edges where those chunks are targets (reverse search)")
    print("4. Get Document Summary source nodes from those edges")
    print("5. Search forward from Document Summary nodes with edge_type_filter=['APPLICATION_PROCESS']")
    print("6. Return the target nodes of APPLICATION_PROCESS edges")
