from datetime import datetime
import time
import inspect
from typing import Any
import os
import tiktoken
from mongoengine import Q

from ..settings import SETTINGS

from ..schemas.format import (
    ResponseFormatter,
    Response,
    ComplexEncoder
)

from ..schemas.knowledge import (
    SystemKnowledgeRequest,
    KnowledgeStringFilter,
    KnowledgeListFilter,
    KnowledgeFilter,
    SecretKnowledge
)

from ..schemas.retrieval import(
    ModelUsageLog,
    RawCitationLog,
    KnowledgeRetrievalRequest,
    KnowledgeRetrievalResponse,
    RawKnowledgeRetrievalResponse,
    RawDocumentObject,
    RawCitationObject,
    CitationRetrievalRequest,
    CitationRetrievalResponse,
    CustomField,
    RawRetrievalObject,
    RetrievalTestRequest,
    RetrievalTestResponse,
    SystemKeywordMappingFuncRequest,
    SystemKeywordMappingFuncReponse
)

from ..schemas.vector import (
    VectorData,
    KeywordSearchRequest,
    VectorFilter,
    VectorStringFilter
)

from ..schemas.graph import (
    GraphSearchInfo,
    GraphSearchRequest
)

from ..routers.request import (

    request_query_refinement,
    request_query_refinement_with_key,
    request_keyword_extraction,
    request_query_embedding,
    request_keyword_search, 
    system_query_knowledge,
    request_vector_search,
    request_graph_search,
    request_rerank,
    RefinementRequest,
    EmbeddingRequest,
    KeywordExtractionRequest,
    VectorSearchRequest,
    GraphSearchRequest,
    RerankRequest
)

from ..routers.registry.general import (
    QAFlowCitationLogCreateRequest,
    QAFlowCitationLogBatchCreateRequest,
    general_batch_create_qaflow_citation_log,
    get_keyword_mappings_with_string,
)

from ..routers.vector.system import (
    SystemVectorRequest,
    system_query_vector
)

from ..routers.registry.system import (
    SystemQAFlowCitationLogRequest,
    system_query_qaflow_citation_log
)


from ..database.registry.services.qaflow_citation_log_data import (
    DataCreate as QAFlowCitationLogCreate,
    COLLECTION_NAME as QAFlowCitationLogCollectionName,
    DBClass as QAFlowCitationLog,
    get_db_api,
    get_db_func
)

# API DB Session
if SETTINGS.BASE.APP_API == True:
    db_api = get_db_api
    default_api_call = True
else:
    db_api = None
    default_api_call = False

# Function DB Session
if SETTINGS.BASE.APP_FUNC == True:
    db_func = get_db_func
else:
    db_func = None


from ..logger.log_handler import get_logger

logger = get_logger(__name__)


class RetrievalServiceManager:
    response_format = ResponseFormatter(suffix = f"{inspect.stack()[0][3]}")

    def __init__(
            self, 
            api_call: bool = default_api_call,
            db_api:   Any | None = db_api, 
            db_func:  Any | None = db_func
        ):
        self.api_call = api_call
        self.db_api   = db_api
        self.db_func  = db_func

    def retrieval_test(self, request: RetrievalTestRequest) -> tuple[RetrievalTestResponse, Response]:
        logger.info(f"Processing : <{SETTINGS.BASE.APP_NAME}> Starts Retrieval Test")
        start_at = time.time()
        response_retrieval = RetrievalTestResponse(**request.__dict__)

        # Token Check
        try:
            encoding = tiktoken.encoding_for_model("text-embedding-ada-002")
            query_token_count = len(encoding.encode(request.query))
            logger.info(f"Retrieval Test : Got <{query_token_count}> Input Tokens")
            if query_token_count > SETTINGS.GEAI.QUERY_TOKEN_MAX:
                response = Response(status_code=413, detail=self.response_format.error(f"Query Too Large Failed : <{SETTINGS.BASE.APP_NAME}> Got Too Large Input Query for Retrieval Test"))
                return response_retrieval, response
        except:
            response = Response(status_code=500, detail=self.response_format.error(f"Token Check Failed : <{SETTINGS.BASE.APP_NAME}> Failed to Perform Token Check for Input Query"))
    

        request_retrieval = KnowledgeRetrievalRequest(
            **request.__dict__,
            save_log = False
        )
        response_retrieval, response = self.knowledge_retrieval(request=request_retrieval)

        if response.status_code >= SETTINGS.STAT.SUCC_CODE_END:
            return response_retrieval, response
        
        grouped_citations = []
        for _document in response_retrieval.documents:
            _citaitons = [_citation for _citation in response_retrieval.citations if _citation.knowledge_id == _document.knowledge_id]
            test_document = RawRetrievalObject(
                document  = _document,
                citations = _citaitons
            )
            grouped_citations.append(test_document)
        
        response_retrieval.__dict__.update(
            **{
                "grouped_citations": grouped_citations,
                "document_count":    response_retrieval.document_count,
                "citation_count":    response_retrieval.citation_count
            }
        )

        response = Response(status_code=200, detail=self.response_format.ok(f"Retrieval Test Completed : <{SETTINGS.BASE.APP_NAME}> Completed Retrieval Test"))
        
        return response_retrieval, response
    
    def append_synonyms_to_text(self, text: str, synonym_map: dict[str, list[str]]) -> str:
        import re
        for kw, syns in synonym_map.items():
            if syns:
                synonyms_without_kw = [s for s in syns if s.lower() != kw.lower()]
                if synonyms_without_kw:
                    # Use a space before "(" if you prefer:
                    syn_tag = f"{kw}({', '.join(synonyms_without_kw)})"
                else:
                    syn_tag = kw
                # Remove the \b word boundary for Chinese
                pattern = re.escape(kw)
                text = re.sub(pattern, syn_tag, text)
        return text
    
    def knowledge_retrieval(self, request: KnowledgeRetrievalRequest)-> tuple[RawKnowledgeRetrievalResponse, Response]:
        logger.info(f"Processing : <{SETTINGS.BASE.APP_NAME}> Starts Knowledge Retrieval")
        start_at = time.time()
        response_retrieval = RawKnowledgeRetrievalResponse(**request.__dict__)
        refined_queries    = []
        _model_usage       = []

        """ 
            1. Refine User Query 
        """
        request_refinement = RefinementRequest(
            refinement_type = 'translation',
            query = request.query
        )
        try:
            response_refinement = request_query_refinement_with_key(request=request_refinement, api_call=self.api_call)
            
            # Parse Output
            refined_queries = response_refinement.refined_queries
            keywords = response_refinement.keywords
            blacklist = response_refinement.blacklist
            # Update Log
            _model_usage += [
                ModelUsageLog(
                    service_name  = 'refinement',
                    model_name    = response_refinement.refinement_model,
                    input_tokens  = response_refinement.refinement_input_tokens,
                    output_tokens = response_refinement.refinement_output_tokens,
                    process_sec   = response_refinement.refinement_time
                )
            ]
            
            response_retrieval.retrieval_log.__dict__.update(
                **{
                    "model_usage": _model_usage,
                    "query_keywords": [_k for _keyword in keywords for _k in _keyword],
                    "input_query": request.query
                }
            )

        except Exception as e:
            response = Response(
                status_code=500, 
                detail=self.response_format.error(
                    f"Knowledge Retrieval Failed : <{SETTINGS.BASE.APP_NAME}> Query Refinement Failed", 
                    str(e)
                )
            )
            response_retrieval.retrieval_log.__dict__.update(
                **{
                    "success_flag":  False,
                    "error_message": response.detail,
                    "process_sec":   time.time() - start_at
                }
            )
            return response_retrieval, response

        """ 
            2. Collect all unique keywords
        """
        unique_key_start_at = time.time()
        all_unique_keywords = {kw for kw_list in keywords for kw in kw_list if kw.strip()}

        keyword_synonym_map: dict[str, list[str]] = {}
        ranking_1_synonyms = set()
        ranking_2_synonyms = set()

        for kw in all_unique_keywords:
            try:
                req = SystemKeywordMappingFuncRequest(search_str=kw)
                mapping_response = get_keyword_mappings_with_string(request=req)

                # If we do find results...
                if mapping_response.filtered_data:
                    syns: set[str] = set()
                    # track if kw has at least one entry with ranking=1
                    has_rank_1 = False

                    for entry in mapping_response.filtered_data:
                        if not entry.keywords:
                            continue

                        # add all synonyms from this entry
                        syns.update(entry.keywords)

                        # if ranking=1 => we consider these synonyms for the first search
                        # otherwise => second search
                        if entry.ranking == 1:
                            has_rank_1 = True
                            ranking_1_synonyms.update(entry.keywords)
                        else:
                            ranking_2_synonyms.update(entry.keywords)

                    # Put the original kw into the map
                    keyword_synonym_map[kw] = list(syns)

                    # If at least one entry had ranking=1, put kw itself in rank-1; else rank-2
                    if has_rank_1:
                        ranking_1_synonyms.add(kw)
                    else:
                        ranking_2_synonyms.add(kw)
                
                else:
                    # Not found at all => "fallback" to rank-2
                    ranking_2_synonyms.add(kw)
                    keyword_synonym_map[kw] = []

            except Exception as e:
                detail = self.response_format.error(
                    f"Knowledge Retrieval Failed : <{SETTINGS.BASE.APP_NAME}> Synonym Lookup Failed for '{kw}'",
                    e
                )
                response = Response(status_code=500, detail=detail)
                response_retrieval.retrieval_log.__dict__.update(
                    success_flag=False,
                    error_message=detail,
                    process_sec=time.time() - start_at
                )
                return response_retrieval, response

        # Expand raw keywords with synonyms in the "keywords" structure used later
        expanded_keywords: list[list[str]] = []
        for kw_list in keywords:
            merged: list[str] = []
            seen: set[str] = set()
            for kw in kw_list:
                if kw not in seen:
                    merged.append(kw)
                    seen.add(kw)
                # Add each synonym from the map
                for syn in keyword_synonym_map.get(kw, []):
                    if syn not in seen:
                        merged.append(syn)
                        seen.add(syn)
            expanded_keywords.append(merged)

        keywords = expanded_keywords

        # Build the original + synonyms for embedding
        og_query = self.append_synonyms_to_text(request.query, keyword_synonym_map)
        expanded_refined = [
            self.append_synonyms_to_text(q, keyword_synonym_map)
            for q in refined_queries
        ]
        integrated_quries = [og_query] + expanded_refined

        unique_keyword_time = time.time() - unique_key_start_at

        """ 
            2. Keyword Extraction 
        """
        # request_keyword = KeywordExtractionRequest(data_input=integrated_quries)
        # try:
        #     response_keyword = request_keyword_extraction(request=request_keyword, api_call=self.api_call)
            
        #     # Parse Output
        #     extracted_keywords = list(set([_keyword for _output in response_keyword.data_output for _keyword in _output]))

        #     # Update Log
        #     _model_usage += [
        #         ModelUsageLog(
        #             service_name  = 'keyword_extraction',
        #             model_name    = response_keyword.keyword_model,
        #             input_tokens  = response_keyword.keyword_input_tokens,
        #             output_tokens = response_keyword.keyword_output_tokens,
        #             process_sec   = response_keyword.keyword_time
        #         )
        #     ]
        #     response_retrieval.retrieval_log.__dict__.update(
        #         **{
        #             "query_keywords": extracted_keywords,
        #             "model_usage":    _model_usage
        #         }
        #     )

        # except Exception as e:
        #     response = Response(status_code=500, detail=self.response_format.error(f"Knowledge Retrieval Failed : <{SETTINGS.BASE.APP_NAME}> Keyword Extraction Failed", str(e)))
        #     response_retrieval.retrieval_log.__dict__.update(
        #         **{
        #             "success_flag":  False,
        #             "error_message": response.detail,
        #             "process_sec":   time.time() - start_at
        #         }
        #     )
        #     return response_retrieval, response


        """ 
            3. Embedding Generation 
        """
        integrated_quries = [_query for _query in integrated_quries if _query]
        
        request_embedding = EmbeddingRequest(data_input=integrated_quries)
        try:
            response_embedding = request_query_embedding(request=request_embedding, api_call=self.api_call)
            
            # Parse Output
            processed_queries  = response_embedding.data_output

            # Update Log
            _model_usage += [
                ModelUsageLog(
                    service_name  = 'embedding',
                    model_name    = response_embedding.embedding_model,
                    input_tokens  = response_embedding.embedding_tokens,
                    process_sec   = response_embedding.embedding_time
                )
            ]
            response_retrieval.retrieval_log.__dict__.update(**{"model_usage": _model_usage})

        except Exception as e:
            response = Response(status_code=500, detail=self.response_format.error(f"Knowledge Retrieval Failed : <{SETTINGS.BASE.APP_NAME}> Embedding Failed", str(e)))
            response_retrieval.retrieval_log.__dict__.update(
                **{
                    "success_flag":  False,
                    "error_message": response.detail,
                    "process_sec":   time.time() - start_at
                }
            )
            return response_retrieval, response


        """ 
            4. Check if the request is empty 
        """
        if not processed_queries or request.search_config.vector_search_enable == False:
            response = Response(status_code=404, detail=self.response_format.error(f"Knowledge Retrieval Failed : <{SETTINGS.BASE.APP_NAME}> Found Empty Embedded Query"))
            logger.error(response.detail)
            response_retrieval.retrieval_log.__dict__.update(
                **{
                    "success_flag":  False,
                    "error_message": response.detail,
                    "process_sec":   time.time() - start_at
                }
            )
            return response_retrieval, response


        ### TODO: Oscar to add Filtering Here
        # Assume here already get a group_id
        agent_group_id = ""
        
        ### To use group_id to find valid knowledge for retrieval
        ### Based on agent_group_id, filter knowledge_ids from the KnowledgeDB
        ### system_query_knowledge()
        
        # response_metadata  = system_query_knowledge(request=metadata_request, api_call=self.api_call)
        
        ### Get the knowledge_ids 
        ### Put back to request.knowledge_ids

        """ 
            5. (Optional) Filtered Knowledge IDs based on Keyword Search 
            
        """

        
        """ 
            5.1 Keyword Search for Rank 1 Keywords
        """
        if SETTINGS.BASE.APP_AUTH == "group":
            logger.info("Processing : Filtering Knowledge IDs based on Access Group")
            agent_group_id_list = request.agent_group_id_list
            if "Cashier" in agent_group_id_list:
                filter_metadata_request = SystemKnowledgeRequest(
                    data_filter=KnowledgeFilter(
                        string_filter=KnowledgeStringFilter(library_name_en_filter=agent_group_id_list)
                    )
                )
            else:
                filter_metadata_request = SystemKnowledgeRequest(
                    data_filter=KnowledgeFilter(
                        list_filter=KnowledgeListFilter(group_id_or=agent_group_id_list)
                    )
                )
            try:
                response_metadata  = system_query_knowledge(request=filter_metadata_request, api_call=self.api_call)
                permitted_knowledge_ids = [knowledge.knowledge_id for knowledge in response_metadata.filtered_data]
                logger.info(f"Knowledge <Agent Metadata Search Completed : <{SETTINGS.BASE.APP_NAME}> Retrieved <{len(permitted_knowledge_ids)}> Knowledge Metadata")

            except Exception as e:
                response = Response(status_code=500, detail=self.response_format.error(f"Knowledge Retrieval Failed : <{SETTINGS.BASE.APP_NAME}> Agent Metadata Retrieval Error", str(e)))
                response_retrieval.retrieval_log.__dict__.update(
                    **{
                        "success_flag":  False,
                        "error_message": response.detail,
                        "process_sec":   time.time() - start_at
                    }
                )
                return response_retrieval, response
        else:
            permitted_knowledge_ids = []
        # extracted_keywords = list(set([_keyword for _output in keywords for _keyword in _output]))
        
        # If user-supplied knowledge_ids exist, we will skip searching for them
        if request.knowledge_ids:
            filtered_knowledge_ids = request.knowledge_ids
        else:
            # Prepare two sets for rank=1 keywords and rank=2/others
            ranking_1_extracted_keywords = list(ranking_1_synonyms)
            ranking_2_extracted_keywords = list(ranking_2_synonyms)

            # Final merged knowledge IDs after all searches
            final_knowledge_ids_set = set()
            # list_of_keyword_1_knowledge_ids = None
            keyword_knowledge_ids = []

            """ 
                5.1 Keyword Search for Rank=1 Keywords
            """
            if (
                request.search_config.keyword_search_enable 
                and ranking_1_extracted_keywords is not None
            ):
                try:
                    keyword_request_1 = KeywordSearchRequest(
                        knowledge_ids         = permitted_knowledge_ids,
                        # knowledge_ids         = request.knowledge_ids,
                        keywords              = ranking_1_extracted_keywords,
                        blacklist             = blacklist,
                        keyword_search_config = {"top": request.search_config.keyword_search_config.get("primary")}                    
                        )
                    response_keyword_1 = request_keyword_search(request=keyword_request_1, api_call=self.api_call)
                    keyword_knowledge_ids = response_keyword_1.knowledge_ids

                    _model_usage += [
                        ModelUsageLog(
                            service_name  = 'keyword_search_rank1',
                            model_name    = 'aisearch',
                            process_sec   = response_keyword_1.keyword_retrieval_time
                        )
                    ]
                    response_retrieval.retrieval_log.__dict__.update(**{"model_usage": _model_usage})

                except Exception as e:
                    response = Response(status_code=500, detail=self.response_format.error(f"Knowledge Retrieval Failed : <{SETTINGS.BASE.APP_NAME}> Rank=1 Keyword Search Failed", str(e)))
                    response_retrieval.retrieval_log.__dict__.update(
                        **{
                            "success_flag":  False,
                            "error_message": response.detail,
                            "process_sec":   time.time() - start_at
                        }
                    )
                    logger.info(response.detail)
                    return response_retrieval, response
            
            """ 
                5.2 Keyword Search for Rank=2 or Others
            """
            if (
                request.search_config.keyword_search_enable 
                and ranking_2_extracted_keywords  is not None
            ):
                try:
                    keyword_request_2 = KeywordSearchRequest(
                        knowledge_ids         = keyword_knowledge_ids,
                        keywords              = ranking_2_extracted_keywords,
                        blacklist             = blacklist,
                        keyword_search_config =  {"top": request.search_config.keyword_search_config.get("secondary")},
                    )
                    response_keyword_2 = request_keyword_search(request=keyword_request_2, api_call=self.api_call)
                    if response_keyword_2.knowledge_ids is not []:
                        keyword_knowledge_ids = response_keyword_2.knowledge_ids

                    _model_usage += [
                        ModelUsageLog(
                            service_name  = 'keyword_search_rank2',
                            model_name    = 'aisearch',
                            process_sec   = response_keyword_2.keyword_retrieval_time
                        )
                    ]
                    response_retrieval.retrieval_log.__dict__.update(**{"model_usage": _model_usage})

                except Exception as e:
                    response = Response(status_code=500, detail=self.response_format.error(f"Knowledge Retrieval Failed : <{SETTINGS.BASE.APP_NAME}> Rank=2 Keyword Search Failed", str(e)))
                    response_retrieval.retrieval_log.__dict__.update(
                        **{
                            "success_flag":  False,
                            "error_message": response.detail,
                            "process_sec":   time.time() - start_at
                        }
                    )
                    return response_retrieval, response

            filtered_knowledge_ids = keyword_knowledge_ids

        response_retrieval.retrieval_log.__dict__.update(
            **{
                "keyword_knowledges": [RawCitationLog(knowledge_id=_id) for _id in filtered_knowledge_ids]
            }
        )


        """ 
            6. Retrieve Vectors 
        """
        retrieved_graph_data     = []
        vector_retrieval_outputs = []
        vector_retrieval_time    = 0
        for raw_query, processed_query in zip(integrated_quries, processed_queries):
            vector_request = VectorSearchRequest(
                retrieval_requestid = request.retrieval_requestid,
                processed_query     = processed_query,
                query               = raw_query,             
                knowledge_ids       = filtered_knowledge_ids,
                search_method       = 'hybrid',
                search_config       = request.search_config.vector_search_config
            )
        
            try:
                response_vector = request_vector_search(request=vector_request, api_call=self.api_call)
                vector_retrieval_outputs.append(response_vector.retrieved_data)
                vector_retrieval_time += response_vector.vector_retrieval_time

            except Exception as e:
                response = Response(status_code=500, detail=self.response_format.error(f"Knowledge Retrieval Failed : <{SETTINGS.BASE.APP_NAME}> Vector Search Failed", str(e)))
                response_retrieval.retrieval_log.__dict__.update(
                    **{
                        "success_flag":  False,
                        "error_message": response.detail,
                        "process_sec":   time.time() - start_at
                    }
                )
                return response_retrieval, response

        # Parse Output
        retrieved_data = self.consolidate_retrieved_vectors(data=vector_retrieval_outputs)

        # Update Log
        _model_usage += [
            ModelUsageLog(
                service_name = 'vector_search',
                model_name   = 'aisearch',
                process_sec  = vector_retrieval_time
            )
        ]
        response_retrieval.retrieval_log.__dict__.update(
            **{
                "model_usage": _model_usage,
                "vector_citations": [
                    RawCitationLog(
                        data_id = _data.data_id,
                        data_version = _data.data_version,
                        knowledge_id = _data.knowledge_id
                    ) for _data in retrieved_data
                    if _data.score >= 0.0
                ]
            }
        )

        retrieved_graph_data += [VectorData(**_data.__dict__) for _data in retrieved_data if _data.score < 0.0]


        """ 
            7. Knowledge Metadata Retrieval 
        """
        metadata_start_at = time.time()
        logger.info("Processing : Retrieving Knowledge Metadata")
        knowledge_ids    = list(set([_data.knowledge_id for _data in retrieved_data]))
        metadata_request = SystemKnowledgeRequest(
            data_filter=KnowledgeFilter(
                string_filter=KnowledgeStringFilter(knowledge_id_filter=knowledge_ids)
            )
        )
        try:
            response_metadata  = system_query_knowledge(request=metadata_request, api_call=self.api_call)
            knowledge_metadata = response_metadata.filtered_data
            logger.info(f"Knowledge <Metadata Search Completed : <{SETTINGS.BASE.APP_NAME}> Retrieved <{len(knowledge_metadata)}> Knowledge Metadata")

        except Exception as e:
            response = Response(status_code=500, detail=self.response_format.error(f"Knowledge Retrieval Failed : <{SETTINGS.BASE.APP_NAME}> Metadata Retrieval Error", str(e)))
            response_retrieval.retrieval_log.__dict__.update(
                **{
                    "success_flag":  False,
                    "error_message": response.detail,
                    "process_sec":   time.time() - start_at
                }
            )
            return response_retrieval, response

        """
            8. Graph Search
        """
        if request.search_config.graph_search_enable == True:
            logger.info("Processing : Performing Graph Search")
            graph_retrieval_time = time.time()
            
            # Step 1: 从input chunks中提取对应的DOCUMENT类型数据（Document Summary）
            # 首先收集所有input chunks的knowledge_ids
            input_knowledge_ids = list(set([_data.knowledge_id for _data in retrieved_data]))
            logger.info(f"Found {len(input_knowledge_ids)} unique knowledge_ids from {len(retrieved_data)} input chunks")
            
            # Step 2: 找到这些knowledge_ids对应的DOCUMENT类型数据作为Document Summary
            document_summary_data = []
            search_info = []
            processed_knowledge_ids = set()  # Track processed knowledge_ids to avoid duplicates
            
            for _metadata in knowledge_metadata:
                if (_metadata.knowledge_graphstorage and 
                    _metadata.knowledge_id in input_knowledge_ids and 
                    _metadata.knowledge_id not in processed_knowledge_ids):
                    
                    # 从vector数据库中查找该knowledge_id对应的DOCUMENT类型数据
                    try:
                        # 构建查询请求找DOCUMENT类型数据
                        document_request = SystemVectorRequest(
                            vector_filter=VectorFilter(
                                string_filter = VectorStringFilter(
                                    knowledge_id_filter = [_metadata.knowledge_id],
                                    data_type_filter = ["DOCUMENT"]
                                )
                            )
                        )
                        response_document = system_query_vector(request=document_request, api_call=self.api_call)
                        
                        # 获取DOCUMENT类型的数据（Document Summary）
                        knowledge_documents = [VectorData(**_vector.__dict__) for _vector in response_document.filtered_vectors]
                        
                        if knowledge_documents:
                            document_summary_data.extend(knowledge_documents)
                            # 提取Document Summary的node_ids用于graph搜索
                            node_ids = [_doc.node_id for _doc in knowledge_documents if _doc.node_id]
                            
                            if node_ids:
                                search_info.append(
                                    GraphSearchInfo(
                                        container_name = _metadata.knowledge_graphinfo.get("container", ""),
                                        knowledge_id   = _metadata.knowledge_id,
                                        node_ids       = node_ids
                                    )
                                )
                                processed_knowledge_ids.add(_metadata.knowledge_id)  # Mark as processed
                                logger.info(f"Found {len(knowledge_documents)} DOCUMENT summaries for knowledge_id: {_metadata.knowledge_id} with {len(node_ids)} node_ids")
                    
                    except Exception as e:
                        logger.warning(f"Failed to retrieve DOCUMENT data for knowledge_id {_metadata.knowledge_id}: {str(e)}")
                        continue

            if not search_info:
                logger.info("No Document Summary data found for graph search, skipping graph search")
                retrieved_graph_data = []
            else:
                logger.info(f"Starting graph search with {len(search_info)} Document Summaries")
                graph_request = GraphSearchRequest(
                    search_info   = search_info,
                    search_method = 'default',
                    search_config = request.search_config.graph_search_config
                )

                try:
                    response_graph = request_graph_search(request=graph_request, api_call=self.api_call)

                except Exception as e:
                    response = Response(status_code=500, detail=self.response_format.error(f"Knowledge Retrieval Failed : <{SETTINGS.BASE.APP_NAME}> Graph Search Failed during Querying to Graph DB", str(e)))
                    response_retrieval.retrieval_log.__dict__.update(
                        **{
                            "success_flag":  False,
                            "error_message": response.detail,
                            "process_sec":   time.time() - start_at
                        }
                    )
                    return response_retrieval, response

                # Parse Output
                data_ids = [node.data_id for graph in response_graph.retrieved_data for node in graph.nodes]
                logger.info(f"Graph search returned {len(data_ids)} related data_ids")

                if not data_ids:
                    retrieved_graph_data = []
                else:
                    # Step 3: 通过AI Search根据data_ids找到相关的chunk数据
                    request_graph_data = SystemVectorRequest(
                        vector_filter=VectorFilter(
                            string_filter = VectorStringFilter(
                                data_id_filter = data_ids
                            )
                        )
                    )
                    try:
                        response_graph_data = system_query_vector(request=request_graph_data, api_call=self.api_call)

                    except Exception as e:
                        response = Response(status_code=500, detail=self.response_format.error(f"Knowledge Retrieval Failed : <{SETTINGS.BASE.APP_NAME}> Graph Search Failed during Retrieving Vectors from Graph Search Result", str(e)))
                        response_retrieval.retrieval_log.__dict__.update(
                            **{
                                "success_flag":  False,
                                "error_message": response.detail,
                                "process_sec":   time.time() - start_at
                            }
                        )
                        return response_retrieval, response

                    # Parse Output - 这些是通过graph关系找到的相关chunks
                    retrieved_graph_data = [VectorData(**_vector.__dict__) for _vector in response_graph_data.filtered_vectors]
                    logger.info(f"Found {len(retrieved_graph_data)} related chunks through graph search")
            
            # Update Log
            _model_usage += [
                ModelUsageLog(
                    service_name = 'graph_search',
                    model_name   = 'cosmosDB + aisearch',
                    process_sec  = time.time() - graph_retrieval_time
                )
            ]
            response_retrieval.retrieval_log.__dict__.update(
                **{
                    "model_usage": _model_usage,
                    "graph_citations": [
                        RawCitationLog(
                            data_id = _data.data_id,
                            data_version = _data.data_version,
                            knowledge_id = _data.knowledge_id
                        ) for _data in retrieved_graph_data
                    ]
                }
            )

            retrieved_data += retrieved_graph_data

        """
            9. Log the Citation into Output
        """
        if request.save_log:
            logger.info("Processing : Logging Citation Records into DB")
            response = self.log_qaflow_citations(data=retrieved_data, metadata=knowledge_metadata)
            logger.info(response.detail)
        
        else:
            logger.info("Processing : Skipping Logging Citation Records into DB")


        """ 
            10. Reranking 
        """
        rerank_request = RerankRequest(
            retrieved_data = retrieved_data,
            threshold = request.search_config.rerank_threshold
        )
        
        try:
            response_rerank = request_rerank(request=rerank_request, api_call=self.api_call)
            
            # Parse Output
            retrieved_data = response_rerank.reranked_data

            # Update Log
            _model_usage += [
                ModelUsageLog(
                    service_name  = 'reranking',
                    model_name    = response_rerank.rerank_model,
                    input_tokens  = response_rerank.rerank_input_tokens,
                    output_tokens = response_rerank.rerank_output_tokens,
                    process_sec   = response_rerank.rerank_time
                )
            ]
            response_retrieval.retrieval_log.__dict__.update(**{"model_usage": _model_usage})

        except Exception as e:
            response = Response(status_code=500, detail=self.response_format.error(f"Knowledge Retrieval Failed : <{SETTINGS.BASE.APP_NAME}> Reranking Failed", str(e)))
            response_retrieval.retrieval_log.__dict__.update(
                **{
                    "success_flag":  False,
                    "error_message": response.detail,
                    "process_sec":   time.time() - start_at
                }
            )
            return response_retrieval, response

        # Update Log
        response_retrieval.retrieval_log.__dict__.update(
            **{
                "rerank_citations": [
                    RawCitationLog(
                        data_id = _data.data_id,
                        data_version = _data.data_version,
                        knowledge_id = _data.knowledge_id
                    ) for _data in retrieved_data
                ]
            }
        )


        """ 
            11. Refine Knowledge Metadata 
        """
        logger.info("Processing : Retrieving Knowledge Metadata")
        knowledge_ids    = list(set([_data.knowledge_id for _data in retrieved_data]))
        knowledge_metadata = [_data for _data in knowledge_metadata if _data.knowledge_id in knowledge_ids]
        
        """ 
            12. Process Retrieved Data 
        """
        process_start_at = time.time()
        documents, citations = self.process_citation(
            knowledge_metadata=knowledge_metadata, 
            retrieved_data=retrieved_data, 
            custom_field=request.custom_field
        )

        """ 
            13. Update Final Response 
        """
        response_retrieval.retrieval_log.__dict__.update(
            **{
                "success_flag": True,
                "process_sec":  time.time() - start_at
            }
        )
        response_retrieval.__dict__.update(
            refined_queries = refined_queries,
            documents       = documents,
            citations       = citations,
            document_count  = len(documents),
            citation_count  = len(citations)
        )

        response = Response(status_code=200, detail=self.response_format.ok(f"Knowledge Retrieval Completed : <{SETTINGS.BASE.APP_NAME}> Completed Knowledge Retrieval"))
        print("Unique Keyword Time: ", unique_keyword_time)

        return response_retrieval, response


    def citation_retrieval(self, request: CitationRetrievalRequest)-> tuple[CitationRetrievalResponse, Response]:
        logger.info(f"Processing : <{SETTINGS.BASE.APP_NAME}> Starts Citation Retrieval")
        start_at = time.time()
        response_retrieval = CitationRetrievalResponse(**request.__dict__)

        """ 2. Embedding Generation """
        request_embedding = EmbeddingRequest(data_input=[request.query])
        try:
            response_embedding = request_query_embedding(request=request_embedding, api_call=self.api_call)
            processed_query = response_embedding.data_output[0]
        except Exception as e:
            response = Response(status_code=500, detail=self.response_format.error(f"Knowledge Retrieval Failed : <{SETTINGS.BASE.APP_NAME}> Embedding Failed", str(e)))
            return response_retrieval, response

        filtered_data_id = [_data.data_id for _data in request.citations]

        """ 5. Retrieve Vectors """
        vector_request = VectorSearchRequest(
            retrieval_requestid = request.retrieval_requestid,
            processed_query     = processed_query,
            query               = request.query,             
            data_ids            = filtered_data_id,
            search_method       = 'hybrid',
            search_config       = request.search_config.vector_search_config
        )
    
        try:
            response_vector = request_vector_search(request=vector_request, api_call=self.api_call)
            retrieved_data  = response_vector.retrieved_data

        except Exception as e:
            response = Response(status_code=500, detail=self.response_format.error(f"Knowledge Retrieval Failed : <{SETTINGS.BASE.APP_NAME}> Vector Search Failed", str(e)))
            return response_retrieval, response

        except:
            response = Response(status_code=500, detail=self.response_format.error(f"Knowledge Retrieval Unexpected Error : <{SETTINGS.BASE.APP_NAME}> Vector Search Failed"))
            return response_retrieval, response
        
        """ 7. Reranking """
        rerank_request = RerankRequest(
            retrieved_data = retrieved_data,
            threshold = request.search_config.rerank_threshold
        )
        
        try:
            response_rerank = request_rerank(request=rerank_request, api_call=self.api_call)
            retrieved_data = response_rerank.reranked_data

        except Exception as e:
            response = Response(status_code=500, detail=self.response_format.error(f"Knowledge Retrieval Failed : <{SETTINGS.BASE.APP_NAME}> Reranking Failed", str(e)))
            return response_retrieval, response

        """ 8. Process Retrieved Data """
        documents, citations = self.process_citation(knowledge_metadata=[], retrieved_data=retrieved_data, custom_field=request.custom_field)
        response_retrieval.citations      = citations
        response_retrieval.citation_count = len(citations)
        # for _citation in citations:
        #     print(_citation)
        #     print('\n\n')
        response = Response(status_code=200, detail=self.response_format.ok(f"Knowledge Retrieval Completed : <{SETTINGS.BASE.APP_NAME}> Completed Knowledge Retrieval"))
        
        return response_retrieval, response

    def process_citation(
            self, 
            knowledge_metadata: list[SecretKnowledge], 
            retrieved_data: list[VectorData], 
            custom_field: CustomField
        )-> tuple[list[RawDocumentObject], list[RawCitationObject]]:
        
        logger.info(f"Processing : <{SETTINGS.BASE.APP_NAME}> Formatting Citations for Final Output")

        documents = []
        citations = []
        
        for _metadata in knowledge_metadata:
            document_data = RawDocumentObject(
                knowledge_id         = _metadata.knowledge_id,
                knowledge_languages  = _metadata.knowledge_languages,
                document_name        = f"{_metadata.knowledge_name}{_metadata.knowledge_fileextension}",
                hyperlink            = _metadata.storage_directory,
                last_update_date     = str(_metadata.file_last_modified_datetime),
                pil_reference_number = _metadata.document_id,
                translation          = ""
            )
            documents.append(document_data)

            # knowledge_data = [_data for _data in retrieved_data if _data.knowledge_id == _metadata.knowledge_id]
            
            # if not knowledge_data:
            #     continue

            # """ 1. Read/Copy Document to Share Blob"""
            # share_request = BlobShareServiceRequest(source_blob_path = _metadata.storage_directory)
            # try:
            #     response_share = share_azure_blob_object(request=share_request)
            #     hyperlink = response_share.blob_sas_url
            # except:
            #     hyperlink = SETTINGS.AZST.SHARE_DOC_ERR

        # Group items by knowledge_id using a standard dictionary
        grouped_by_knowledge = {}
        for _data in retrieved_data:
            if _data.knowledge_id not in grouped_by_knowledge:
                grouped_by_knowledge[_data.knowledge_id] = []
            grouped_by_knowledge[_data.knowledge_id].append(_data)

        # Prepare the list of RawCitationObject
        citation_by_data_id = {}
        for knowledge_id, items in grouped_by_knowledge.items():
            # Group items by seq_no within the same knowledge_id using a standard dictionary
            items_by_seq = {}
            for item in items:
                if item.seq_no not in items_by_seq:
                    items_by_seq[item.seq_no] = []
                items_by_seq[item.seq_no].append(item)

            # Flatten the items sorted by seq_no
            sorted_seq_groups = sorted(items_by_seq.items())  # Sort by seq_no
            sorted_items = [item for _, group in sorted_seq_groups for item in group]

            # Find parent_id logic and create RawCitationObject
            last_title_data_id = None
            for item in sorted_items:
                parent_id = ""

                if item.seq_no > 0 and last_title_data_id:
                    # Assign parent_id if the previous item is a TITLE and seq_no matches the next one
                    parent_id = last_title_data_id
                    last_title_data_id = None

                # Update last_title_data_id if the item is a TITLE
                if item.data_type == "TITLE":
                    last_title_data_id = item.data_id 

                citaiton_source = RawCitationObject(
                    data_id          = item.data_id,
                    knowledge_id     = item.knowledge_id,
                    content          = item.raw_data,
                    source_type      = item.data_type.lower(),
                    image_table_link = item.data_url,
                    page_start       = item.page_start,
                    page_end         = item.page_end,
                    seq_no           = item.seq_no,
                    parent_id        = parent_id,
                    langauage        = item.data_languages[0] if item.data_languages else "",
                    score            = item.score if item.score else 0
                )

                citation_by_data_id[citaiton_source.data_id] = citaiton_source

        # Keep the order of retrieved_data
        for _data in retrieved_data:
            citations.append(citation_by_data_id[_data.data_id])

        return documents, citations    

    def log_qaflow_citations(self, data: list[VectorData], metadata: list[SecretKnowledge]) -> Response:
        logger.info("Processing : Logging QAFlow Citation Records into DB")

        data_to_log    = []
        exist_data_ids = []
        query = Q()

        knowledge_data = dict()
        for _data in data:
            knowledge_data[_data.knowledge_id] = _data

        try:
            # Query for Not Existing Citation Logs
            for key, value in knowledge_data.items():
                query = Q(knowledge_id=key, data_id=value.data_id, data_version=value.data_version)

                if self.api_call == True:
                    with self.db_api(collection_name=QAFlowCitationLogCollectionName):
                        db_data = QAFlowCitationLog.objects(query)
                else:
                    with self.db_func(collection_name=QAFlowCitationLogCollectionName):
                        db_data = QAFlowCitationLog.objects(query)
                
                if db_data:
                    exist_data_ids += [_data.data_id for _data in db_data]

            data_to_log = [_data for _data in data if _data.data_id not in exist_data_ids]

        # Handle common exceptions that might occur
        except (BaseException, Exception) as e:
            response = Response(status_code=500, detail=self.response_format.error(f"Common Error : Getting {DATA_OBJECT}", str(e)))
            logger.error(response.detail)

        # Handle any other exceptions that might occur
        except:
            response = Response(status_code=500, detail=self.response_format.error(f"Unexpected Error : Getting {DATA_OBJECT}"))
            logger.error(response.detail)
            
        if data_to_log:
            create_requests = []
            for _data in data_to_log:
                data_metadata = [_metadata.__dict__ for _metadata in metadata if _metadata.knowledge_id == _data.knowledge_id]
                if data_metadata:
                    data_metadata = {key: value for key, value in data_metadata[0].items() if key not in ["knowledge_id", "created_at", "updated_at"]}
                else:
                    data_metadata = dict()
                    
                create_requests.append(
                    QAFlowCitationLogCreateRequest(
                        data = QAFlowCitationLogCreate(
                            **_data.__dict__,
                            **data_metadata
                        )
                    )
                )

            batch_create = QAFlowCitationLogBatchCreateRequest(create_requests = create_requests)

            try:
                response = general_batch_create_qaflow_citation_log(request=batch_create, api_call=self.api_call)
            except Exception as e:
                response = Response(status_code=500, detail=self.response_format.error("Failed to Save QAFlow Log", str(e)))
                return response
        
        else:
            response = Response(status_code=200, detail=self.response_format.ok("All Citation Logs have already been Registered"))

        return response

    def consolidate_page_ranges(self, page_ranges: list[dict]) -> str:
        if not page_ranges:
            return ""

        # Sort by page_start and then page_end
        sorted_ranges = sorted(page_ranges, key=lambda x: (x['page_start'], x['page_end']))
        consolidated = []
        
        current_start = sorted_ranges[0]['page_start']
        current_end = sorted_ranges[0]['page_end']

        for page in sorted_ranges[1:]:
            start = page['page_start']
            end = page['page_end']

            if start <= current_end + 1:  # Check for overlap or contiguity
                current_end = max(current_end, end)  # Extend the current range
            else:
                # Append the current range
                consolidated.append((current_start, current_end))
                current_start = start
                current_end = end

        # Append the last range
        consolidated.append((current_start, current_end))

        # Format the output
        formatted_ranges = []
        for start, end in consolidated:
            if start == end:
                formatted_ranges.append(str(start))  # Single page
            else:
                formatted_ranges.append(f"{start}-{end}")  # Range

        return ", ".join(formatted_ranges)
    

    @staticmethod
    def consolidate_retrieved_vectors(data: list[list[VectorData]]) -> list[VectorData]:
        data_merger = {}

        # Flatten the list of lists into a single list
        for _vector_data in data:
            for _vector in _vector_data:
                if _vector.data_id not in data_merger:
                    data_merger[_vector.data_id] = {'vector': _vector, 'total_score': 0.0, 'count': 0}
                data_merger[_vector.data_id]['total_score'] += _vector.score
                data_merger[_vector.data_id]['count']       += 1

        # Create a new list for the merged_data results
        data_output = []
        for _data_id, _data in data_merger.items():
            average_score = _data['total_score'] / _data['count']
            if isinstance(_data.get('vector', None), VectorData):
                _data.get('vector').score = average_score
                data_output.append(_data.get('vector'))

        data_output = sorted(data_output, key=lambda x: x.score, reverse=True)

        return data_output