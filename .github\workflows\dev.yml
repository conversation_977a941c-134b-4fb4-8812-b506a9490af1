name: Build and Push

on:
  workflow_dispatch:
    
  push:
    branches:
      - develop
      - feature/deploy-dev
    paths-ignore:
      - '.github/**'
      - '.devcontainer/**'
      - 'README.md'

jobs:
  check_merge:
    runs-on: ubuntu-latest
    outputs:
      skip_workflow: ${{ steps.check.outputs.skip_workflow }}
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0  # Fetch all branches
      - id: check
        run: |
          if [[ "${{ github.ref }}" == "refs/heads/develop" ]] && \
             [[ "$(git log -1 --pretty=%P | wc -w)" == "2" ]] && \
             git log -1 --pretty=%P | grep -q "$(git rev-parse origin/feature/deploy-dev)"; then
            echo "skip_workflow=true" >> $GITHUB_OUTPUT
          else
            echo "skip_workflow=false" >> $GITHUB_OUTPUT
          fi

  build:
    needs: check_merge
    if: needs.check_merge.outputs.skip_workflow != 'true'
    runs-on: ubuntu-latest
    outputs:
      build_number: ${{ steps.build-and-push.outputs.build_number }}
    steps:
      - name: Checkout Code
        uses: actions/checkout@v4

      - name: Build and Push Docker Image
        id: build-and-push
        uses: pru-pss/pss-eta-reusable_workflows/actions/ci-docker-build-and-push-to-jfrog@main
        with:
          build_number: ${{ github.run_number }}
          registry_url: ${{ vars.JFROG_DOCKER_REGISTRY_URL }}
          image_name: ${{ vars.IMAGE_NAME }}
          jfrog_username: ${{ vars.JFROG_USERNAME }}
          jfrog_password: ${{ secrets.JFROG_PASSWORD }}
          artifactory_url: ${{ vars.JFROG_ARTIFACTORY_URL }}
          artifactory_repo: ${{ vars.JFROG_ARTIFACTORY_REPO }}

  deploy:
    needs: [check_merge, build]
    if: needs.check_merge.outputs.skip_workflow != 'true'
    runs-on: pru-phkl-all-nprd-linux-runner-01
    environment: dev
    steps: 
      - name: Checkout Code
        uses: actions/checkout@v4

      - name: Deploy to Dev
        id: deploy-to-dev
        uses: pru-pss/pss-eta-reusable_workflows/actions/cd-kubectl-set-image@main
        with:
          image_name: ${{ vars.IMAGE_NAME }}
          build_number: ${{ needs.build.outputs.build_number }}
          env: dev
          deployment_name: ${{ vars.DEPLOYMENT_NAME }}
          container_name: ${{ vars.DEPLOYMENT_NAME }}
          registry_url: ${{ vars.JFROG_DOCKER_REGISTRY_URL }}
          registry_internal_url: ${{ vars.JFROG_DOCKER_REGISTRY_INTERNAL_URL }}
          jfrog_username: ${{ vars.JFROG_USERNAME }}
          jfrog_password: ${{ secrets.JFROG_PASSWORD }}
          role_id: ${{ secrets.VAULT_AKS_ROLE_ID }}
          secret_id: ${{ secrets.VAULT_AKS_SECRET_ID }}
          kv_path: ${{ vars.VAULT_AKS_KV_PATH }}
          kv_endpoint: ${{ vars.VAULT_AKS_KV_ENDPOINT }}