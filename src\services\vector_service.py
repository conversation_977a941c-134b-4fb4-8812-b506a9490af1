from datetime import datetime
import inspect
import time
import json
import re
import math
from typing import Generator

from azure.search.documents import SearchClient
from azure.search.documents.models import VectorizedQuery 

from ..settings import SETTINGS
from ..services.inference_service import (
    InferenceServiceManager,
    InferenceInput,
    InferenceRequest
)
from ..database.vector.models.vector_models import vb_schema

from ..schemas.format import (
    ResponseFormatter,
    Response,
    ComplexEncoder
)

from ..schemas.vector import (
    KeywordSearchRequest,
    KeywordSearchResponse,
    VectorSearchRequest,
    VectorSearchResponse,
    VectorData,
    KnowledgeVectorSearchRequest,

)
from ..database.registry.services.knowledge_data import (
    DataManager as KnowledgeDataManager,
    SystemDataRequest as SystemKnowledgeRequest,
)
from ..schemas.knowledge import KnowledgeFilter, KnowledgeNumericFilter
from ..schemas.keyword import BatchMappingRequest, BatchMappingResult


from ..schemas.keyword import (
    SystemKeywordMappingRequest,
    KeywordMappingFilter,
    KeywordMappingNumericFilter,
    KeywordMappingListFilter,
    KeywordMappingModel,
    KeywordEngine,
    KeywordExtractionRequest,  
    KeywordExtractionResponse,
    MappingRequest,
    MappingResult,
    SystemKeywordMappingFuncRequest,
    KeywordMappingRequest,
    KeywordMappingCreate,
    KeywordMappingUpdate,
    KeywordMappingUpdateRequest,
    KeywordMappingCreateRequest,
    BatchMappingRequest,
    BatchMappingResult,
    SystemKeywordMappingResponse,
    KeywordExtractRequest,
    KeywordExtractReponse
)

from ..database.vector.connections.vector_connection import get_vb_func, get_vb_api
from ..routers.registry.general import (
    get_keyword_mappings_with_string,
    keyword_mappings_create,
    keyword_mappings_update,
    keyword_mappings_del,
    system_query_knowledge
    )
# API DB Session
if SETTINGS.BASE.APP_API == True:
    vb_api = get_vb_api()
    default_api_call = True
else:
    vb_api = None
    default_api_call = False

# Function DB Session
if SETTINGS.BASE.APP_FUNC == True:
    vb_func = get_vb_func
else:
    vb_func = None

from ..logger.log_handler import get_logger

logger = get_logger(__name__)


class VectorServiceManager:
    response_format = ResponseFormatter(suffix = f"{inspect.stack()[0][3]}")

    def __init__(
            self, 
            api_call:        bool,
            vb_api:          SearchClient | None = vb_api, 
            vb_func:         Generator    | None = vb_func, 
            vector_storage:  str='AISEARCH', 
            vector_location: str='azure', 
            vector_config:   dict={},
        ):
        self.api_call        = api_call
        self.vb_api          = vb_api
        self.vb_func         = vb_func
        self.vector_storage  = vector_storage
        self.vector_location = vector_location
        self.vector_config   = vector_config

    def search_by_keywords(self, request: KeywordSearchRequest) -> tuple[KeywordSearchResponse, Response]:
        logger.info(f"Processing : <{SETTINGS.BASE.APP_NAME}> Starts Keyword Search")
        start_at = time.time()
        response_keyword = KeywordSearchResponse(**request.__dict__)

        if not request.keywords:
            response = Response(
                status_code=200,
                detail=self.response_format.ok(f"Keyword Search Completed : <{SETTINGS.BASE.APP_NAME}> No Keywords Provided")
            )
            logger.info(response.detail)
            return response_keyword, response

        keyword_search_config = request.keyword_search_config or SETTINGS.RETR.DEFAULT_KEYWORD_CONFIG
        filter_str = "data_type eq 'DOCUMENT'"
        search_text = " ".join(request.keywords).replace('/', '')

        # NOTE: Instead of building a long OR chain, use search.in(...)
        if request.knowledge_ids and not SETTINGS.BASE.APP_AUTH == "group":
            knowledge_ids_str = ",".join(request.knowledge_ids)
            filter_str += f" and search.in(knowledge_id, '{knowledge_ids_str}', ',')"

        if request.blacklist: 
            escaped_keywords = [kw.replace("'", "''") for kw in request.blacklist]
            kw_conditions = [f"k eq '{kw}'" for kw in escaped_keywords]
            any_condition = " or ".join(kw_conditions)
            blacklist_filter = f"not (data_keywords/any(k: {any_condition}))"
            filter_str += f" and {blacklist_filter}"

        selected_fields = ["knowledge_id"]
        search_text = search_text.replace('/', '')

        try:
            if SETTINGS.BASE.APP_AUTH == "group":
                combined_search_results = []
                total_knowledge_id_count = len(request.knowledge_ids)
                batch_size = SETTINGS.VTDB.PERMITTED_BATCH_SIZE
                batch_count = max(1, math.ceil(total_knowledge_id_count / batch_size))
                logger.info(f"Search Knowledge_ids : Detected <{total_knowledge_id_count}> ids. Divided into <{batch_count}> Batches with <{batch_size}> Each for Processing")
                
                for batch_index in range(1, batch_count + 1):
                    logger.info(f"Searching Data: <{batch_index} / {batch_count}> Batch")

                    if request.knowledge_ids:
                        start = (batch_index - 1) * batch_size
                        end = start + batch_size
                        batch_ids = request.knowledge_ids[start:end]

                        knowledge_id_str = ",".join(batch_ids)
                        batch_filter_str  = f"{filter_str} and search.in(knowledge_id, '{knowledge_id_str}', ',')"
                    else:
                        batch_filter_str = filter_str

                    if self.api_call:
                        batch_results = self.vb_api.search(
                            search_text   = search_text,
                            select        = selected_fields,
                            query_type    = "full", # Incorporates synonyms, spell correction, fuzzy search.
                            search_fields = ["data_keywords"],  
                            top           = keyword_search_config.get("top", SETTINGS.RETR.DEFAULT_KEYWORD_LIMIT),
                            filter        = batch_filter_str
                        )
                    
                    else:
                        with self.vb_func() as vb:
                            batch_results = vb.search(
                                search_text   = search_text,
                                select        = selected_fields,
                                query_type    = "full", # Incorporates synonyms, spell correction, fuzzy search.
                                search_fields = ["data_keywords"], 
                                top           = keyword_search_config.get("top", SETTINGS.RETR.DEFAULT_KEYWORD_LIMIT), 
                                filter        = batch_filter_str
                            )
                    vector_data = [VectorData(**_result, score=_result.get("@search.score", 0.0))
                                    for _result in batch_results if _result.get("knowledge_id")]
                    combined_search_results.extend(vector_data)
                    logger.info(f"Searched Result for <{batch_index} / {batch_count}> : {len(vector_data)} data; Combined result total : {len(combined_search_results)} data")

                sorted_combined_search_results = sorted(combined_search_results, key=lambda x: x.score, reverse=True)
                retrieved_knowledge_ids = [_result.knowledge_id for _result in sorted_combined_search_results if _result.knowledge_id]
            else:
                if self.api_call:
                    results = self.vb_api.search(
                        search_text   = search_text,
                        select        = selected_fields,
                        query_type    = "full", # Incorporates synonyms, spell correction, fuzzy search.
                        search_fields = ["data_keywords"],  
                        top           = keyword_search_config.get("top", SETTINGS.RETR.DEFAULT_KEYWORD_LIMIT),
                        filter        = filter_str
                    )
                
                else:
                    with self.vb_func() as vb:
                        results = vb.search(
                            search_text   = search_text,
                            select        = selected_fields,
                            query_type    = "full", # Incorporates synonyms, spell correction, fuzzy search.
                            search_fields = ["data_keywords"], 
                            top           = keyword_search_config.get("top", SETTINGS.RETR.DEFAULT_KEYWORD_LIMIT), 
                            filter        = filter_str
                        )
                retrieved_knowledge_ids = [_result.get("knowledge_id") for _result in results if _result.get("knowledge_id", None)]
            retrieved_knowledge_ids = set(retrieved_knowledge_ids)
            response_keyword.__dict__.update(
                knowledge_ids           = list(retrieved_knowledge_ids),
                keyword_retrieval_count = len(retrieved_knowledge_ids),
                keyword_retrieval_time  = time.time() - start_at
            )


            response = Response(status_code=200, detail=self.response_format.ok(f"Keyword Search Success : <{SETTINGS.BASE.APP_NAME}> Completed Keyword Search"))
            logger.info(response.detail)

        # Handle common exceptions that might occur
        except (BaseException, Exception) as e:
            response = Response(status_code=500, detail=self.response_format.error(f"Common Error : <{SETTINGS.BASE.APP_NAME}> Encounterd Common Error during Keyword Search", str(e)))
            logger.error(response.detail)

        # Handle any other exceptions that might occur
        except:
            response = Response(status_code=500, detail=self.response_format.error(f"Unexpected Error : <{SETTINGS.BASE.APP_NAME}> Encounterd Unexpected Error during Keyword Search", str(e)))
            logger.error(response.detail)

        return response_keyword, response
            
    def search_by_vector(self, request: VectorSearchRequest) -> tuple[VectorSearchResponse, Response]:
        logger.info(f"Processing : <{SETTINGS.BASE.APP_NAME}> Starts Vector Search")
        start_at = time.time()

        response_vector = VectorSearchResponse(**request.__dict__)
        if not request.processed_query:
            response = Response(status_code=200, detail=self.response_format.ok(f"Vector Search Completed : <{SETTINGS.BASE.APP_NAME}> No Vectors Provided"))
            return response_vector, response

        if request.search_method.lower() == 'hybrid':
            retrieved_vectors, response = self.hybrid_search(request=request)
        else:
            retrieved_vectors, response = self.vector_search(request=request)

        if response.status_code >= SETTINGS.STAT.SUCC_CODE_END:
            return response_vector, response

        response_vector.__dict__.update(
            retrieved_data         = retrieved_vectors,
            vector_retrieval_count = len(retrieved_vectors),
            vector_retrieval_time  = time.time() - start_at
        )

        return response_vector, response
    
    def vector_search(self, request: VectorSearchRequest) -> tuple[list[VectorData], Response]:
        vector_data = []

        if not request.search_config:
            request.search_config = SETTINGS.RETR.DEFAULT_VECTOR_CONFIG

        filter_parts = ["data_type ne 'TITLE'"]
        if request.knowledge_ids:
            knowledge_filter = " or ".join([f"knowledge_id eq '{_knowledge_id}'" for _knowledge_id in request.knowledge_ids])
            filter_parts.append(f"({knowledge_filter})")
        filter_str = " and ".join(filter_parts)
        selected_fields = [field.name for field in vb_schema if field.name != "processed_data"]

        try:
            if request.search_config.get("knn"):
                search_config = request.search_config.get("knn")
                vector_query = VectorizedQuery(
                    vector=request.processed_query,
                    k_nearest_neighbors=search_config.get("knn"),
                    fields="processed_data"
                )

                if self.api_call:
                    results = self.vb_api.search(
                        vector_queries = [vector_query],
                        select         = selected_fields,
                        top            = search_config.get("top_k", "10"),
                        filter         = filter_str
                    )

                else:
                    with self.vb_func() as vb:
                        results = vb.search(
                            search_text=request.query,  # Added for hybrid search

                            vector_queries = [vector_query],
                            select         = selected_fields,
                            top            = search_config.get("top_k", "10"),
                            filter         = filter_str
                        )
                
                vector_data = [VectorData(**_result, score=_result.get("@search.score", 0.0)) for _result in results]
                response = Response(status_code=200, detail=self.response_format.ok(f"Vector Search Success : <{SETTINGS.BASE.APP_NAME}> Completed Vector Search"))
                logger.info(response.detail)

            else:
                response = Response(status_code=404, detail=self.response_format.error(f"Vector Search Failed : <{SETTINGS.BASE.APP_NAME}> Encountered Unknown Vector Search Configuration"))
                logger.error(response.detail)

        # Handle common exceptions that might occur
        except (BaseException, Exception) as e:
            response = Response(status_code=500, detail=self.response_format.error(f"Common Error : <{SETTINGS.BASE.APP_NAME}> Encounterd Common Error during Vector Search", str(e)))
            logger.error(response.detail)

        # Handle any other exceptions that might occur
        except:
            response = Response(status_code=500, detail=self.response_format.error(f"Unexpected Error : <{SETTINGS.BASE.APP_NAME}> Encounterd Unexpected Error during Vector Search", str(e)))
            logger.error(response.detail)

        return vector_data, response

    def hybrid_search(self, request: VectorSearchRequest) -> tuple[list[VectorData], Response]:
        vector_data = []

        if not request.search_config:
            request.search_config = SETTINGS.RETR.DEFAULT_HYBRID_CONFIG

        data_length_min = 10
        if len(request.query) <= data_length_min:
            data_length_max = len(request.query) * 3
            filter_parts = [ f"data_length gt {data_length_max}"]
        else:
            filter_parts = []
        
        if request.knowledge_ids:
            knowledge_ids_str = ",".join(request.knowledge_ids)
            knowledge_filter = f"search.in(knowledge_id, '{knowledge_ids_str}', ',')"
            filter_parts.append(knowledge_filter)

        
        if request.data_ids:
            data_filter = " or ".join([f"data_id eq '{_data_id}'" for _data_id in request.data_ids])
            filter_parts.append(f"({data_filter})") 
        
        filter_parts.append("data_type ne 'DOCUMENT'")

        filter_str = " and ".join(filter_parts)
        selected_fields = [field.name for field in vb_schema if field.name != "processed_data"]

        try:
            if request.search_config.get("knn"):
                search_config = request.search_config.get("knn")
                vector_query = VectorizedQuery(
                    vector=request.processed_query,
                    k_nearest_neighbors=search_config.get("knn"),
                    fields="processed_data"
                )

                if self.api_call:
                    results = self.vb_api.search(
                        search_text    = request.query,
                        vector_queries = [vector_query],
                        select         = selected_fields,
                        top            = search_config.get("top_k", "50"),
                        filter         = filter_str,
                        # query_type     = "semantic",
                        # SemanticConfiguration   = "my-semantic-config",
                        # scoring_profile         = "boostLongDocs"
                    )

                else:
                    with self.vb_func() as vb:
                        results = vb.search(
                            search_text    = request.query,
                            vector_queries = [vector_query],
                            select         = selected_fields,
                            top            = search_config.get("top_k", "50"),
                            filter         = filter_str,
                            # query_type     = "semantic",
                            # SemanticConfiguration   = "my-semantic-config",
                            # scoring_profile         = "boostLongDocs"
                        )

                #for _results in results:
                #   if _result.get(data_type) == "TITLE":
                #       seq_no =  _result.get(data_type)+1
                #       kid    =  _result.get(knowledge_id)
                #       next_results = vb.search(
                        #     
                        #     
                        #     select         = selected_fields,
                        #    
                        #     filter         = "seq_no eq {seq_no} and knowledeg_id eq {knowledge_id}"
                        # )
                        
                        # result.append(next_results)
                        # result.delete(_results)

                ##
                                # Collect initial results into a list
                initial_results = list(results)

                # Process results to handle TITLEs
                for result in initial_results:
                    if result.get('data_type') != 'TITLE':
                        vector_data.append(VectorData(**result, score=result.get("@search.score", 0.0)))
                    else:
                        vector_data.append(VectorData(**result, score=result.get("@search.score", 0.0)))
 
                        # Handle TITLE: find the next non-TITLE with same knowledge_id
                        knowledge_id = result.get('knowledge_id')
                        seq_no = result.get('seq_no') + 1  # Increment sequence number
                        score = result.get("@search.score", 0.0)  # Preserve the original score
                        max_attempts = 3  # Prevent infinite loops

                        for _ in range(max_attempts):
                            if self.api_call:
                                next_results = self.vb_api.search(
                                    select=selected_fields,
                                    filter=f"knowledge_id eq '{knowledge_id}' and seq_no eq {seq_no}"
                                ) 
                            else:
                                with self.vb_func() as vb:
                                    next_results = vb.search(
                                    select=selected_fields,
                                    filter=f"knowledge_id eq '{knowledge_id}' and seq_no eq {seq_no}"
                                ) 
                            next_result_list = list(next_results)
                            if next_result_list:
                                next_item = next_result_list[0]
                                if next_item.get('data_type') != 'TITLE':
                                    # Found a non-TITLE, append it with the original score
                                    vector_data.append(VectorData(**next_item, score=score))
                                    break
                                else:
                                    # Still a TITLE, increment seq_no and continue
                                    seq_no += 1
                            else:
                                # No next item found, stop searching
                                break
                        else:
                            # Max attempts reached without finding a non-TITLE
                            logger.warning(
                                f"Could not find non-TITLE item after {max_attempts} attempts "
                                f"for knowledge_id {knowledge_id} starting from seq_no {result['seq_no']}"
                            )
                response = Response(status_code=200, detail=self.response_format.ok(f"Vector Search Success : <{SETTINGS.BASE.APP_NAME}> Completed Hybrid Search. Retrieved <{len(vector_data)}> Vectors"))
                logger.info(response.detail)

            else:
                response = Response(status_code=404, detail=self.response_format.error(f"Hybrid Search Failed : <{SETTINGS.BASE.APP_NAME}> Encountered Unknown Hybrid Search Configuration"))
                logger.error(response.detail)

        # Handle common exceptions that might occur
        except (BaseException, Exception) as e:
            response = Response(status_code=500, detail=self.response_format.error(f"Common Error : <{SETTINGS.BASE.APP_NAME}> Encounterd Common Error during Hybrid Search", str(e)))
            logger.error(response.detail)

        # Handle any other exceptions that might occur
        except:
            response = Response(status_code=500, detail=self.response_format.error(f"Unexpected Error : <{SETTINGS.BASE.APP_NAME}> Encounterd Unexpected Error during Hybrid Search", str(e)))
            logger.error(response.detail)

        return vector_data, response


    def get_chunks_by_knowledge_id(self, request: KnowledgeVectorSearchRequest) -> tuple[VectorSearchResponse, Response]:
        knowledge_id = request.knowledge_ids
        logger.info(f"Fetching all chunks for knowledge_id: {knowledge_id}")
        start_time = time.time()

        response_vector = VectorSearchResponse(
            retrieval_requestid=request.retrieval_requestid,
            retrieved_data=[],
            vector_retrieval_count=0,
            vector_retrieval_time=0.0
        )

        filter_str = f"knowledge_id eq '{knowledge_id}'"
        selected_fields = [field.name for field in vb_schema if field.name != "processed_data"]

        try:
            if self.api_call:
                results = self.vb_api.search(
                    search_text="*",
                    filter=filter_str,
                    select=selected_fields,
                    query_type="full"
                )
            else:
                with self.vb_func() as vb:
                    results = vb.search(
                        search_text="*",
                        filter=filter_str,
                        select=selected_fields,
                        query_type="full"
                    )

            # Convert search results into a list of VectorData objects.
            vector_data = [
                VectorData(**_result, score=_result.get("@search.score", 0.0))
                for _result in results if _result.get("knowledge_id")
            ]

            # Update the response with the retrieved results.
            response_vector.__dict__.update(
                retrieved_data=vector_data,
                vector_retrieval_count=len(vector_data),
                vector_retrieval_time=time.time() - start_time
            )

            # Response object for a successful search.
            response = Response(
                status_code=200,
                detail=self.response_format.ok(f"Successfully retrieved {len(vector_data)} chunks for knowledge_id: {knowledge_id}")
            )
            logger.info(response.detail)

        except Exception as e:
            # Handle exceptions and return an error response.
            response = Response(
                status_code=500,
                detail=self.response_format.error(f"Error retrieving chunks for knowledge_id: {knowledge_id}", str(e))
            )
            logger.error(response.detail)

        return response_vector, response
        
    def map_keyword_knowledge_prompt(self, retrieved_data: list[VectorData]):

        retrieved_data_str = ""
        for _data in retrieved_data:
            retrieved_data_str += f"{{'data_id': {_data.data_id}, 'data_context': {_data.raw_data}}}\n"
        default_system_prompt = f"""[System]
            YOU ARE THE WORLD'S BEST PRODUCT SYNONYM MAPPING ASSISTANT, SPECIALIZING IN INSURANCE PRODUCTS. YOUR TASK IS TO ANALYZE THE GIVEN DOCUMENT AND IDENTIFY GROUPS OF SYNONYMS THAT REFER TO THE SAME INSURANCE PRODUCT. FOR EXAMPLE, "EGC", "Evergreen Wealth Multi-Currency Plan", "雋富多元貨幣計劃", AND "售富多元貨計劃" ALL REFER TO THE SAME PRODUCT AND SHOULD BE MAPPED TO A STANDARD KEYWORD.

            ### INSTRUCTIONS

            1. **Document Analysis**:
            - Carefully read the input document.
            - Identify all insurance products and their various synonyms.
            - Recognize that synonyms may appear in different languages or formats but still refer to the same insurance product.
            - Use contextual clues (e.g., definitions, descriptions, or similar usage) to confirm that terms are synonyms.

            2. **Mapping Guidelines**:
            - Map each group of synonyms to a standard product keyword.
            - Ensure mappings are precise and based only on the document’s content.
            - Do not group terms unless they clearly refer to the same insurance product; avoid assumptions about unclear or ambiguous terms.

            3. **Standard Keyword Selection**:
            - Choose the standard keyword as the most commonly used term in the document or the official product name if identifiable.
            - If unsure, select the term that appears first in the document.

            4. **Synonym Completeness**:
            - Only consider terms that represent the *entire* meaning of the product name as synonyms.
            - Partial overlaps or words like “Plan,” “the plans,” “Supplement —,” “Benefit Schedule,” “he,” “she,” “it,” “they,” “them,” which do not represent the full product name on their own, **must not** be merged into the same synonym group.
            5. **Output Format**:
            - MUST provide your response in JSON format with a single key "response", whose value is an array of arrays.
            - Each sub-array starts with the standard keyword followed by all its synonyms found in the document.
            - Example:
                ```json
                {{
                    "response": [
                        ["standard_keyword", "synonym1", "synonym2"],
                        ["another_standard_keyword", "synonymA", "synonymB"]
                    ]
                }}
                ```

            6. **Additional Requirements**:
            - Do not add explanations or commentary outside the JSON output.
            - Ensure all mappings accurately reflect the synonyms present in the document.

            [Input Document]
            "{retrieved_data_str}"

            [Output]
            Provide your answer strictly in the JSON format specified above.
            """
        return default_system_prompt


    def jsonlize(self, text: str) -> dict:
        match = re.search(r'```json\s*(.*?)\s*```', text, re.DOTALL)
        if match:
            json_str = match.group(1)
        else:
            json_str = text  
        try:
            data = json.loads(json_str)
            return data
        except json.JSONDecodeError:
            raise json.JSONDecodeError
        

    def map_keyword_knowledge(self,  request:MappingRequest)->tuple[MappingResult,Response]:   
        mapping_response = MappingResult(
            mapping_requestid = request.mapping_requestid,
            knowledge_id=request.knowledge_id,
            mapping_result=[]
        )
        _request = KnowledgeVectorSearchRequest(knowledge_ids=request.knowledge_id)
        try:
            retrieved_data, response = self.get_chunks_by_knowledge_id(request=_request)
        except Exception as e:
            logger.error(f"Error retrieving chunks by knowledge ID: {str(e)}")
            response = Response(
                status_code=500,
                detail=self.response_format.error(f"Failed to retrieve data for knowledge ID: {request.knowledge_id}")
            )
            return mapping_response, response
        
        prompt = self.map_keyword_knowledge_prompt(retrieved_data= retrieved_data.retrieved_data)
        logger.info(f"Processing : <{SETTINGS.BASE.APP_NAME}> Started Mapping Keyword")
        retry_max_no = SETTINGS.INFR.RETRY_LIMIT
        retry_count = 1
        result ={}
        while retry_count <= retry_max_no:
            logger.info(f"Calling Inference Service - Attempt: <{retry_count} / {retry_max_no}>")
            _request = InferenceRequest(
                system_prompt=prompt,
                input=InferenceInput(text=""),
                config=None
            )
            response_inference, response = InferenceServiceManager(
                    api_call=self.api_call
                ).inference_engine(request=_request)

            if response.status_code >= SETTINGS.STAT.SUCC_CODE_END:
                logger.error("Inference Issue. Retrying")
                retry_count += 1
                continue

            try:
                result = self.jsonlize(response_inference.inference_output.text)
                logger.info("Successfully processed jsonlize response")
                break
            except Exception as e:
                if retry_count >= retry_max_no:
                    logger.error(f"GenAI Formatting Issue. Max retries reached: {str(e)}")
                    break
                logger.error(f"GenAI Formatting Issue. Retrying: {str(e)}")
                retry_count += 1

        response = Response(status_code=200, detail=self.response_format.ok(f"Refinement Completed : <{SETTINGS.BASE.APP_NAME}> Successfully Refined User Query"))
        mapping_list = result.get("response", [])
        def remove_duplicates(seq: list[str]) -> list[str]:
            seen = set()
            output = []
            for item in seq:
                if item not in seen:
                    seen.add(item)
                    output.append(item)
            return output

        # Iterate through each inner list and remove duplicates.
        cleaned_mapping_list = []
        for inner_list in mapping_list:
            if isinstance(inner_list, list):
                cleaned_list = remove_duplicates(inner_list)
                cleaned_mapping_list.append(cleaned_list)
            else:
                # In case it's not a list, append as is.
                cleaned_mapping_list.append(inner_list)

        # Update mapping_response with the cleaned mapping list.
        mapping_response.__dict__.update(mapping_result=cleaned_mapping_list)

        return mapping_response, response

    def batch_create_mapping(self, request: MappingResult) -> tuple[MappingResult, Response]:
        """
        Process a MappingResult to create or update keyword mappings in the database.
        
        Args:
            request (MappingResult): Contains mapping_result, a list of synonym groups.
        
        Returns:
            tuple[MappingResult, Response]: The input request and a success response.
        """
        # Initialize KMDataManager for database operations
        mapping_result = request.mapping_result
        for group in mapping_result:
            intersecting_mappings = {}
            for keyword in group:
                search_request = SystemKeywordMappingFuncRequest(search_str=keyword)
                try:
                    # get_keyword_mappings_with_string now returns a list of objects 
                    # with actual 'keywords' attributes
                    response_data = get_keyword_mappings_with_string(search_request)
                except Exception as e:
                    logger.error(f"Failed to search mappings for keyword: {keyword}, error: {str(e)}")
                    return request, Response(status_code=500, detail=str(e))

                # response_data is SystemKeywordMappingFuncReponse
                if response_data.filtered_data:
                    for mapping in response_data.filtered_data:
                        # This no longer fails because 'mapping' is a real object with .keywords
                        if any(kw.upper() == keyword.upper() for kw in mapping.keywords):
                            if mapping.mapping_id not in intersecting_mappings:
                                intersecting_mappings[mapping.mapping_id] = mapping

            if not intersecting_mappings:
                # No existing mappings found; create a new one
                create_data = KeywordMappingCreate(keywords=group)
                create_request = KeywordMappingCreateRequest(data=create_data)
                try:
                    create_response = keyword_mappings_create(request=create_request)
                except Exception as e:
                    logger.error(f"Failed to create mapping for group: {group}, error: {create_response.detail}")
                    return request, create_response
            else:
                # Merge existing mappings with the new group
                all_keywords = set(group)
                for mapping in intersecting_mappings.values():
                    all_keywords.update(mapping.keywords)
                new_keywords = list(all_keywords)

                # Select one mapping to update and drop the rest
                mapping_ids = list(intersecting_mappings.keys())
                update_id = mapping_ids[0]
                update_data = KeywordMappingUpdate(keywords=new_keywords)
                update_request = KeywordMappingUpdateRequest(mapping_id=update_id, update_data=update_data)
                try:
                    update_response =keyword_mappings_update(request=update_request)
                except Exception as e:

                    logger.error(f"Failed to update mapping {update_id}, error: {update_response.detail}")
                    return request, update_response

                # Drop the other overlapping mappings
                for mapping_id in mapping_ids[1:]:
                    drop_request = KeywordMappingRequest(mapping_id=mapping_id)
                    try:
                        drop_response = keyword_mappings_del(request=drop_request)

                    except Exception as e:

                        logger.error(f"Failed to drop mapping {mapping_id}, error: {drop_response.detail}")
                        return request, drop_response

        # Return the original request with a success response
        response = Response(status_code=200, detail=self.response_format.ok("Success: Batch created/updated mappings"))
        return request, response
    

    def batch_create_map_keyword_knowledge(self, request: BatchMappingRequest) -> tuple[BatchMappingResult, Response]:
        """
        Process a batch of MappingRequests to generate and store keyword mappings.

        Args:
            request (BatchMappingRequest): Contains a list of MappingRequest objects, each with a knowledge_id.

        Returns:
            tuple[BatchMappingResult, Response]: A BatchMappingResult containing all MappingResults and a Response.
        """
        logger.info(f"Processing : <{SETTINGS.BASE.APP_NAME}> Started Batch Mapping Keyword Knowledge")
        start_time = time.time()

        # Initialize the response object
        batch_result = BatchMappingResult(mapping_knowledge_result=[])

        # Process each MappingRequest
        for id in request.knowledge_id:
            # Step 1: Generate MappingResult using map_keyword_knowledge
            mapping_request = MappingRequest(
                knowledge_id=id
            )
            mapping_result, response = self.map_keyword_knowledge(mapping_request)
            
            # Check if mapping generation failed
            if response.status_code >= SETTINGS.STAT.SUCC_CODE_END:
                logger.error(f"Failed to map keywords for knowledge_id: {mapping_request.knowledge_id}")
                return batch_result, response

            # Step 2: Create or update mappings in the database using batch_create_mapping
            mapping_result, response = self.batch_create_mapping(mapping_result)
            
            # Check if mapping creation/update failed
            if response.status_code >= SETTINGS.STAT.SUCC_CODE_END:
                logger.error(f"Failed to create/update mappings for knowledge_id: {mapping_request.knowledge_id}")
                return batch_result, response

            # Add the MappingResult to the batch result
            batch_result.mapping_knowledge_result.append(mapping_result)

        # Create a success response
        response = Response(
            status_code=200,
            detail=self.response_format.ok(
                f"Batch Mapping Completed : <{SETTINGS.BASE.APP_NAME}> Successfully Processed {len(request.knowledge_id)} Knowledge IDs in {time.time() - start_time:.2f} seconds"
            )
        )
        logger.info(response.detail)

        return batch_result, response
    def batch_map_active_knowledge(self) -> tuple[BatchMappingResult, Response]:
        """
        1) Query all Knowledge with knowledge_status >= 1
        2) Extract their knowledge_id
        3) Call batch_create_map_keyword_knowledge(...) on that list
        """
        logger.info("Started batch_map_active_knowledge")

        # 1) build system‐level request to fetch all active knowledge
        sys_req = SystemKnowledgeRequest(
            data_filter=KnowledgeFilter(
                numeric_filter=KnowledgeNumericFilter(knowledge_status_min=1)
            )
        )
        try:
        # 2) perform the query
            response_data, resp_query = KnowledgeDataManager(
                api_call=self.api_call
            ).query_data_by_system(request=sys_req)

        # if the query itself errored, just bubble up
        except Exception as e:
            logger.error("Failed to fetch active knowledge")
            return BatchMappingResult(mapping_knowledge_result=[]), resp_query

        # 3) extract knowledge_ids
        unique_ids = {
            kd.knowledge_id
            for kd in response_data.filtered_data
            if kd.knowledge_id
        }
        knowledge_ids = list(unique_ids)
        if not knowledge_ids:
            # nothing to do
            empty = BatchMappingResult(mapping_knowledge_result=[])
            ok = Response(
                status_code=200,
                detail=self.response_format.ok("No active knowledge found to map")
            )
            return empty, ok

        # 4) delegate to your existing batch_create_map_keyword_knowledge
        batch_req = BatchMappingRequest(knowledge_id=knowledge_ids)
        batch_result, resp_map = self.batch_create_map_keyword_knowledge(request=batch_req)

        return batch_result, resp_map
    
    def extract_keyword_prompt(self, retrieved_data: list[VectorData]):

        retrieved_data_str = ""
        for _data in retrieved_data:
            retrieved_data_str += f"{{'data_id': {_data.data_id}, 'data_context': {_data.raw_data}}}\n"
        default_system_prompt = f"""[System]
        YOU ARE AN EXPERT AT EXTRACTING KEYWORDS FROM DOCUMENTS. YOUR TASK IS TO ANALYZE THE GIVEN DOCUMENT AND IDENTIFY ALL MEANINGFUL TERMS, WHICH ARE SPECIFIC NOUNS OR PHRASES THAT CARRY SIGNIFICANT MEANING IN THE CONTEXT, SUCH AS PRODUCT NAMES, KEY CONCEPTS, OR IMPORTANT ENTITIES.

            ###INSTRUCTIONS

            1. **Document Analysis**
                - Carefully read the input document.
                - Must Identify all meaningful terms that are specific nouns or phrases used in the text.
                - Ensure that the terms are exact matches from the document; do not modify or paraphrase them.
            2. **Extraction Guidelines**
                - Extract terms that represent complete concepts or entities.
                - Exclude generic words (e.g., "the," "and," "or"), pronouns (e.g., "he," "she," "it"), and partial phrases that do not convey the full meaning on their own (e.g., "Plan" instead of "Evergreen Wealth Multi-Currency Plan").
                - Do not group terms based on synonyms or variations; list each term individually as it appears in the text.
                - If a keyword contains a slash (`/`), split it into separate terms. For example, if the text has "Data A/B," extract it as `"Data A"` and `"Data B"`.
                - If the phrase includes a monetary value with a currency symbol or unit, only extract the currency portion. For instance, if the document says "HKD 10,000," only extract `"HKD"`, not the numeric value.

            3. **Output Format**
                - Provide your response in JSON format with a single key "data_keywords", whose value is an array of the extracted keywords.
                - Example:
                    
                    ```json
                    {{
                        "data_keywords": [
                            "keyword1", "keyword2", "keyword3", "keyword4"
                        ]
                    }}
                    ```
                    
            4. **Additional Requirements**
                - Do not add explanations or commentary outside the JSON output.
                - Ensure all keywords are accurately extracted from the document.

            [Input Document]
            "{retrieved_data_str}"

            [Output]
            
            Provide your answer strictly in the JSON format specified above.

            """
        return default_system_prompt

    def extract_keyword_knowledge(self, request: KeywordExtractRequest) -> tuple[KeywordExtractReponse, Response]:
        extract_response = KeywordExtractReponse(
            extract_id=request.extract_id,
            knowledge_id=request.knowledge_id,
            data_keywords=[]
        )
        _request = KnowledgeVectorSearchRequest(knowledge_ids=request.knowledge_id)
        try:
            retrieved_data, response = self.get_chunks_by_knowledge_id(request=_request)
        except Exception as e:
            logger.error(f"Error retrieving chunks by knowledge ID: {str(e)}")
            response = Response(
                status_code=500,
                detail=self.response_format.error(f"Failed to retrieve data for knowledge ID: {request.knowledge_id}")
            )
            return extract_response, response

        # Prepare your system prompt (omitted, as this part is unchanged)
        prompt = self.extract_keyword_prompt(retrieved_data=retrieved_data.retrieved_data)
        logger.info(f"Processing : <{SETTINGS.BASE.APP_NAME}> Started Mapping Keyword")

        retry_max_no = SETTINGS.INFR.RETRY_LIMIT
        retry_count = 1
        result = {}

        while retry_count <= retry_max_no:
            logger.info(f"Calling Inference Service - Attempt: <{retry_count} / {retry_max_no}>")

            _request = InferenceRequest(
                system_prompt=prompt,
                input=InferenceInput(text=""),
                config=None
            )
            response_inference, response = InferenceServiceManager(api_call=self.api_call).inference_engine(request=_request)

            if response.status_code >= SETTINGS.STAT.SUCC_CODE_END:
                logger.error("Inference Issue. Retrying")
                retry_count += 1
                continue

            try:
                # Attempt to parse the output as JSON
                result = self.jsonlize(response_inference.inference_output.text)
                logger.info("Successfully processed jsonlize response")
                break
            except Exception as e:
                if retry_count >= retry_max_no:
                    logger.error(f"GenAI Formatting Issue. Max retries reached: {str(e)}")
                    break
                logger.error(f"GenAI Formatting Issue. Retrying: {str(e)}")
                retry_count += 1

        # Build a successful response
        response = Response(
            status_code=200,
            detail=self.response_format.ok(
                f"Refinement Completed : <{SETTINGS.BASE.APP_NAME}> Successfully Refined User Query"
            )
        )



        extract_list = result.get("data_keywords", [])

        seen = set()
        unique_keywords = []

        for item in extract_list:
            if isinstance(item, list):
                for keyword in item:
                    if keyword not in seen:
                        seen.add(keyword)
                        unique_keywords.append(keyword)
            else:
                if item not in seen:
                    seen.add(item)
                    unique_keywords.append(item)

        extract_response.data_keywords = unique_keywords

        return extract_response, response