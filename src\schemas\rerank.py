from pydantic import BaseModel, Field
import uuid
from datetime import datetime, timedelta

from ..settings import SETTINGS

from ..schemas.vector import VectorData

""" Reranking Request """
class RerankRequest(BaseModel):
    rerank_requestid:   str=Field(default_factory=lambda: str(uuid.uuid4()))
    system_prompt:      str=''
    query:              str=Field(default='', description="Query")
    retrieved_data:     list[VectorData] = Field(default=[], description="List of CitationObjects")
    threshold:          float=Field(default=SETTINGS.RETR.DEFAULT_RERANK_THRESHOLD, description="Threshold for reranking")
    config:             dict | None = None

""" Reranking Response """
class RerankResponse(BaseModel):
    rerank_requestid:     str
    reranked_data:        list[VectorData] = Field(default=[], description="List of CitationObjects")
    rerank_model:         str=''
    rerank_input_tokens:  int=-1
    rerank_output_tokens: int=-1
    rerank_count:         int=0
    rerank_time:          float=0.0
