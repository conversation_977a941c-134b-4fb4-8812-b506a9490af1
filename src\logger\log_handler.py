from sqlalchemy.orm import Session
import os
import sys

import logging.handlers
import logging
import logging.config

from ..settings import SETTINGS

console_format = '[ %(asctime)-8s ] %(levelname)-12s: %(message)s'
logger_format  = '%(asctime)-8s | %(levelname)-12s | %(module)-20s | %(name)-20s | %(funcName)-20s | %(lineno)-4d | %(message)-20s |'
logger_datefmt = '%Y-%m-%d %H:%M:%S'

logging_config = {
    'version': 1,
    'disable_existing_loggers': False,
    'loggers': {
        'root': {
            'level': 'DEBUG',
            'handlers': ['consoleHandler'],
        },
        'appLogger': {
            'level': 'DEBUG',
            'handlers': ['consoleHandler'],
            'propagate': False,
        },
    },
    'handlers': {
        'consoleHandler': {
            'class': 'logging.StreamHandler',
            'level': 'INFO',
            'formatter': 'consoleformatter',
            'stream': sys.stdout,
        },
    },
    'formatters': {
        'simpleFormatter': {
            'format':  logger_format,
            'datefmt': logger_datefmt,
        },
        'consoleformatter': {
            'format':  console_format,
            'datefmt': logger_datefmt,
        },
    },
}

if SETTINGS.LOG.SAVE_FILE:
    log_file_root = os.path.join(SETTINGS.LOG.FILE_RDIR, SETTINGS.LOG.FILE_SDIR)
    log_file_dir  = os.path.join(log_file_root, SETTINGS.LOG.FILE_NAME)
    os.makedirs(log_file_root, exist_ok=True)

    class LogFileHanlder:

        def __init__(
                self, 
                _log_file_dir: str=log_file_dir, 
                _max_bytes:    int=SETTINGS.LOG.FILE_BYTE, 
                _backup_count: int=SETTINGS.LOG.FILE_NUM
            ):
            self.log_file_dir = _log_file_dir
            self.max_bytes    = _max_bytes
            self.backup_count = _backup_count
            self.handler      = self._create_handler()

        def _create_handler(self):
            """Create and configure the rotating file handler."""
            handler = logging.handlers.RotatingFileHandler(
                self.log_file_dir,
                mode='a',
                maxBytes=self.max_bytes,
                backupCount=self.backup_count
            )
            formatter = logging.Formatter(
                logger_format,
                datefmt=logger_datefmt
            )
            handler.setFormatter(formatter)
            return handler

        def get_handler(self):
            """Return the configured handler."""
            return self.handler
        
if SETTINGS.LOG.SAVE_DB and SETTINGS.LOG.FORM.upper() in SETTINGS.LOG.DB_FORM:
    from ..logger.log_connection import get_db
    from ..logger.log_model import Logs

    class LogDBHanlder(logging.Handler):

        def __init__(self, db_session: Session = get_db):
            logging.Handler.__init__(self)
            self.db_session = db_session

        def emit(self, record):
            db_log = Logs(
                log_level  = str(record.levelname),
                log_module = str(record.module),
                log_func   = str(record.funcName),
                log_msg    = str(record.msg)
            )

            with self.db_session() as db:
                db.add(db_log)
                db.commit()
                db.refresh(db_log)
                self.rotate_log_records(db)

        @staticmethod
        def rotate_log_records(session: Session):
            
            record_count = session.query(Logs).count() # Get the count of log records

            # If the count exceeds limit, delete the oldest records
            if record_count > SETTINGS.LOG.LIMIT:
                cutoff_timestamp = session.query(Logs.log_at) \
                                        .order_by(Logs.log_at.asc()) \
                                        .offset(SETTINGS.LOG.LIMIT - 1) \
                                        .first()[0]
                
                # Delete records older than the cutoff timestamp
                session.query(Logs) \
                       .filter(Logs.log_at < cutoff_timestamp) \
                       .delete()
                session.commit()


# logger saving app.log and logdb
def get_logger(name):
    # logging.config.fileConfig(f'{LOG_CONFIG_FILE}', disable_existing_loggers=True)

    logging.config.dictConfig(logging_config)
    logger = logging.getLogger(name)

    # Override Log Level in Logger Config
    if SETTINGS.LOG.LEVEL:
        # Retrieve Log Level
        if SETTINGS.LOG.LEVEL.upper() == "INFO":
            logger.setLevel(logging.INFO)
        elif SETTINGS.LOG.LEVEL.upper() == "DEBUG":
            logger.setLevel(logging.DEBUG)
        elif SETTINGS.LOG.LEVEL.upper() == "NOTSET":
            logger.setLevel(logging.NOTSET)
        elif SETTINGS.LOG.LEVEL.upper() == "ERROR":
            logger.setLevel(logging.ERROR)
        elif SETTINGS.LOG.LEVEL.upper() in ["WARN", "WARNING"]:
            logger.setLevel(logging.WARNING)
        elif SETTINGS.LOG.LEVEL.upper() in ["CRITICAL", "FATAL"]:
            logger.setLevel(logging.CRITICAL)
        else:
            logger.setLevel(logging.INFO)

    # Check if Logging into File
    if SETTINGS.LOG.SAVE_FILE:
        logfile = LogFileHanlder().get_handler()
        logger.addHandler(logfile)

    # Check if Logging into DB
    if SETTINGS.LOG.SAVE_DB and SETTINGS.LOG.FORM.upper() in SETTINGS.LOG.DB_FORM:
        logdb = LogDBHanlder()
        logger.addHandler(logdb)
    
    return logger

