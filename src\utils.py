from fastapi import HTTPException

from .settings import SETTINGS

from .schemas.format import Response

# Router Response Handler
def router_response_handler(response: Response, api_call: bool):
    if response.status_code >= SETTINGS.STAT.SUCC_CODE_END:
        if api_call == True:
            raise HTTPException(status_code=response.status_code, detail=response.detail)
        else:
            raise Exception(response.detail)