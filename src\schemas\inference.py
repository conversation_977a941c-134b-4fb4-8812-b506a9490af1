from pydantic import BaseModel, Field
from typing import Literal
import uuid
from datetime import datetime, timezone

class ChatHistoryItem(BaseModel):
    session_id: str = Field(description="Session ID of the chat history.", example = "123-123-123")
    role:       Literal['human', 'chatbot'] = Field(description="Role of the chat conversation. Either human or chatbot", example="human")
    content:    str = Field(description="Content of the chat history.", example="Hi, this is from hu0man.")
    unix_time:  int = Field(description="Unix timestamp of the chat history.", example = 1734408157)

""" 
    Inference Request 
"""
class InferenceModel(BaseModel):
    model_id:         str  | None = None
    model_version:    int  | None = None
    model_name:       str  | None = None

    model_type:       str  | None = None
    model_cluster:    str  | None = None
    model_location:   str  | None = None
    
    model_host:       str  | None = None
    model_port:       str  | None = None
    model_api:        str  | None = None
    model_engine:     str  | None = None
    model_base:       str  | None = None
    model_parameters: dict | None = None
    model_secrets:    dict | None = None
    model_file_path:  str  | None = None
    model_key:        str  | None = None

class InferenceInput(BaseModel):
    text:    str | None = None
    image:   list[str] | None = []
    audio:   list[str] | None = []
    video:   list[str] | None = []

class InferenceOutput(BaseModel):
    text:    str | None = None
    image:   list[str] | None = []
    audio:   list[str] | None = []
    video:   list[str] | None = []
    
class InferenceMetrics(BaseModel):
    inference_code:     str=''
    inference_reason:   str='FAIL'
    inference_function: str | None = None
    input_tokens:       int=0
    output_tokens:      int=0
    input_per_second:   float=0.0
    output_per_second:  float=0.0
    first_token_time:   float=0.0
    model_load_time:    float=0.0
    inference_at:       datetime=Field(default_factory=lambda: datetime.now(timezone.utc))
    inference_time:     float=0.0

class InferenceResult(BaseModel):
    inference_output:  InferenceOutput=InferenceOutput()
    inference_metrics: InferenceMetrics=InferenceMetrics()

class InferenceRequest(BaseModel):
    inference_requestid: str=Field(default_factory=lambda: str(uuid.uuid4()))

    user_requestid:      str=Field(default_factory=lambda: str(uuid.uuid4()))
    user_id:             str | None = None
    user_name:           str | None = None

    caller_id:           str | None = None
    caller_name:         str | None = None
    caller_type:         str | None = None
    
    system_prompt:       str | None = None
    model:               InferenceModel | None = None
    input:               InferenceInput
    chat_history:        list[ChatHistoryItem] | None = []
    config:              dict | None = None

    request_at:          datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    
class InferenceResponse(BaseModel):
    inference_requestid: str
    inference_output:    InferenceOutput  | None = None
    inference_metrics:   InferenceMetrics | None = None
    response_at:         datetime | None = None