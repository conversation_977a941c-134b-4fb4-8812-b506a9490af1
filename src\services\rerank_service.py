from datetime import datetime
import time
import inspect
import httpx
import json

from ..settings import SETTINGS

from openai import AzureOpenAI

from ..schemas.format import (
    ResponseFormatter,
    Response,
)

from ..services.inference_service import (
    InferenceServiceManager,
    InferenceInput,
    InferenceRequest
)

from ..schemas.rerank import (
    RerankRequest,
    RerankResponse
)

from ..schemas.vector import (
    VectorData
)

from ..logger.log_handler import get_logger

logger = get_logger(__name__)


class RerankServiceManager:
    response_format = ResponseFormatter(suffix = f"{inspect.stack()[0][3]}")

    default_system_prompt = ""

    def __init__(self, api_call: bool):
        self.api_call = api_call

    def rerank_prompt(self, query: str, retrieved_data: list[VectorData]) -> str:
        # Construct retrieved_data as a string
        retrieved_data_str = ""
        for _data in retrieved_data:
            retrieved_data_str += f"{{'data_id': {_data.data_id}, 'data_context': {_data.raw_data}}}\n"

        prompt = f"""
            You are a document reranking and filtering assistant. Your task is to analyze documents, filter out irrelevant or low-quality content, and reorder the remaining documents based on their relevance to the user's query.

            USER QUERY:
            {query}

            DOCUMENTS TO RERANK:
            {retrieved_data_str}

            FILTERING AND RERANKING INSTRUCTIONS:
            1. Filter out documents that:
               - Are completely irrelevant to the query
               - Contain only partial or incomplete information
               - Are too generic or not specific enough
               - Have low information density
               - Are redundant with other documents
               - Are outdated or incorrect

            2. For the remaining documents, analyze them based on:
               - Direct relevance to the query
               - Information completeness and accuracy
               - Specificity and detail level
               - Information freshness and timeliness
               - Uniqueness of information

            3. Order the filtered documents from most relevant to least relevant

            RESPONSE FORMAT:
            Return ONLY a list of data_ids in order of relevance (most relevant first), like this:
            ["138799d3-cb3e-4cc7-9d4a-38a91bc40560", "67582a1d-54bd-44e6-8064-fdecc56bdb1f", "842e2bbb-95c3-49fc-839c-2168a811727d"]
            
            Do not include any explanations or other text in your response, just the list of data_ids.
            Only include data_ids of documents that pass the filtering criteria.
        """
        return prompt

    """
        Request Operation
    """
    def jsonlize(self, text: str) -> list[str]:
        data = []
        try:
            data = json.loads(text)
            uuid_pattern = all(isinstance(item, str) and len(item) == 36 and item.count('-') == 4 for item in data)
            if not uuid_pattern:
                raise ValueError("Processed data is not a list of valid UUIDs")
            return data
        
        except json.JSONDecodeError:
            raise json.JSONDecodeError
            
    def rerank_data(self, request: RerankRequest) -> tuple[RerankResponse, Response]:
        logger.info(f"Processing : <{SETTINGS.BASE.APP_NAME}> Started Reranking Retrieved Data")
        start_at = time.time()

        response_rerank, response = self.rerank_data_by_threshold(request=request)
        if response.status_code >= SETTINGS.STAT.SUCC_CODE_END:
            logger.error(response.detail)
            return response_rerank, response
        else:
            request.__dict__.update(retrieved_data=response_rerank.reranked_data)
        
        # response_rerank, response = self.rerank_data_by_genai(request=request)

        response_rerank.__dict__.update(
            rerank_time = time.time() - start_at
        )
        return response_rerank, response

    def rerank_data_by_threshold(self, request: RerankRequest) -> tuple[RerankResponse, Response]:
        logger.info(f"Processing : <{SETTINGS.BASE.APP_NAME}> Starts Reranking by Threshold")
        start_at = time.time()
        response_rerank = RerankResponse(**request.__dict__, reranked_data = request.retrieved_data)

        if not request.retrieved_data:
            response = Response(status_code=200, detail=self.response_format.ok(f"Reranking Completed : <{SETTINGS.BASE.APP_NAME}> No Vectors Provided"))
            logger.info(response.detail)
            return response_rerank, response

        if request.threshold <= 0:
            response = Response(status_code=200, detail=self.response_format.ok(f"Reranking Completed : <{SETTINGS.BASE.APP_NAME}> No Threshold is Set"))
            logger.info(response.detail)
            return response_rerank, response

        reranked_data = []
        
        if not request.threshold:
            request.threshold = SETTINGS.RETR.DEFAULT_RERANK_THRESHOLD
            
        reranked_data = []
        existing_contents = []
        sorted_content = sorted(request.retrieved_data, key=lambda x: x.score, reverse=True)
        for _data in sorted_content:
            if _data.score >= request.threshold and _data.raw_data not in existing_contents:
                reranked_data.append(_data)
                existing_contents.append(_data.raw_data)
        
        response_rerank.__dict__.update(
            reranked_data = reranked_data,  
            rerank_model  = 'threshold',
            rerank_count  = len(reranked_data),
            rerank_time   = time.time() - start_at
        )

        response = Response(status_code=200, detail=self.response_format.ok(f"Threshold Reranking Completed : <{SETTINGS.BASE.APP_NAME}> Reranking Completed"))
        logger.info(response.detail)

        return response_rerank, response

    def rerank_data_by_genai(self, request: RerankRequest) -> tuple[RerankResponse, Response]:
        logger.info(f"Processing : <{SETTINGS.BASE.APP_NAME}> Starts Reranking by GenAI")
        start_at = time.time()
        response_rerank = RerankResponse(**request.__dict__, reranked_data=request.retrieved_data)

        if not request.system_prompt:
            request.system_prompt = self.default_system_prompt

        text_input = self.rerank_prompt(query=request.query, retrieved_data=request.retrieved_data)

        retry_max_no = SETTINGS.INFR.RETRY_LIMIT
        retry_count  = 1
        reranked_data_ids = []
        while retry_count <= retry_max_no:
            logger.info(f"Calling Inference Service - Attempt: <{retry_count} / {retry_max_no}>")
            request_inference = InferenceRequest(
                system_prompt = request.system_prompt,
                input         = InferenceInput(text = text_input),
                config        = request.config,
            )
            
            response_inference, response = InferenceServiceManager(
                api_call=self.api_call
            ).inference_engine(request=request_inference)

            if response.status_code >= SETTINGS.STAT.SUCC_CODE_END:
                logger.error("Inference Issue. Retrying")
                retry_count += 1
                continue

            try:
                reranked_data_ids = self.jsonlize(response_inference.inference_output.text)
                response = Response(status_code=200, detail=self.response_format.ok(f"GenAI Reranking Completed : Completed to Rerank by GenAI"))
                logger.info(response.detail)
                break
            except:
                if retry_count > retry_max_no:
                    response = Response(status_code=500, detail=self.response_format.error(f"GenAI Reranking Failed : Failed to Parse GenAI Output"))
                    break
                logger.error(f"GenAI Formatting Issue. Retrying")
                retry_count += 1

        if response.status_code >= SETTINGS.STAT.SUCC_CODE_END:
            return response_rerank, response

        reranked_data = [_data for _data in request.retrieved_data if _data.data_id in reranked_data_ids]

        # Update Metrics
        response_rerank.__dict__.update(
            **{
                "reranked_data": reranked_data,
                "rerank_model": SETTINGS.GEAI.MODEL_NAME,
                "rerank_input_tokens": response_inference.inference_metrics.input_tokens,
                "rerank_output_tokens": response_inference.inference_metrics.output_tokens,
                "rerank_count": len(reranked_data),
                "rerank_time": time.time() - start_at
            }
        )

        return response_rerank, response