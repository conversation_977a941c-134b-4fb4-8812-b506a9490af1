from fastapi import APIRouter, Depends, status
from typing import Any

from ...settings import SETTINGS
from ...schemas.format import Response
from ...utils import router_response_handler
from ...database.registry.services.knowledge_data import (
        DataManager as KnowledgeDataManager,
        SystemDataRequest as SystemKnowledgeRequest, 
        SystemDataResponse as SystemKnowledgeResponse,
    )
from ...database.registry.services.qaflow_citation_log_data import (
        DataManager as QAFlowCitationLogDataManager,
        CreateRequest as QAFlowCitationLogCreateRequest, 
        BatchCreateRequest as QAFlowCitationLogBatchCreateRequest,
        UpdateRequest as QAFlowCitationLogUpdateRequest, 
        CommonRequest as QAFlowCitationLogRequest,
        BatchCommonRequest as QAFlowCitationLogBatchRequest,
    )


from ...database.registry.services.keyword_mapping import(
    DataManager as KMDataManager,
    SystemDataRequest as SystemKMeRequest, 
    SystemDataResponse as SystemKMResponse,
    CreateRequest as CreateRequest,
    UpdateRequest as UpdateRequest,
    CommonRequest as KMRequest,

    SystemKeywordMappingFuncReponse as SystemKeywordMappingFuncReponse,
    SystemKeywordMappingFuncRequest as SystemKeywordMappingFuncRequest

)


router = APIRouter(tags=["QAFlowCitationLog-Registry-General"])

# API DB Session
if SETTINGS.BASE.APP_API == True:
    default_api_call = True
else:
    default_api_call = False

@router.post("/request/knowledge_search", include_in_schema=not(SETTINGS.BASE.APP_PRODUCTION), status_code=status.HTTP_200_OK, response_model=SystemKnowledgeResponse)
def system_query_knowledge(request: SystemKnowledgeRequest, api_call: bool = default_api_call) -> SystemKnowledgeResponse:
    request = SystemKnowledgeRequest(**request.__dict__)
    response_data, response = KnowledgeDataManager(api_call=api_call).query_data_by_system(request=request)
    router_response_handler(response=response, api_call=api_call)
    return response_data
"""
    QAFlowCitationLog Data Management
"""
@router.post("/general/qaflow_citation_log/single/create", include_in_schema=not(SETTINGS.BASE.APP_PRODUCTION), status_code=status.HTTP_201_CREATED)
def general_create_qaflow_citation_log(request: QAFlowCitationLogCreateRequest, api_call: bool=default_api_call) -> Response:
    request  = QAFlowCitationLogCreateRequest(**request.__dict__)
    response = QAFlowCitationLogDataManager(api_call=api_call).create(request=request)
    router_response_handler(response=response, api_call=api_call)
    return response

@router.patch("/general/qaflow_citation_log/single/update", include_in_schema=not(SETTINGS.BASE.APP_PRODUCTION), status_code=status.HTTP_200_OK)
def general_update_qaflow_citation_log(request: QAFlowCitationLogUpdateRequest, api_call: bool=default_api_call) -> Response:
    request  = QAFlowCitationLogUpdateRequest(**request.__dict__)
    response = QAFlowCitationLogDataManager(api_call=api_call).update(request=request)
    router_response_handler(response=response, api_call=api_call)
    return response

@router.delete("/general/qaflow_citation_log/single/delete", include_in_schema=not(SETTINGS.BASE.APP_PRODUCTION), status_code=status.HTTP_200_OK)
def general_delete_qaflow_citation_log(request: QAFlowCitationLogRequest, api_call: bool=default_api_call) -> Response:
    request  = QAFlowCitationLogRequest(**request.__dict__)
    response = QAFlowCitationLogDataManager(api_call=api_call).delete(request=request)
    router_response_handler(response=response, api_call=api_call)
    return response

@router.delete("/general/qaflow_citation_log/single/drop", include_in_schema=not(SETTINGS.BASE.APP_PRODUCTION), status_code=status.HTTP_200_OK)
def general_drop_qaflow_citation_log(request: QAFlowCitationLogRequest, api_call: bool=default_api_call) -> Response:
    request  = QAFlowCitationLogRequest(**request.__dict__)
    response = QAFlowCitationLogDataManager(api_call=api_call).drop(request=request)
    router_response_handler(response=response, api_call=api_call)
    return response

@router.post("/general/qaflow_citation_log/single/activate", include_in_schema=not(SETTINGS.BASE.APP_PRODUCTION), status_code=status.HTTP_200_OK)
def general_activate_qaflow_citation_log(request: QAFlowCitationLogRequest, api_call: bool=default_api_call) -> Response:
    request  = QAFlowCitationLogRequest(**request.__dict__)
    response = QAFlowCitationLogDataManager(api_call=api_call).activate(request=request)
    router_response_handler(response=response, api_call=api_call)
    return response

@router.post("/general/qaflow_citation_log/single/deactivate", include_in_schema=not(SETTINGS.BASE.APP_PRODUCTION), status_code=status.HTTP_200_OK)
def general_deactivate_qaflow_citation_log(request: QAFlowCitationLogRequest, api_call: bool=default_api_call) -> Response:
    request  = QAFlowCitationLogRequest(**request.__dict__)
    response = QAFlowCitationLogDataManager(api_call=api_call).deactivate(request=request)
    router_response_handler(response=response, api_call=api_call)
    return response

@router.post("/general/qaflow_citation_log/batch/create", include_in_schema=not(SETTINGS.BASE.APP_PRODUCTION), status_code=status.HTTP_201_CREATED)
def general_batch_create_qaflow_citation_log(request: QAFlowCitationLogBatchCreateRequest, api_call: bool=default_api_call) -> Response:
    request  = QAFlowCitationLogBatchCreateRequest(**request.__dict__)
    response = QAFlowCitationLogDataManager(api_call=api_call).batch_create(request=request)
    router_response_handler(response=response, api_call=api_call)
    return response

@router.delete("/general/qaflow_citation_log/batch/delete", include_in_schema=not(SETTINGS.BASE.APP_PRODUCTION), status_code=status.HTTP_200_OK)
def general_batch_delete_qaflow_citation_log(request: QAFlowCitationLogBatchRequest, api_call: bool=default_api_call) -> Response:
    request  = QAFlowCitationLogBatchRequest(**request.__dict__)
    response = QAFlowCitationLogDataManager(api_call=api_call).batch_delete(request=request)
    router_response_handler(response=response, api_call=api_call)
    return response

@router.delete("/general/qaflow_citation_log/batch/drop", include_in_schema=not(SETTINGS.BASE.APP_PRODUCTION), status_code=status.HTTP_200_OK)
def general_batch_drop_qaflow_citation_log(request: QAFlowCitationLogBatchRequest, api_call: bool=default_api_call) -> Response:
    request  = QAFlowCitationLogBatchRequest(**request.__dict__)
    response = QAFlowCitationLogDataManager(api_call=api_call).batch_drop(request=request)
    router_response_handler(response=response, api_call=api_call)
    return response

@router.post("/general/qaflow_citation_log/batch/activate", include_in_schema=not(SETTINGS.BASE.APP_PRODUCTION), status_code=status.HTTP_200_OK)
def general_batch_activate_qaflow_citation_log(request: QAFlowCitationLogBatchRequest, api_call: bool=default_api_call) -> Response:
    request  = QAFlowCitationLogBatchRequest(**request.__dict__)
    response = QAFlowCitationLogDataManager(api_call=api_call).batch_activate(request=request)
    router_response_handler(response=response, api_call=api_call)
    return response

@router.post("/general/qaflow_citation_log/batch/deactivate", include_in_schema=not(SETTINGS.BASE.APP_PRODUCTION), status_code=status.HTTP_200_OK)
def general_batch_deactivate_qaflow_citation_log(request: QAFlowCitationLogBatchRequest, api_call: bool=default_api_call) -> Response:
    request  = QAFlowCitationLogBatchRequest(**request.__dict__)
    response = QAFlowCitationLogDataManager(api_call=api_call).batch_deactivate(request=request)
    router_response_handler(response=response, api_call=api_call)
    return response


@router.post("/request/system_query_keyword_mapping", include_in_schema=not(SETTINGS.BASE.APP_PRODUCTION), status_code=status.HTTP_200_OK, response_model=SystemKMResponse)
def system_query_keyword_mapping(request: SystemKMeRequest, api_call: bool = default_api_call) -> SystemKMResponse:
    request = SystemKMeRequest(**request.__dict__)
    response_data, response = KMDataManager(api_call=api_call).query_data_by_system(request=request)
    router_response_handler(response=response, api_call=api_call)
    return response_data


@router.post("/request/get_keyword_mappings_with_string", include_in_schema=not(SETTINGS.BASE.APP_PRODUCTION), status_code=status.HTTP_200_OK, response_model=SystemKeywordMappingFuncReponse)
def get_keyword_mappings_with_string(request:SystemKeywordMappingFuncRequest, api_call: bool = default_api_call) -> SystemKeywordMappingFuncReponse:
    request = SystemKeywordMappingFuncRequest(**request.__dict__)

    response_data, response = KMDataManager(api_call=api_call).get_keyword_mappings_with_string(request=request)
    router_response_handler(response=response, api_call=api_call)
    return response_data

@router.post("/request/keyword_mappings_create", include_in_schema=not(SETTINGS.BASE.APP_PRODUCTION), status_code=status.HTTP_200_OK, response_model=Any)
def keyword_mappings_create(request: CreateRequest, api_call: bool = default_api_call) -> Any:
    request = CreateRequest(**request.__dict__)
    response_data, response = KMDataManager(api_call=api_call).create(request=request)
    return response_data

@router.post("/request/keyword_mappings_update", include_in_schema=not(SETTINGS.BASE.APP_PRODUCTION), status_code=status.HTTP_200_OK, response_model=Any)
def keyword_mappings_update(request: UpdateRequest, api_call: bool = default_api_call) -> Any:
    request = UpdateRequest(**request.__dict__)
    response_data, response = KMDataManager(api_call=api_call).update(request=request)
    return response_data


@router.post("/request/keyword_mappings_del", include_in_schema=not(SETTINGS.BASE.APP_PRODUCTION), status_code=status.HTTP_200_OK, response_model=Any)
def keyword_mappings_del(request: KMRequest, api_call: bool = default_api_call) -> Any:
    request = KMRequest(**request.__dict__)
    response_data, response = KMDataManager(api_call=api_call).delete(request=request)
    return response_data