from datetime import datetime, timezone
import time
import inspect
import re
import json

from ..settings import SETTINGS

from ..schemas.format import (
    ResponseFormatter,
    Response,
)

from ..schemas.refinement import (
    RefinementKeyResponse,
    RefinementRequest,
    RefinementResponse
)

from ..services.inference_service import (
    InferenceServiceManager,
    InferenceInput,
    InferenceRequest
)

from ..logger.log_handler import get_logger

logger = get_logger(__name__)

class RefinementServiceManager:
    response_format = ResponseFormatter(suffix = f"{inspect.stack()[0][3]}")

    default_system_prompt = ""

    def __init__(self, api_call: bool):
        self.api_call = api_call

    @staticmethod
    def general_prompt(query: str) -> str:
        prompt = f"""[System]
YOU ARE THE WORLD'S BEST INSURANCE AGENT ASSISTANT FROM Prudential Hong Kong, AWARDED THE "BEST INSURANCE AGENT" TITLE BY THE ASIAN INSURANCE ASSOCIATION (2023) AND RANKED AS THE "TOP INSURANCE ADVISOR" BY THE INTERNATIONAL INSURANCE NETWORK (2022).
Your task is to refine the input query to be more specific and effective.

[Input]
"{query}"

[Output]
Output the refined query directly without any prefix or explanation."""
        return prompt

    @staticmethod
    def translation_prompt(query: str) -> str:
        prompt = f"""[System]
YOU ARE THE WORLD'S BEST INSURANCE AGENT ASSISTANT FROM Prudential Hong Kong, AWARDED THE "BEST INSURANCE AGENT" TITLE BY THE ASIAN INSURANCE ASSOCIATION (2023) AND RANKED AS THE "TOP INSURANCE ADVISOR" BY THE INTERNATIONAL INSURANCE NETWORK (2022).
- Your task is to translate the input to English or Chinese, depending on the language of the input query. 
- If the language of the input query is Chinese, then translate to English.
- If the language of the input query is English, then translate to Traditional Chinese.
- Do not modify the meaning of the question, but if the question is not clear, you can try to rephrase the question based on the user intent.
- If abbreviation is mentioned in the query, do not add any full term in it, just leave the abbrevation as it is.

[Input]
"{query}"

[Output]
Output the refined query directly without any prefix or explanation."""
        return prompt


    def refine_query(self, request: RefinementRequest) -> tuple[RefinementResponse, Response]:
        logger.info(f"Processing : <{SETTINGS.BASE.APP_NAME}> Started Refining User Query")
        start_at = time.time()
        response_refinement = RefinementResponse(**request.__dict__, refined_query=request.query)
        if not request.system_prompt:
            request.system_prompt = self.default_system_prompt

        if request.refinement_type.lower() == 'translation':
            text_input = self.translation_prompt(query=request.query)
        else:
            text_input = self.general_prompt(query=request.query)

        request = InferenceRequest(
            system_prompt = request.system_prompt,
            input         = InferenceInput(text = text_input),
            config        = request.config,
        )
        
        response_inference, response = InferenceServiceManager(
            api_call=self.api_call
        ).inference_engine(request=request)

        # Update Metrics
        response_refinement.__dict__.update(
            **{
                "refinement_model": SETTINGS.GEAI.MODEL_NAME,
                "refinement_input_tokens": response_inference.inference_metrics.input_tokens,
                "refinement_output_tokens": response_inference.inference_metrics.output_tokens
            }
        )

        if response.status_code >= SETTINGS.STAT.SUCC_CODE_END:
            logger.error(response.detail)
        else:
            response = Response(status_code=200, detail=self.response_format.ok(f"Refinement Completed : <{SETTINGS.BASE.APP_NAME}> Successfully Refined User Query"))
            response_refinement.refined_queries = [re.sub(r'\W+$', '', response_inference.inference_output.text)]

        # Update Final Response
        response_refinement.__dict__.update(
            **{
                "refinement_time": time.time() - start_at,
                "response_at":     datetime.now(timezone.utc)
            }
        )

        return response_refinement, response

    def refine_with_key(self, query: str) -> str:
        prompt = f"""[System]
        YOU ARE THE WORLD'S BEST INSURANCE AGENT ASSISTANT FROM Prudential Hong Kong, AWARDED THE "BEST INSURANCE AGENT" TITLE BY THE ASIAN INSURANCE ASSOCIATION (2023) AND RANKED AS THE "TOP INSURANCE ADVISOR" BY THE INTERNATIONAL INSURANCE NETWORK (2022). YOUR TASK IS TO ANALYZE AND PROCESS INSURANCE-RELATED QUERIES WITH THE HIGHEST LEVEL OF ACCURACY AND PROFESSIONALISM.

        ### INSTRUCTIONS

        1. **Query Analysis**:
        - Carefully read and understand the input query
        - Identify key insurance-related terms and concepts 
        - Determine the primary language of the query

        2. **Processing Guidelines**:
        - If the query is in Chinese, translate it to English
        - If the query is in English, translate it to Traditional Chinese
        - Extract relevant keywords that capture the essence of both the original query and the translated query
        - Identify any exclusion clauses (e.g., "except," "excluding," "other than," "apart from") and extract the excluded terms as part of the blacklist
        - Maintain the original meaning and context
        - Do not modify the meaning of the question, but if the question is not clear, you can try to rephrase the question based on the user intent
        - If the query references a plan name, **do not translate** that plan name. **Must keep the plan name in its original language**
        - Preserve any abbreviations as they are

        3. **Output Format**:
        - Provide a JSON response with three components:
            - "response": The translated query
            - "keywords": A list of relevant keywords extracted from both the original query and the translated query
            - "blacklist": A list of keywords that are explicitly excluded in the user query
            - "blacklist": A list of keywords that are explicitly excluded in the user query

        4. **Keyword Selection**:
        - **For "keywords"**:
            - Focus on insurance-specific terms
            - Include product names and types
            - Capture key concepts and requirements
            - **Exclude any plan names that are mentioned in exclusion clauses**
            - Exclude common words and articles
            - If referencing a multi-word product or plan name, keep it as a single keyword (e.g., VHIS plan cannot be extracted to 'VHIS' and 'plan')
            - Treat pronouns and determiners as general terms, not as specific insurance plan names within the context
            - Include both singular and plural forms (e.g., plan and plans) as keywords
            - Ensure keywords are meaningful and relevant
            
        - **For "blacklist"**:
            - Identify exclusion clauses using keywords such as "except," "excluding," "other than," "apart from," etc.
            - Extract the plan names or keywords that directly follow these exclusion indicators
            - Do not include these excluded plan names in the "keywords" list
            - Include the full name of multi-word plans as single entries (e.g., "vv plan", "yy plan")
            - Ensure blacklist items are relevant and accurately reflect the exclusions in the query

        ### What Not To Do ###
        - NEVER MODIFY THE ORIGINAL MEANING OF THE QUERY
        - NEVER ADD OR REMOVE IMPORTANT DETAILS
        - NEVER EXPAND ABBREVIATIONS
        - NEVER INCLUDE GENERIC OR IRRELEVANT KEYWORDS
        - NEVER PROVIDE EXPLANATIONS OR ADDITIONAL TEXT

        [Input]
        "{query}"

        [Output]
        Provide the response in the following JSON format:
        {{
            "response": "translated query",
            "keywords": ["keyword1", "keyword2","關鍵字3","關鍵字4" ...],
            "blacklist": ["excludedKeyword1", "excludedKeyword2","排除的關鍵字3" ...]
        }}
        """
#   - Determine the most relevant category from {category} based on the query's content and include it in the "keywords" list
        return prompt
    
    def jsonlize(self, text: str) -> dict:
        match = re.search(r'```json\s*(.*?)\s*```', text, re.DOTALL)
        if match:
            json_str = match.group(1)
        else:
            json_str = text  
        try:
            data = json.loads(json_str)
            return data
        except json.JSONDecodeError:
            raise json.JSONDecodeError
        
    def refine_query_with_key(self,  request:RefinementRequest) -> tuple[RefinementKeyResponse, Response]:
        """
        Process the input text to generate a translated query and extract keywords.
        
        Args:
            text (str): The input query to be processed
            
        Returns:
            dict: A dictionary containing:
                - response: The translated query
                - keywords: List of relevant keywords
        """

        logger.info(f"Processing : <{SETTINGS.BASE.APP_NAME}> Started Refining User Query")
        response_refinement = RefinementKeyResponse(**request.__dict__, refined_query=request.query)
        start_at = time.time()

        retry_max_no = SETTINGS.INFR.RETRY_LIMIT
        retry_count = 1
        result = {
            "response": "",
            "keywords": [],
            "blacklist": [], 
        }

        while retry_count <= retry_max_no:
            logger.info(f"Calling Inference Service - Attempt: <{retry_count} / {retry_max_no}>")
            _request = InferenceRequest(
                system_prompt=self.default_system_prompt,
                input=InferenceInput(text=self.refine_with_key(request.query)),
                config=None
            )
            print(self.refine_with_key(request.query))
            response_inference, response = InferenceServiceManager(
                api_call=self.api_call
            ).inference_engine(request=_request)
            print("Refinement Output: ", response_inference.inference_output.text)

            if response.status_code >= SETTINGS.STAT.SUCC_CODE_END:
                logger.error("Inference Issue. Retrying")
                retry_count += 1
                continue

            try:
                result = self.jsonlize(response_inference.inference_output.text)
                logger.info("Successfully processed jsonlize response")
                break
            except Exception as e:
                if retry_count >= retry_max_no:
                    logger.error(f"GenAI Formatting Issue. Max retries reached: {str(e)}")
                    break
                logger.error(f"GenAI Formatting Issue. Retrying: {str(e)}")
                retry_count += 1

        response = Response(status_code=200, detail=self.response_format.ok(f"Refinement Completed : <{SETTINGS.BASE.APP_NAME}> Successfully Refined User Query"))
        response_refinement.refined_queries = [re.sub(r'\W+$', '', result.get("response", ""))]
        response_refinement.keywords =  [result.get("keywords", [])]
        response_refinement.blacklist =  result.get("blacklist", [])

        response_refinement.__dict__.update(
            **{

                "refinement_model": SETTINGS.GEAI.MODEL_NAME,
                "refinement_input_tokens": response_inference.inference_metrics.input_tokens,
                "refinement_output_tokens": response_inference.inference_metrics.output_tokens,
                "refinement_time": time.time() - start_at,
                "response_at":     datetime.now(timezone.utc)
            }
        )
        return response_refinement,response
    
