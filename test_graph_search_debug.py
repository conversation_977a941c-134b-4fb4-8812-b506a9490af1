#!/usr/bin/env python3
"""
Test script to debug graph search with your specific data
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.services.graph_service import GraphServiceManager
from src.schemas.graph import GraphSearchRequest, GraphSearchInfo

def test_graph_search():
    """
    Test graph search with your specific data_ids and edge_type_filter
    """
    
    # Your specific data
    data_ids = ["873e5240-0738-40e8-b2cc-d2b25a9a4fb4"]
    edge_type_filter = ["APPLICATION_PROCESS"]
    
    print("🔍 Testing Graph Search with:")
    print(f"  data_ids: {data_ids}")
    print(f"  edge_type_filter: {edge_type_filter}")
    print()
    
    # Create the request
    request = GraphSearchRequest(
        search_info=[
            GraphSearchInfo(
                data_ids=data_ids
            )
        ],
        search_config={
            "edge_type_filter": edge_type_filter,
            "depth": 1,
            "top_k": None
        }
    )
    
    print("📋 Request details:")
    print(f"  search_info: {request.search_info}")
    print(f"  search_config: {request.search_config}")
    print()
    
    # Initialize the service
    graph_service = GraphServiceManager()
    
    try:
        # Execute the search
        print("🚀 Executing graph search...")
        response_graph, response = graph_service.search_by_graph(request)
        
        print(f"📊 Response status: {response.status_code}")
        print(f"📊 Response detail: {response.detail}")
        print()
        
        if response.status_code == 200:
            print(f"✅ Graph search completed successfully!")
            print(f"📈 Retrieved data count: {response_graph.graph_retrieval_count}")
            print(f"⏱️  Retrieval time: {response_graph.graph_retrieval_time:.3f}s")
            print()
            
            for i, data in enumerate(response_graph.retrieved_data):
                print(f"📦 Retrieved data {i+1}:")
                print(f"  Nodes: {len(data.nodes) if data.nodes else 0}")
                if data.nodes:
                    for j, node in enumerate(data.nodes):
                        print(f"    Node {j+1}: {node.node_id} (type: {getattr(node, 'node_type', 'N/A')})")
        else:
            print(f"❌ Graph search failed!")
            
    except Exception as e:
        print(f"💥 Exception occurred: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_graph_search()
