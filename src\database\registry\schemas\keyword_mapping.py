from pydantic import BaseModel, Field
from datetime import datetime, timezone
import uuid


# Base model for data representation
class KeywordMappingModel(BaseModel):
    mapping_id: str      | None = None
    keywords: list[str]  | None = None
    ranking: int         | None = None
    mapping_status: int  | None = None
    mapping_version: int | None = None
    created_at: datetime | None = None
    updated_at: datetime | None = None

# Schema for creating a new mapping
class KeywordMappingCreate(BaseModel):
    mapping_id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    keywords: list[str] = Field(..., min_items=1)  # Required, at least one keyword
    ranking: int = 2
    mapping_status: int = 1
    mapping_version: int = 1
    created_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    updated_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
class SecretKeywordMapping(BaseModel):
    mapping_id: str | None = None
    keywords: list[str] | None = None
    ranking: int | None = None
    mapping_status: int | None = None
    mapping_version: int | None = None
    created_at: datetime | None = None
    updated_at: datetime | None = None

class KeywordMappingCreateRequest(BaseModel):
    user_requestid: str | None = None
    user_id: str = ''
    user_name: str = ''
    is_admin: bool = False
    data: KeywordMappingCreate

class KeywordMappingBatchCreateRequest(BaseModel):
    create_requests: list[KeywordMappingCreateRequest]

# Schema for updating an existing mapping
class KeywordMappingUpdate(BaseModel):
    keywords: list[str] | None = None  # Optional update to keywords
    ranking: int | None = None
    mapping_version: int | None = None  # Optional, managed by DataManager if not provided

class KeywordMappingUpdateRequest(BaseModel):
    user_requestid: str | None = None
    user_id: str = ''
    user_name: str = ''
    is_admin: bool = False
    mapping_id: str
    update_data: KeywordMappingUpdate
    overwrite: bool = True

class KeywordMappingRequest(BaseModel):
    user_requestid: str | None = None
    user_id: str | None = None
    user_name: str | None = None
    mapping_id: str

class KeywordMappingBatchRequest(BaseModel):
    batch_requests: list[KeywordMappingRequest]

# Filter schemas
class KeywordMappingStringFilter(BaseModel):
    mapping_id_filter: list[str] | None = None

class KeywordMappingNumericFilter(BaseModel):
    mapping_status_min: int | None = None
    mapping_status_max: int | None = None
    mapping_version_min: int | None = None
    mapping_version_max: int | None = None
    ranking_min:         int | None = None      
    ranking_max:         int | None = None        

class KeywordMappingListFilter(BaseModel):
    keywords_or: list[str] | None = None  # Keywords containing any of these

class KeywordMappingDatetimeFilter(BaseModel):
    created_at_start: datetime | None = None
    created_at_end: datetime | None = None
    updated_at_start: datetime | None = None
    updated_at_end: datetime | None = None

class KeywordDictionaryFilter(BaseModel):
    storage_secrets_or:          list[str] | None = None
    storage_secrets_and:         list[str] | None = None
    storage_secrets_origin_or:   list[str] | None = None
    storage_secrets_origin_and:  list[str] | None = None

    keyword_vectorinfo_or:     list[str] | None = None
    keyword_vectorinfo_and:    list[str] | None = None
    keyword_graphinfo_or:      list[str] | None = None
    keyword_graphinfo_and:     list[str] | None = None
    keyword_searchstorage_or:  list[str] | None = None
    keyword_searchstorage_and: list[str] | None = None
    keyword_searchinfo_or:     list[str] | None = None
    keyword_searchinfo_and:    list[str] | None = None
    keyword_secrets_or:        list[str] | None = None
    keyword_secrets_and:       list[str] | None = None
class KnowledgeBooleanFilter(BaseModel):
    keyword_record_filter: bool | None = None

class KnowledgeDatetimeFilter(BaseModel):
    file_created_datetime_start:       datetime | None = None
    file_created_datetime_end:         datetime | None = None
    file_last_modified_datetime_start: datetime | None = None
    file_last_modified_datetime_end:   datetime | None = None
    reference_start_date_start:        datetime | None = None
    reference_start_date_end:          datetime | None = None
    reference_end_date_start:          datetime | None = None
    reference_end_date_end:            datetime | None = None

    retention_at:                      datetime | None = None

    keyword_issue_date_start:        datetime  | None = None
    keyword_issue_date_end:          datetime  | None = None
    keyword_effective_from_start:    datetime  | None = None
    keyword_effective_from_end:      datetime  | None = None
    keyword_effective_to_start:      datetime  | None = None
    keyword_effective_to_end:        datetime  | None = None

    created_at_start:                  datetime  | None = None
    created_at_end:                    datetime  | None = None
    updated_at_start:                  datetime  | None = None
    updated_at_end:                    datetime  | None = None

class KnowledgeByteFilter(BaseModel):
    not_used_filter: list[bytes] | None = None
class KeywordMappingFilter(BaseModel):
    string_filter: KeywordMappingStringFilter | None = None
    numeric_filter: KeywordMappingNumericFilter | None = None
    list_filter: KeywordMappingListFilter | None = None
    datetime_filter: KeywordMappingDatetimeFilter | None = None
    dictionary_filter: KeywordDictionaryFilter| None = None
    boolean_filter:    KnowledgeBooleanFilter    | None = None
    datetime_filter:   KnowledgeDatetimeFilter   | None = None
    byte_filter:       KnowledgeByteFilter       | None = None
    sorting: dict = {"mapping_id": "asc"}
    filter_no: int = -1  # -1 means no limit

# System-level request and response
class SystemKeywordMappingRequest(BaseModel):
    mapping_requestid: str = Field(default_factory=lambda: str(uuid.uuid4()))
    data_filter: KeywordMappingFilter | None = None

class SystemKeywordMappingResponse(BaseModel):
    mapping_requestid: str
    filtered_data: list[KeywordMappingModel] = []
    data_count: int = 0

# Backup and export configurations (unchanged from your provided code)
class BackupConfig(BaseModel):
    format: str | None = None
    location: str | None = None
    name: str | None = None
    host: str | None = None
    port: str | None = None
    user: str | None = None
    pswd: str | None = None
    table: str | None = None
    rdir: str | None = None
    sdir: str | None = None
    limit: int | None = None

class KeywordMappingBackupRequest(BaseModel):
    mapping_requestid: str = Field(default_factory=lambda: str(uuid.uuid4()))
    data_filter: KeywordMappingFilter | None = None
    backup_config: BackupConfig | None = None

class IOConfig(BaseModel):
    format: str | None = None
    location: str | None = None
    name: str | None = None
    host: str | None = None
    port: str | None = None
    user: str | None = None
    pswd: str | None = None
    table: str | None = None
    rdir: str | None = None
    sdir: str | None = None
    file_rdir: str | None = None
    file_sdir: str | None = None
    file_name: str | None = None


class KeywordMappingImportRequest(BaseModel):
    mapping_requestid: str=Field(default_factory=lambda: str(uuid.uuid4()))
    data_filter: KeywordMappingFilter | None = None
    io_config:           IOConfig | None = None
    backup:              bool=True

class KeywordMappingExportRequest(BaseModel):
    mapping_requestid: str = Field(default_factory=lambda: str(uuid.uuid4()))
    data_filter: KeywordMappingFilter | None = None
    io_config: IOConfig | None = None
    include_datetime: bool = True

class RestoreConfig(BaseModel):
    format: str | None = None
    location: str | None = None
    name: str | None = None
    host: str | None = None
    port: str | None = None
    user: str | None = None
    pswd: str | None = None
    table: str | None = None
    rdir: str | None = None
    sdir: str | None = None
    
class KeywordMappingBackupListRequest(BaseModel):
    keyword_requestid: str=Field(default_factory=lambda: str(uuid.uuid4()))
    backup_config:       BackupConfig | None = None

class KeywordMappingBackupListResponse(BaseModel):
    keyword_requestid: str=Field(default_factory=lambda: str(uuid.uuid4()))
    table_list:          list[str]=[]

class KeywordMappingRestoreRequest(BaseModel):
    mapping_requestid: str = Field(default_factory=lambda: str(uuid.uuid4()))
    restore_config: RestoreConfig | None = None


    
    
class UserKeywordRequest(BaseModel):
    mapping_requestid: str=Field(default_factory=lambda: str(uuid.uuid4()))
    data_filter:       KeywordMappingFilter
class UserKeywordReponse(BaseModel):
    mapping_requestid: str=Field(default_factory=lambda: str(uuid.uuid4()))
    filtered_data:       list[KeywordMappingModel]



class SystemKeywordMappingFuncRequest(BaseModel):
    search_str: str

class SystemKeywordMappingFuncReponse(BaseModel):
    filtered_data: list[KeywordMappingModel]