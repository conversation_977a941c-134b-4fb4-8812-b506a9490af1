from ...settings import SETTINGS

from ...logger.log_handler import get_logger

logger = get_logger(__name__)

# Import Functions if APP_FUNC == True
if SETTINGS.BASE.APP_FUNC == True:
    if SETTINGS.BASE.APP_ENCRYPTION:
        # Import Security Function
        try:
            module = __import__(SETTINGS.SCRT.REQUEST_USERKEY_MODULE, fromlist=[SETTINGS.SCRT.REQUEST_USERKEY_FUNC])
            request_retrieve_userkey = getattr(module, SETTINGS.SCRT.REQUEST_USERKEY_FUNC)        
        except:
            err_msg = f"Import Error : <{SETTINGS.BASE.APP_NAME}> Failed to Import <SecurityHub> Module"
            logger.error(err_msg)
            raise Exception(err_msg)
