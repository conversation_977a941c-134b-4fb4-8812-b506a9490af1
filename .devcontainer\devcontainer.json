{"name": "PruCodespace", "image": "prudentialgte/prucodespaces:python-stable", "runArgs": ["--cap-add=NET_ADMIN", "--cap-add=NET_RAW", "--device=/dev/net/tun"], "remoteUser": "vscode", "postAttachCommand": "/usr/bin/sudo /usr/bin/cs_entrypoint.sh", "customizations": {"vscode": {"settings": {"terminal.integrated.defaultProfile.linux": "bash"}, "extensions": ["github.copilot"]}}, "postCreateCommand": "pip3 install --user pyodbc"}