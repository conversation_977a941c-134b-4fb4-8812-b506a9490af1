
"""
    KeywordMapping Data Manager Custom Config
"""

from ....settings import SETTINGS
from ..models.registry_models import KeywordMapping  # Adjust path as needed
from ..schemas import keyword_mapping as schema  # Adjust path as needed

# Custom Config Constants
DATA_UID = "mapping_id"
DATA_STATUS = "mapping_status"
DATA_VERSION = "mapping_version"
DATA_OBJECT = "KeywordMapping"
COLLECTION_NAME = SETTINGS.DATB.KM_TABLE

DBClass = KeywordMapping

DataCreate = schema.KeywordMappingCreate
CreateRequest = schema.KeywordMappingCreateRequest
BatchCreateRequest = schema.KeywordMappingBatchCreateRequest
UpdateRequest = schema.KeywordMappingUpdateRequest
CommonRequest = schema.KeywordMappingRequest
BatchCommonRequest = schema.KeywordMappingBatchRequest
SystemKeywordMappingFuncRequest =schema.SystemKeywordMappingFuncRequest
SystemKeywordMappingFuncReponse     =schema.SystemKeywordMappingFuncReponse
SystemDataRequest = schema.SystemKeywordMappingRequest
SystemDataResponse = schema.SystemKeywordMappingResponse
DataFilter = schema.KeywordMappingFilter
SecretData             = schema.SecretKeywordMapping
UserDataRequest        = schema.UserKeywordRequest
UserDataResponse       = schema.UserKeywordReponse
KeywordMappingModel    = schema.KeywordMappingModel
DataImportRequest      = schema.KeywordMappingImportRequest
DataBackupRequest      = schema.KeywordMappingBackupRequest
DataExportRequest      = schema.KeywordMappingExportRequest
DataBackupListRequest  = schema.KeywordMappingBackupListRequest
DataBackupListResponse = schema.KeywordMappingBackupListResponse
DBRestoreRequest       = schema.KeywordMappingRestoreRequest

DataStringFilter  =  schema.KeywordMappingStringFilter 
DataNumericFilter = schema.KeywordMappingNumericFilter 
# Database Connections
from ....database.registry.connections.registry_connection import get_db_func, get_db_api

if SETTINGS.BASE.APP_API == True:
    db_api = get_db_api
    default_api_call = True
else:
    db_api = None
    default_api_call = False

if SETTINGS.BASE.APP_FUNC == True:
    db_func = get_db_func
else:
    db_func = None

DEFAULT_BACKUP_CONFIG = schema.BackupConfig(
    format=SETTINGS.BKUP.FORM,
    location=SETTINGS.BKUP.LOCA,
    name=SETTINGS.BKUP.NAME,
    host=SETTINGS.BKUP.HOST,
    port=SETTINGS.BKUP.PORT,
    user=SETTINGS.BKUP.USER,
    pswd=SETTINGS.BKUP.PSWD,
    table=SETTINGS.BKUP.TABLE,
    rdir=SETTINGS.BKUP.RDIR,
    sdir=SETTINGS.BKUP.SDIR,
    limit=SETTINGS.BKUP.LIMIT
)

DEFAULT_EXPORT_CONFIG = schema.IOConfig(
    format=SETTINGS.EXPT.FORM,
    location=SETTINGS.EXPT.LOCA,
    name=SETTINGS.EXPT.NAME,
    host=SETTINGS.EXPT.HOST,
    port=SETTINGS.EXPT.PORT,
    user=SETTINGS.EXPT.USER,
    pswd=SETTINGS.EXPT.PSWD,
    table=SETTINGS.EXPT.TABLE,
    rdir=SETTINGS.EXPT.RDIR,
    sdir=SETTINGS.EXPT.SDIR,
    file_rdir=SETTINGS.EXPT.FILE_RDIR,
    file_sdir=SETTINGS.EXPT.FILE_SDIR,
    file_name=SETTINGS.EXPT.FILE_NAME
)

# Import Database Connection (unchanged from your example)
from ....database.registry.models.io_models import set_io_db_table as set_io_db_table
from ....database.registry.models.backup_models import set_bkup_db_table as set_bkup_db_table
from ....database.registry.models.backup_models import get_bkup_db_table
from ....database.registry.connections.backup_connection import init_bkup_db_engine, get_bkup_db_func
from ....database.registry.connections.io_connection import init_io_db_engine, get_io_db_func
from ....database.registry.connections.registry_connection import get_db_func, get_db_api


if SETTINGS.BASE.APP_API == True:
    db_api = get_db_api
    default_api_call = True
else:
    db_api = None
    default_api_call = False

if SETTINGS.BASE.APP_FUNC == True:
    db_func = get_db_func
else:
    db_func = None



DEFAULT_BACKUP_CONFIG = schema.BackupConfig(
    format=SETTINGS.BKUP.FORM,
    location=SETTINGS.BKUP.LOCA,
    name=SETTINGS.BKUP.NAME,
    host=SETTINGS.BKUP.HOST,
    port=SETTINGS.BKUP.PORT,
    user=SETTINGS.BKUP.USER,
    pswd=SETTINGS.BKUP.PSWD,
    table=SETTINGS.BKUP.TABLE,
    rdir=SETTINGS.BKUP.RDIR,
    sdir=SETTINGS.BKUP.SDIR,
    limit=SETTINGS.BKUP.LIMIT
)

DEFAULT_EXPORT_CONFIG = schema.IOConfig(
    format=SETTINGS.EXPT.FORM,
    location=SETTINGS.EXPT.LOCA,
    name=SETTINGS.EXPT.NAME,
    host=SETTINGS.EXPT.HOST,
    port=SETTINGS.EXPT.PORT,
    user=SETTINGS.EXPT.USER,
    pswd=SETTINGS.EXPT.PSWD,
    table=SETTINGS.EXPT.TABLE,
    rdir=SETTINGS.EXPT.RDIR,
    sdir=SETTINGS.EXPT.SDIR,
    file_rdir=SETTINGS.EXPT.FILE_RDIR,
    file_sdir=SETTINGS.EXPT.FILE_SDIR,
    file_name=SETTINGS.EXPT.FILE_NAME
)

# Import Database Connection
from ....database.registry.models.io_models import set_io_db_table as set_io_db_table
from ....database.registry.models.backup_models import set_bkup_db_table as set_bkup_db_table

from ....database.registry.models.backup_models import get_bkup_db_table
from ....database.registry.connections.backup_connection import init_bkup_db_engine, get_bkup_db_func
from ....database.registry.connections.io_connection import init_io_db_engine, get_io_db_func

from ....database.registry.connections.registry_connection import get_db_func, get_db_api

# API DB Session
if SETTINGS.BASE.APP_API == True:
    db_api = get_db_api
    default_api_call = True
else:
    db_api = None
    default_api_call = False

# Function DB Session
if SETTINGS.BASE.APP_FUNC == True:
    db_func = get_db_func
else:
    db_func = None
    
"""
    End of Custom Config
"""

import os
from mongoengine.queryset.visitor import Q
from datetime import datetime, timezone
import uuid
import inspect
from typing import Generator, Any

import json
import csv

from ..schemas.format import (
    ResponseFormatter,
    Response,
    ComplexEncoder
)
from ..schemas.database import (
    BackupDatabaseConfiguration,
    IODatabaseConfiguration
)

if SETTINGS.BASE.APP_ENCRYPTION == True:
    from ....security.services.crypto_service import CryptoServiceManager

from ....logger.log_handler import get_logger

logger = get_logger(__name__)


class DataManager:
    response_format = ResponseFormatter(suffix = f"{inspect.stack()[0][3]}")

    def __init__(
            self, 
            api_call: bool = default_api_call,
            db_api:   Any | None = db_api, 
            db_func:  Any | None = db_func
        ):
        self.api_call = api_call
        self.db_api   = db_api
        self.db_func  = db_func

    """
        General Operation
    """
    # Create
    def create(self, request: CreateRequest) -> Response:
        
        # Validate parameters with creator role
        response = self.verify_content(is_admin=request.is_admin, data=request.data)
        if response:
            return response

        data = DataCreate(**request.data.__dict__)
        db_data = DBClass(**data.dict())

        try:
            if self.api_call == True:
                with self.db_api(collection_name=COLLECTION_NAME):
                    db_data.save()
                    db_data.reload()
            else:
                with self.db_func(collection_name=COLLECTION_NAME):
                    db_data.save()
                    db_data.reload()
            response = Response(status_code=201, detail=self.response_format.ok(f"Success : Registered {DATA_OBJECT} <{DATA_UID}: {getattr(data, DATA_UID)}>"))
            logger.info(response.detail)

        # Handle common exceptions that might occur
        except (BaseException, Exception) as e:
            response = Response(status_code=500, detail=self.response_format.error(f"Common Error : Registering {DATA_OBJECT} <{DATA_UID}: {getattr(data, DATA_UID)}>", str(e)))
            logger.error(response.detail)

        # Handle any other exceptions that might occur
        except:
            response = Response(status_code=500, detail=self.response_format.error(f"Unexpected Error : Registering {DATA_OBJECT} <{DATA_UID}: {getattr(data, DATA_UID)}>"))
            logger.error(response.detail)

        return response

    # Batch Create
    def batch_create(self, request: BatchCreateRequest) -> Response:
        _data_list = []
        admin_list = []

        for _request in request.create_requests:
            if isinstance(_request.data, DataCreate):
                _data_list.append(_request.data)
                admin_list.append(_request.is_admin)
            elif isinstance(_request.data, dict):
                _data_list.append(DataCreate(**_request.data))
                admin_list.append(_request.is_admin)
            else:
                logger.error(f"Invalid Data Type for <{_request.data}>")

        # Validate parameters with creator role
        for is_admin, data in zip(admin_list, _data_list):
            response = self.verify_content(is_admin=is_admin, data=data)
            if response:
                return response

        try:
            db_data_batch = [DBClass(**data.dict()) for data in _data_list]
            if self.api_call == True:
                with self.db_api(collection_name=COLLECTION_NAME):
                    DBClass.objects.insert(db_data_batch)
                
            else:
                with self.db_func(collection_name=COLLECTION_NAME):
                    DBClass.objects.insert(db_data_batch)
                    
            response = Response(status_code=201, detail=self.response_format.ok(f"Success : Batch Registered All <{len(request.create_requests)}> {DATA_OBJECT}"))
            logger.info(response.detail)

        # Handle common exceptions that might occur
        except (BaseException, Exception) as e:
            response = Response(status_code=500, detail=self.response_format.error(f"Common Error : Batch Registering {DATA_OBJECT}", str(e)))
            logger.error(response.detail)

        # Handle any other exceptions that might occur
        except:
            response = Response(status_code=500, detail=self.response_format.error(f"Unexpected Error : Batch Registering {DATA_OBJECT}"))
            logger.error(response.detail)
        
        return response
    
    # Update
    def update(self, request: UpdateRequest) -> Response:
        
        # Retrieve data from database
        data_filter = DataFilter(
            string_filter=DataStringFilter(**{f"{DATA_UID}_filter": [getattr(request, DATA_UID)]}),
            numeric_filter=DataNumericFilter(**{f"{DATA_STATUS}_min": 0})
        )
        conditions, sorting, filter_no = self.filter_formatter(data_filter=data_filter)
        db_data, response              = self.query_with_conditions(conditions=conditions, sorting=sorting, filter_no=filter_no)

        # Check if data exists in database
        if not db_data:
            response = Response(status_code=404, detail=self.response_format.error(f"Unfound Error : Updating {DATA_OBJECT} <{DATA_UID}: {getattr(request, DATA_UID)}>"))
            logger.error(response.detail)
            return response

        else:
            update_data = {key: value for key, value in request.update_data.__dict__.items() if value is not None}

            if not update_data:
                response = Response(status_code=404, detail=self.response_format.error(f"Unfound Error : Invalid Update {DATA_OBJECT}"))
                logger.error(response.detail)
                return response

            new_data = DataCreate(**db_data.first().to_dict())
            new_data.__dict__.update(update_data)
            if DATA_VERSION not in update_data.keys() and request.overwrite == False:
                new_data.__dict__.update(**{DATA_VERSION: getattr(new_data, DATA_VERSION)+1})

            # Validate parameters with creator role
            response = self.verify_content(is_admin=request.is_admin, data=new_data)
            if response:
                return response
            
            # Reset Parameters
            new_data.updated_at = datetime.now(timezone.utc)
            
            new_db_data = DBClass(**new_data.dict())
            
            try:
                old_id = str(uuid.uuid4())
                if self.api_call == True:
                    with self.db_api(collection_name=COLLECTION_NAME):
                        db_data.first().update(**{
                            f'set__{DATA_UID}':   old_id,
                            f'set__{DATA_STATUS}': 0
                        })
                        
                        # Add new version
                        new_db_data.save()
                        new_db_data.reload()
                        
                        if request.overwrite == True:
                            DBClass.objects(**{DATA_UID: old_id}).first().delete()
                            
                        
                else:
                    old_id = str(uuid.uuid4())
                    with self.db_func(collection_name=COLLECTION_NAME) as db:
                        db_data.first().update(**{
                            f'set__{DATA_UID}':   old_id,
                            f'set__{DATA_STATUS}': 0
                        })
                            
                        # Add new version
                        new_db_data.save()
                        new_db_data.reload()
                        
                        if request.overwrite == True:
                            DBClass.objects(**{DATA_UID: old_id}).first().delete()
                            

                response = Response(status_code=200, detail=self.response_format.ok(f"Success : Updated {DATA_OBJECT} <{DATA_UID}: {getattr(new_data, DATA_UID)}>"))
                logger.info(response.detail)

            # Handle common exceptions that might occur
            except (BaseException, Exception) as e:
                response = Response(status_code=500, detail=self.response_format.error(f"Common Error : Updating {DATA_OBJECT} <{DATA_UID}: {getattr(new_data, DATA_UID)}>", str(e)))
                logger.error(response.detail)

            # Handle any other exceptions that might occur
            except:
                response = Response(status_code=500, detail=self.response_format.error(f"Unexpected Error : Updating {DATA_OBJECT} <{DATA_UID}: {getattr(new_data, DATA_UID)}>"))
                logger.error(response.detail)

        return response

    # Activate
    def activate(self, request: CommonRequest) -> Response:
        try:        
            # Retrieve data from database
            data_filter = DataFilter(
                string_filter=DataStringFilter(**{f"{DATA_UID}_filter": [getattr(request, DATA_UID)]}),
                numeric_filter=DataNumericFilter(**{f"{DATA_STATUS}_max": 0, f"{DATA_STATUS}_min": 0})
            )
            conditions, sorting, filter_no = self.filter_formatter(data_filter=data_filter)
            db_data, response              = self.query_with_conditions(conditions=conditions, sorting=sorting, filter_no=filter_no)

            if db_data:
                if self.api_call == True:
                    with self.db_api(collection_name=COLLECTION_NAME):
                        db_data.first().update(**{f'set__{DATA_STATUS}': 1})
                else:
                    with self.db_func(collection_name=COLLECTION_NAME) as db:
                        db_data.first().update(**{f'set__{DATA_STATUS}': 1})
                        
                response = Response(status_code=200, detail=self.response_format.ok(f"Success : Activated {DATA_OBJECT} <{DATA_UID}: {getattr(request, DATA_UID)}>"))
                logger.info(response.detail)
            
            else:
                response = Response(status_code=404, detail=self.response_format.error(f"Unfound Error : Activating {DATA_OBJECT} <{DATA_UID}: {getattr(request, DATA_UID)}>"))
                logger.error(response.detail)

        # Handle common exceptions that might occur
        except (BaseException, Exception) as e:
            response = Response(status_code=500, detail=self.response_format.error(f"Common Error : Activating {DATA_OBJECT} <{DATA_UID}: {getattr(request, DATA_UID)}>", str(e)))
            logger.error(response.detail)

        # Handle any other exceptions that might occur
        except:
            response = Response(status_code=500, detail=self.response_format.error(f"Unexpected Error : Activating {DATA_OBJECT} <{DATA_UID}: {getattr(request, DATA_UID)}>"))
            logger.error(response.detail)

        return response

    # Batch Activate
    def batch_activate(self, request: BatchCommonRequest) -> Response:
        matched_conditions = []
        try:
            for _request in request.batch_requests:
                # Retrieve data from database
                data_filter = DataFilter(
                    string_filter=DataStringFilter(**{f"{DATA_UID}_filter": [getattr(_request, DATA_UID)]}),
                    numeric_filter=DataNumericFilter(**{f"{DATA_STATUS}_max": 0, f"{DATA_STATUS}_min": 0})
                )
                conditions, sorting, filter_no = self.filter_formatter(data_filter=data_filter)
                db_data, response              = self.query_with_conditions(conditions=conditions, sorting=sorting, filter_no=filter_no)

                # Check if data exists
                if db_data:
                    matched_conditions.append(Q(**{DATA_UID: getattr(_request, DATA_UID)}))
                else:
                    response = Response(status_code=404, detail=self.response_format.error(f"Unfound Error : Activating {DATA_OBJECT} <{DATA_UID}: {getattr(_request, DATA_UID)}>"))
                    logger.error(response.detail)
                    return response

            if not matched_conditions:
                response = Response(status_code=404, detail=self.response_format.error("Unfound Erorr : Failed to Load Conditions for Batch Activation"))
                logger.error(response.detail)
                return response
            else:
                nested_conditions = matched_conditions[0]
                for _condition in matched_conditions[1:]:
                    nested_conditions |= _condition

            # Update status
            if self.api_call == True:
                with self.db_api(collection_name=COLLECTION_NAME):
                    db_data = DBClass.objects(nested_conditions)
                    db_data.update(**{f'set__{DATA_STATUS}': 1})

            else:
                with self.db_func(collection_name=COLLECTION_NAME):
                    db_data = DBClass.objects(nested_conditions)
                    db_data.update(**{f'set__{DATA_STATUS}': 1})

            response = Response(status_code=200, detail=self.response_format.ok(f"Success : Batch Activated <{len(request.batch_requests)}> {DATA_OBJECT}"))
            logger.info(response.detail)

        # Handle common exceptions that might occur
        except (BaseException, Exception) as e:
            response = Response(status_code=500, detail=self.response_format.error(f"Common Error : Batch Activating {DATA_OBJECT}", str(e)))
            logger.error(response.detail)

        # Handle any other exceptions that might occur
        except:
            response = Response(status_code=500, detail=self.response_format.error(f"Unexpected Error : Batch Activating {DATA_OBJECT}"))
            logger.error(response.detail)

        return response

    # Deactivate
    def deactivate(self, request: CommonRequest) -> Response:
        try:
            # Retrieve data from database
            data_filter = DataFilter(
                string_filter=DataStringFilter(**{f"{DATA_UID}_filter": [getattr(request, DATA_UID)]}),
                numeric_filter=DataNumericFilter(**{f"{DATA_STATUS}_min": 1})
            )
            conditions, sorting, filter_no = self.filter_formatter(data_filter=data_filter)
            db_data, response              = self.query_with_conditions(conditions=conditions, sorting=sorting, filter_no=filter_no)

            if db_data:
                if self.api_call == True:
                    with self.db_api(collection_name=COLLECTION_NAME):
                        db_data.first().update(**{f'set__{DATA_STATUS}': 0})
                else:
                    with self.db_func(collection_name=COLLECTION_NAME) as db:
                        db_data.first().update(**{f'set__{DATA_STATUS}': 0})

                response = Response(status_code=200, detail=self.response_format.ok(f"Success : Deactivated {DATA_OBJECT} <{DATA_UID}: {getattr(request, DATA_UID)}>"))
                logger.info(response.detail)
            
            else:
                response = Response(status_code=404, detail=self.response_format.error(f"Unfound Error : Deactivating {DATA_OBJECT} <{DATA_UID}: {getattr(request, DATA_UID)}>"))
                logger.error(response.detail)

        # Handle common exceptions that might occur
        except (BaseException, Exception) as e:
            response = Response(status_code=500, detail=self.response_format.error(f"Common Error : Deactivating {DATA_OBJECT} <{DATA_UID}: {getattr(request, DATA_UID)}>", str(e)))
            logger.error(response.detail)

        # Handle any other exceptions that might occur
        except:
            response = Response(status_code=500, detail=self.response_format.error(f"Unexpected Error : Deactivating {DATA_OBJECT} <{DATA_UID}: {getattr(request, DATA_UID)}>"))
            logger.error(response.detail)

        return response

    # Batch Deactivate
    def batch_deactivate(self, request: BatchCommonRequest) -> Response:
        matched_conditions = []
        try:
            for _request in request.batch_requests:
                # Retrieve data from database
                data_filter = DataFilter(
                    string_filter=DataStringFilter(**{f"{DATA_UID}_filter": [getattr(_request, DATA_UID)]}),
                    numeric_filter=DataNumericFilter(**{f"{DATA_STATUS}_min": 1})
                )
                conditions, sorting, filter_no = self.filter_formatter(data_filter=data_filter)
                db_data, response              = self.query_with_conditions(conditions=conditions, sorting=sorting, filter_no=filter_no)

                # Check if data exists
                if db_data:
                    matched_conditions.append(Q(**{DATA_UID: getattr(_request, DATA_UID)}))
                else:
                    response = Response(status_code=404, detail=self.response_format.error(f"Unfound Error : Activating {DATA_OBJECT} <{DATA_UID}: {getattr(_request, DATA_UID)}>"))
                    logger.error(response.detail)
                    return response

            if not matched_conditions:
                return response
            else:
                nested_conditions = matched_conditions[0]
                for _condition in matched_conditions[1:]:
                    nested_conditions |= _condition

            # Update status
            if self.api_call == True:
                with self.db_api(collection_name=COLLECTION_NAME):
                    db_data = DBClass.objects(nested_conditions)
                    db_data.update(**{f'set__{DATA_STATUS}': 0})

            else:
                with self.db_func(collection_name=COLLECTION_NAME):
                    db_data = DBClass.objects(nested_conditions)
                    db_data.update(**{f'set__{DATA_STATUS}': 0})

            response = Response(status_code=200, detail=self.response_format.ok(f"Success : Batch Deactivated <{len(request.batch_requests)}> {DATA_OBJECT}"))
            logger.info(response.detail)
        
        # Handle common exceptions that might occur
        except (BaseException, Exception) as e:
            response = Response(status_code=500, detail=self.response_format.error(f"Common Error : Batch Deactivating {DATA_OBJECT}", str(e)))
            logger.error(response.detail)

        # Handle any other exceptions that might occur
        except:
            response = Response(status_code=500, detail=self.response_format.error(f"Unexpected Error : Batch Deactivating {DATA_OBJECT}"))
            logger.error(response.detail)

        return response

    # Delete
    def delete(self, request: CommonRequest) -> Response:
        response = self.deactivate(request=request)
        return response

    # Batch Delete
    def batch_delete(self, request: BatchCommonRequest) -> Response:
        response = self.batch_deactivate(request=request)
        return response

    # Drop
    def drop(self, request: CommonRequest) -> Response:
        try:
            # Retrieve data from database
            data_filter = DataFilter(
                string_filter=DataStringFilter(**{f"{DATA_UID}_filter": [getattr(request, DATA_UID)]}),
                numeric_filter=DataNumericFilter(**{f"{DATA_STATUS}_max": 0, f"{DATA_STATUS}_min": 0})
            )
            conditions, sorting, filter_no = self.filter_formatter(data_filter=data_filter)
            db_data, response              = self.query_with_conditions(conditions=conditions, sorting=sorting, filter_no=filter_no)
            
            if db_data:
                if self.api_call == True:
                    with self.db_api(collection_name=COLLECTION_NAME):
                        DBClass.objects(**{DATA_UID: getattr(request, DATA_UID)}).delete()
                else:
                    with self.db_func(collection_name=COLLECTION_NAME) as db:
                        DBClass.objects(**{DATA_UID: getattr(request, DATA_UID)}).delete()
                        
                response = Response(status_code=200, detail=self.response_format.ok(f"Success : Dropped {DATA_OBJECT} <{DATA_UID}: {getattr(request, DATA_UID)}>"))
                logger.info(response.detail)
            
            else:
                response = Response(status_code=404, detail=self.response_format.error(f"Unfound Error : Dropping {DATA_OBJECT} <{DATA_UID}: {getattr(request, DATA_UID)}>"))
                logger.error(response.detail)

        # Handle common exceptions that might occur
        except (BaseException, Exception) as e:
            response = Response(status_code=500, detail=self.response_format.error(f"Common Error : Dropping {DATA_OBJECT} <{DATA_UID}: {getattr(request, DATA_UID)}>", str(e)))
            logger.error(response.detail)

        # Handle any other exceptions that might occur
        except:
            response = Response(status_code=500, detail=self.response_format.error(f"Unexpected Error : Dropping {DATA_OBJECT} <{DATA_UID}: {getattr(request, DATA_UID)}>"))
            logger.error(response.detail)

        return response

    # Batch Drop
    def batch_drop(self, request: BatchCommonRequest) -> Response:
        matched_conditions = []
        try:
            for _request in request.batch_requests:
                # Retrieve data from database
                data_filter = DataFilter(
                    string_filter=DataStringFilter(**{f"{DATA_UID}_filter": [getattr(_request, DATA_UID)]}),
                    numeric_filter=DataNumericFilter(**{f"{DATA_STATUS}_max": 0, f"{DATA_STATUS}_min": 0})
                )
                conditions, sorting, filter_no = self.filter_formatter(data_filter=data_filter)
                db_data, response              = self.query_with_conditions(conditions=conditions, sorting=sorting, filter_no=filter_no)

                # Check if data exists
                if db_data:
                    matched_conditions.append(Q(**{DATA_UID: getattr(_request, DATA_UID)}))
                else:
                    response = Response(status_code=404, detail=self.response_format.error(f"Unfound Error : Dropping {DATA_OBJECT} <{DATA_UID}: {getattr(_request, DATA_UID)}>"))
                    logger.error(response.detail)
                    return response

            if not matched_conditions:
                response = Response(status_code=404, detail=self.response_format.error(f"Unfound Error : Dropping {DATA_OBJECT}"))
                logger.error(response.detail)
                return response
            else:
                nested_conditions = matched_conditions[0]
                for _condition in matched_conditions[1:]:
                    nested_conditions |= _condition

            # Delete data
            if self.api_call == True:
                with self.db_api(collection_name=COLLECTION_NAME):
                    db_data = DBClass.objects(nested_conditions)
                    db_data.delete()
            else:
                with self.db_func(collection_name=COLLECTION_NAME) as db:
                    db_data = DBClass.objects(nested_conditions)
                    db_data.delete()

            response = Response(status_code=200, detail=self.response_format.ok(f"Success : Batch Dropped <{len(request.batch_requests)}> {DATA_OBJECT}"))
            logger.info(response.detail)

        # Handle common exceptions that might occur
        except (BaseException, Exception) as e:
            response = Response(status_code=500, detail=self.response_format.error(f"Common Error : Batch Dropping {DATA_OBJECT}", str(e)))
            logger.error(response.detail)

        # Handle any other exceptions that might occur
        except:
            response = Response(status_code=500, detail=self.response_format.error(f"Unexpected Error : Batch Dropping {DATA_OBJECT}"))
            logger.error(response.detail)

        return response


    """
        System Operation
    """
    # System Drop All Inactive
    def drop_inactive_by_system(self) -> Response:
        try:
            if self.api_call == True:
                with self.db_api(collection_name=COLLECTION_NAME):
                    DBClass.objects(**{DATA_STATUS: 0}).delete()
            else:
                with self.db_func(collection_name=COLLECTION_NAME) as db:
                    DBClass.objects(**{DATA_STATUS: 0}).delete()

            response = Response(status_code=200, detail=self.response_format.ok(f"Success : Dropped All Inactive {DATA_OBJECT}"))
            logger.info(response.detail)

        # Handle common exceptions that might occur
        except (BaseException, Exception) as e:
            response = Response(status_code=500, detail=self.response_format.error(f"Common Error : Dropping Inactive {DATA_OBJECT}", str(e)))
            logger.error(response.detail)

        # Handle any other exceptions that might occur
        except:
            response = Response(status_code=500, detail=self.response_format.error(f"Unexpected Error : Dropping Inactive {DATA_OBJECT}"))
            logger.error(response.detail)

        return response

    # System Query Data
    def query_data_by_system(self, request: SystemDataRequest) -> tuple[SystemDataResponse, Response]:
        response_data = SystemDataResponse(**request.__dict__)

        if request.data_filter:
            data_filter = DataFilter(**request.data_filter.__dict__)
        else:
            data_filter = DataFilter()

        conditions, sorting, filter_no = self.filter_formatter(data_filter=data_filter)
        db_data, response              = self.query_with_conditions(conditions=conditions, sorting=sorting, filter_no=filter_no)
        
        if db_data:
            response_data.__dict__.update(filtered_data=[SecretData(**data.to_dict()) for data in db_data], data_count=len(db_data))
            response = Response(status_code=200, detail=self.response_format.ok("Success : Get Filtered Data for System"))
       
        return response_data, response

    def import_data_by_system(self, request: DataImportRequest) -> Response:
        config = request.io_config
        if request.backup == True:
            response = self.backup_data_by_system(request=DataBackupRequest())

            if response.status_code >= SETTINGS.STAT.SUCC_CODE_END:
                response = Response(status_code=500, detail=self.response_format.error(f"Import Error : Failed to Backup Current DB before Import"))
                logger.error(response.detail)
                return response 
            
        response = self.import_data(config=config)

        return response

    # System Export Data
    def export_data_by_system(self, request: DataExportRequest) -> Response:

        # Get Data
        if request.data_filter:
            data_filter = DataFilter(**request.data_filter.__dict__)
        else:
            data_filter = DataFilter()

        conditions, sorting, filter_no = self.filter_formatter(data_filter=data_filter)
        db_data, response              = self.query_with_conditions(conditions=conditions, sorting=sorting, filter_no=filter_no)

        if db_data:
            filtered_data = [SecretData(**data.to_dict()).__dict__ for data in db_data]
            response = Response(status_code=200, detail=self.response_format.ok("Success : Get Filtered Data for System"))
        else:
            return response # Return No Data

        response = self.export_data(
            config=request.io_config, 
            data=filtered_data, 
            include_datetime=request.include_datetime
        )

        return response    

    # System Backup Data
    def backup_data_by_system(self, request: DataBackupRequest) -> Response:
        # Get Data
        if request.data_filter:
            data_filter = DataFilter(**request.data_filter.__dict__)
        else:
            data_filter = DataFilter()

        conditions, sorting, filter_no = self.filter_formatter(data_filter=data_filter)
        db_data, response              = self.query_with_conditions(conditions=conditions, sorting=sorting, filter_no=filter_no)

        if db_data:
            filtered_data = [SecretData(**data.to_dict()).__dict__ for data in db_data]
            response = Response(status_code=200, detail=self.response_format.ok("Success : Get Filtered Data for System"))
        else:
            return response # Return No Data

        response = self.backup_data(config=request.backup_config, data=filtered_data)
    
        return response  

    # System List Backup Table Name
    def list_backup_by_system(self, request: DataBackupListRequest) -> tuple[DataBackupListResponse, Response]:
        response_table = DataBackupListResponse(**request.__dict__)
        config=request.backup_config

        # Detect Backup Configuration
        if config is None:
            config = DEFAULT_BACKUP_CONFIG.copy()
            response = Response(status_code=200, detail=self.response_format.ok("Info : Empty Backup Configuration >>> Use System Default Backup Configuration"))
            logger.info(response.detail)

        # Check DB Format
        if not config.format.upper() in SETTINGS.BKUP.DB_FORM:
            response = Response(status_code=500, detail=self.response_format.error("Data Backup Error : Invalid Data Backup Format"))
            logger.error(response.detail)       
            return response_table, response 
         
        # Load Backup DB Config
        try:
            table_prefix = config.table
            db_config = BackupDatabaseConfiguration(**config.__dict__)
            engine = init_bkup_db_engine(db_config) # init engine
            _table_list = sqlalchemy.inspect(engine).get_table_names()
            table_list  = [table for table in _table_list if table_prefix in table]
            
            if table_list:
                table_list.sort(reverse=True)
                response_table.table_list = table_list
            response = Response(status_code=200, detail=self.response_format.ok("Success : Retrieved Table List from Backup DB"))
            return response_table, response
        
        except:
            response = Response(status_code=500, detail=self.response_format.error(f"Unexpected Error : Failed to Init Backup DB <{db_config.name}>"))
            logger.error(response.detail)
            return response_table, response

    # System Restore Data
    def restore_backup_by_system(self, request: DBRestoreRequest) -> Response:
        config = request.restore_config
        response = self.restore_data(config=config)
        return response


    """
        User Operation
    """
    def query_data_by_user(self, request: UserDataRequest) -> tuple[UserDataResponse, Response]:
        response_data = UserDataResponse(**request.__dict__)

        data_filter = DataFilter(**request.data_filter.__dict__)
        conditions, sorting, filter_no = self.filter_formatter(data_filter=data_filter)

        if not conditions:
            response = Response(status_code=500, detail=self.response_format.error(f"Missing Filter Error : Filter cannot be All Empty"))
            logger.error(response.detail)
            return response_data, response

        else:
            db_data, response = self.query_with_conditions(conditions=conditions, sorting=sorting, filter_no=filter_no)

        if db_data:
            response_data.__dict__.update(filtered_data=[UserData(**data.to_dict()) for data in db_data])
            response = Response(status_code=200, detail=self.response_format.ok("Success : Get Permitted Data for User"))
       
        return response_data, response


    """
        Class Operation
    """
    # Import Data
    def import_data(self, config: schema.IOConfig | None) -> Response:         
        
        # 1. Detect IO Configuration
        if config is None:
            response = Response(status_code=500, detail=self.response_format.ok("Import Error : Empty Import Configuration"))
            logger.error(response.detail)
            return response

        # 2.1. Init Import File
        if config.format.upper() in SETTINGS.IMPT.FILE_FORM:
            try:
                file_extn = '.' + config.format.lower()
                if file_extn in config.file_name.lower() and len(config.file_name.lower().split(file_extn)) == 2:
                    file_name = config.file_name
                else:
                    file_name = config.file_name + file_extn

                if not config.file_sdir:
                    file_root = config.file_rdir
                else:
                    file_root = os.path.join(config.file_rdir, config.file_sdir)

                file_path = os.path.join(file_root, file_name)

            # Handle missing parameters
            except TypeError as e:
                response = Response(status_code=500, detail=self.response_format.error(f"Missing Parameter Error : Importing Data", str(e)))
                logger.error(response.detail)
                return response

            # Handle any other exceptions that might occur
            except:
                response = Response(status_code=500, detail=self.response_format.error(f"Unexpected Error : Importing Data"))
                logger.error(response.detail)
                return response

            if not os.path.isfile(file_path):
                response = Response(status_code=500, detail=self.response_format.error(f"Import Error : Cannot Find Import File {file_path}"))
                logger.error(response.detail)
                return response
        
        # # 2.2. Init Import DB
        # elif config.format.upper() in SETTINGS.IMPT.DB_FORM:
        #     try:
        #         db_config = IODatabaseConfiguration(**config.__dict__)
        #         engine = init_io_db_engine(db_config) # init engine

        #     except:
        #         response = Response(status_code=500, detail=self.response_format.error(f"Import Error : Failed to Init Import DB <{db_config.name}>"))
        #         logger.error(response.detail)
        #         return response

        # 3. Import Data
        # 3.1. Import to JSON
        if config.format.upper() == 'JSON':
            try:
                with open(file_path, 'r', encoding='utf-8') as json_file:
                    io_data = json.load(json_file)
            
                response = Response(status_code=200, detail=self.response_format.ok(f"Import in Progress : Retrieved Data from <{file_path}>"))
                logger.info(response.detail)

            except:
                response = Response(status_code=500, detail=self.response_format.error(f"Data Import Error : Failed to Retrieve Data from <{file_path}>"))
                logger.error(response.detail)
                return response
            
        # 3.2. Import to CSV
        elif config.format.upper() == 'CSV':
            try:
                with open(file_path, mode='r', newline='', encoding='utf-8-sig') as csv_file:
                    reader = csv.DictReader(csv_file)  # Use DictReader to create dictionaries
                    io_data = [row for row in reader] 

                response = Response(status_code=200, detail=self.response_format.ok(f"Import in Progress : Retrieved Data from <{file_path}>"))
                logger.info(response.detail)

            except:
                response = Response(status_code=500, detail=self.response_format.error(f"Data Import Error : Failed to Retrieve Data from <{file_path}>"))
                logger.error(response.detail)    
                return response
    
        # # 3.3. Import to DB
        # elif config.format.upper() in SETTINGS.IMPT.DB_FORM: # Valid DB
        #     _table_list = sqlalchemy.inspect(engine).get_table_names()
        #     _table_name = [table for table in _table_list if table == config.table]
            
        #     # Check if Table Exists
        #     if not _table_name:
        #         response = Response(status_code=500, detail=self.response_format.error("Data Import Error : Cannot Find the Table in Import DB"))
        #         logger.error(response.detail)
        #         return response
        
        #     # Retrieve Import Data
        #     try:
        #         table_name = _table_name[0]
        #         IODBClass = set_io_db_table(table_name=table_name, engine=engine)
        #         with get_io_db_func(engine=engine) as db:
        #             _io_data = db.query(IODBClass).all()
        #         io_data = [CreateRequest(**_data.__dict__) for _data in _io_data]
        #         response = Response(status_code=200, detail=self.response_format.ok("Import in Progress : Retrieved Data from Import DB"))
        #         logger.info(response.detail)

        #     except:
        #         response = Response(status_code=500, detail=self.response_format.error(f"Unexpected Error : Failed to Get Data from Import DB <{db_config.name}>"))
        #         logger.error(response.detail)
        #         return response

        # 3.X. Unknown Import Format
        else:
            response = Response(status_code=500, detail=self.response_format.error("Data Import Error : Invalid Data Import Format"))
            logger.error(response.detail)       
            return response 

        # 4. Remove Current Data from DB
        try:
            if self.api_call == True:
                with self.db_api(collection_name=COLLECTION_NAME):
                    DBClass.objects().delete()
                # db = self.db_api
                # db.query(DBClass).delete()
                # db.commit()
            else:
                with self.db_func(collection_name=COLLECTION_NAME) as db:
                    DBClass.objects().delete()
                    # db.query(DBClass).delete()
                    # db.commit()

            response = Response(status_code=200, detail=self.response_format.ok("Import in Progress : Removed Current Data from DB"))
            logger.info(response.detail)

        # 4.X. Handle common exceptions that might occur
        except (BaseException, Exception) as e:
            response = Response(status_code=500, detail=self.response_format.error(f"Common Error : Removing Current Data from DB during Importing Data", str(e)))
            logger.error(response.detail)
            return response

        # 4.X. Handle any other exceptions that might occur
        except:
            response = Response(status_code=500, detail=self.response_format.error(f"Unexpected Error : Removing Current Data from DB during Importing Data"))
            logger.error(response.detail)
            return response

        # 5. Add Data to Current DB
        batch_create_request = BatchCreateRequest(
            create_requests=[
                CreateRequest(
                    data=DataCreate(
                        **_data
                        )
                ) for _data in io_data
            ]
        )
        response = self.batch_create(request=batch_create_request)
        if response.status_code < SETTINGS.STAT.SUCC_CODE_END:
            response = Response(status_code=200, detail=self.response_format.ok(f"Success : Imported Data from Import DB <{config.table}>"))
            logger.info(response.detail)
        else:
            response = Response(status_code=500, detail=self.response_format.error(f"Unexpected Error : Importing Data to Current DB"))
            logger.error(response.detail)

        return response

    # Export Data
    def export_data(self, config: schema.IOConfig | None, data: list, include_datetime: bool=True) -> Response:         
        
        # 1. Detect Export Configuration
        if config is None:
            config = DEFAULT_EXPORT_CONFIG.copy()
            response = Response(status_code=200, detail=self.response_format.ok("Info : Empty Export Configuration >>> Use System Default Export Configuration"))
            logger.info(response.detail)

        # 2.1. Init Export File
        if config.format.upper() in SETTINGS.EXPT.FILE_FORM:
            try:
                if include_datetime == True:
                    file_name = config.file_name + '_' + datetime.now(timezone.utc).strftime("%Y-%m-%d-%H-%M-%S")
                file_name = '.'.join([file_name, config.format.lower()])

                if not config.file_sdir:
                    file_root = config.file_rdir
                else:
                    file_root = os.path.join(config.file_rdir, config.file_sdir)

                file_path = os.path.join(file_root, file_name)

            # Handle missing parameters
            except TypeError as e:
                response = Response(status_code=500, detail=self.response_format.error(f"Missing Parameter Error : Exporting Data", str(e)))
                logger.error(response.detail)
                return response

            # Handle any other exceptions that might occur
            except:
                response = Response(status_code=500, detail=self.response_format.error(f"Unexpected Error : Exporting Data"))
                logger.error(response.detail)
                return response

            try:
                os.makedirs(file_root, exist_ok=True)
            except:
                response = Response(status_code=500, detail=self.response_format.error(f"Unexpected Error : Failed to Create Directory when Exporting Data"))
                logger.error(response.detail)
                return response
        
        # # 2.2. Init Export DB
        # elif config.format.upper() in SETTINGS.EXPT.DB_FORM:
        #     try:
        #         if include_datetime == True:
        #             config.table = config.table + '_' + datetime.now(timezone.utc).strftime("%Y-%m-%d-%H-%M-%S")
            
        #     # Handle missing parameters
        #     except TypeError as e:
        #         response = Response(status_code=500, detail=self.response_format.error(f"Missing Parameter Error : Exporting Data", str(e)))
        #         logger.error(response.detail)
        #         return response

        #     # Handle any other exceptions that might occur
        #     except:
        #         response = Response(status_code=500, detail=self.response_format.error(f"Unexpected Error : Exporting Data"))
        #         logger.error(response.detail)
        #         return response
 
        # 2.3. Handle Unknown Format
        else:
            response = Response(status_code=500, detail=self.response_format.error("Data Export Error : Invalid Data Export Format"))
            logger.error(response.detail)       
            return response 

        # 3. Export Data
        # 3.1. Export to JSON
        if config.format.upper() == 'JSON':
            try:
                with open(file_path, 'w', encoding="utf-8") as json_file:
                    json.dump(data, json_file, cls=ComplexEncoder, ensure_ascii=False, indent=4)
            
                response = Response(status_code=200, detail=self.response_format.ok(f"Success : Exported Data to <{file_path}>"))
                logger.info(response.detail)

            except:
                response = Response(status_code=500, detail=self.response_format.error(f"Data Export Error : Failed to Export Data to <{file_path}>"))
                logger.error(response.detail)
            
        # 3.2. Export to CSV
        elif config.format.upper() == 'CSV':
            try:
                with open(file_path, 'w',  newline='', encoding="utf-8-sig") as csv_file:
                    fieldnames = data[0].keys()
                    writer = csv.DictWriter(csv_file, fieldnames=fieldnames)
                    writer.writeheader()    # Write the header
                    writer.writerows(data)  # Write the data
                response = Response(status_code=200, detail=self.response_format.ok(f"Success : Exported Data to <{file_path}>"))
                logger.info(response.detail)

            except:
                response = Response(status_code=500, detail=self.response_format.error(f"Data Export Error : Failed to Export Data to <{file_path}>"))
                logger.error(response.detail)      
            
        # # 3.3. Export to DB
        # elif config.format.upper() in SETTINGS.EXPT.DB_FORM: # Valid DB
        #     db_config = IODatabaseConfiguration(**config.__dict__)

        #     try:
        #         engine = init_io_db_engine(db_config) # init engine
        #         IODBClass = set_io_db_table(table_name=db_config.table, engine=engine) # set table name
        #         db_data = [IODBClass(**CreateRequest(**_data).__dict__) for _data in data]
            
        #     except:
        #         response = Response(status_code=500, detail=self.response_format.error(f"Unexpected Error : Failed to Init Export DB <{db_config.name}>"))
        #         logger.error(response.detail)
        #         return response

        #     try:
        #         with get_io_db_func(engine=engine) as db:
        #             db.add_all(db_data)
        #             db.commit()
        #         response = Response(status_code=201, detail=self.response_format.ok(f"Success : Exported Data to DB <{db_config.table}>"))
        #         logger.info(response.detail)
            
        #     except:
        #         response = Response(status_code=500, detail=self.response_format.error(f"Unexpected Error : Failed to Export Data to DB <{db_config.table}>"))
        #         logger.error(response.detail)

        # 3.X. Unknown Export Format
        else:
            response = Response(status_code=500, detail=self.response_format.error("Data Export Error : Invalid Data Export Format"))
            logger.error(response.detail)       

        return response

    # Backup Data
    def backup_data(self, config: schema.BackupConfig | None, data: list) -> Response:         
        
        # 1. Detect Backup Configuration
        if config is None:
            config = DEFAULT_BACKUP_CONFIG.copy()
            response = Response(status_code=200, detail=self.response_format.ok("Info : Empty Backup Configuration >>> Use System Default Backup Configuration"))
            logger.info(response.detail)

        # 2. Check DB Format
        if not config.format.upper() in SETTINGS.BKUP.DB_FORM:
            response = Response(status_code=500, detail=self.response_format.error("Data Backup Error : Invalid Data Backup Format"))
            logger.error(response.detail)       
            return response 
         
        # Load Backup DB Config
        try:
            table_prefix = config.table
            config.table = config.table + '_' + datetime.now(timezone.utc).strftime("%Y-%m-%d-%H-%M-%S")
            db_config = BackupDatabaseConfiguration(**config.__dict__)
            engine = init_bkup_db_engine(db_config) # init engine
            BackupDBClass = set_bkup_db_table(table_name=db_config.table, engine=engine) # set table name
            db_data = [BackupDBClass(**DataCreate(**_data).__dict__) for _data in data]
        
        except:
            response = Response(status_code=500, detail=self.response_format.error(f"Unexpected Error : Failed to Init Backup DB <{db_config.name}>"))
            logger.error(response.detail)
            return response

        # Add Data to Backup DB
        try:
            with get_bkup_db_func(engine=engine) as db:
                db.add_all(db_data)
                db.commit()
            response = Response(status_code=201, detail=self.response_format.ok(f"Success : Backuped Data to DB <{db_config.table}>"))
            logger.info(response.detail)

        except:
            response = Response(status_code=500, detail=self.response_format.error(f"Unexpected Error : Failed to Backup Data to DB <{db_config.table}>"))
            logger.error(response.detail)

            return response

        # # Check if Backup Limit is Reached
        # if config.limit and config.limit > 0:
        #     logger.info(f"Backup Progress : Backup Limit <{config.limit}> is Set. Start Dropping Previous Backup Tables")
        #     _table_list = sqlalchemy.inspect(engine).get_table_names()
        #     table_list  = [table for table in _table_list if table_prefix in table]
        #     table_list.sort(reverse=True)

        #     if len(table_list) > config.limit:
        #         table_to_drop = table_list[config.limit:]
        #         for table_name in table_to_drop:
        #             try:
        #                 with get_bkup_db_func(engine=engine) as db:
        #                     table = get_bkup_db_table(table_name=table_name, engine=engine)
        #                     table.drop(engine)
        #                     db.commit()
        #                 response = Response(status_code=200, detail=self.response_format.ok(f"Success : Dropped Backup DB Table <{table_name}>"))
        #                 logger.info(response.detail)
                    
        #             except:
        #                 response = Response(status_code=500, detail=self.response_format.error(f"Unexpected Error : Failed to Drop Backup DB Table <{table_name}>"))
        #                 logger.error(response.detail)
        #                 return response
                    
        #         response = Response(status_code=200, detail=self.response_format.ok(f"Completed Backup : Dropped Previous <{len(table_list)-config.limit}> Tables during Data Backup. <{len(sqlalchemy.inspect(engine).get_table_names())}> Backup Tables Remained"))
        #         logger.info(response.detail)    

        #     else:
        #         response = Response(status_code=200, detail=self.response_format.ok(f"Completed Backup : Number of Backup Table is Less than Backup Limit. No Actions are Required"))
        #         logger.info(response.detail)

        return response

    # Restore Data
    def restore_data(self, config: schema.RestoreConfig | None) -> Response:         

        # 1. Detect Restore Configuration
        # Check if table name is given (table_name denotes backup version)
        if config.table is None:
            response = Response(status_code=404, detail=self.response_format.error("Data Restore Error : Unfound Table Name"))
            logger.error(response.detail)
            return response
        else:
            _table = config.table

        # Check if Use Default Restore DB Config
        db_config = {key: value for key, value in config.__dict__.items() if key != "table" and value is not None}
        if not db_config:
            config = DEFAULT_BACKUP_CONFIG.copy()
            config.table = _table

            response = Response(status_code=200, detail=self.response_format.ok("Info : Empty Restore Configuration >>> Use System Default Restore Configuration"))
            logger.info(response.detail)

        # Check DB Format
        if not config.format.upper() in SETTINGS.BKUP.DB_FORM:
            response = Response(status_code=500, detail=self.response_format.error("Data Restore Error : Invalid Data Restore Format"))
            logger.error(response.detail)
            return response

        # 2. Init Restore DB 
        try:
            db_config = BackupDatabaseConfiguration(**config.__dict__)
            engine = init_bkup_db_engine(db_config) # init engine
            _table_list = sqlalchemy.inspect(engine).get_table_names()
            _table_name = [table for table in _table_list if table == config.table]
        
        except:
            response = Response(status_code=500, detail=self.response_format.error(f"Unexpected Error : Failed to Init Backup DB <{db_config.name}>"))
            logger.error(response.detail)
            return response

        # 3. Check if Table Exists
        if not _table_name:
            response = Response(status_code=500, detail=self.response_format.error("Data Restore Error : Cannot Find the Table in Backup DB"))
            logger.error(response.detail)
            return response

        # 4. Retrieve Data from BackupDB
        try:
            table_name = _table_name[0]
            BackupDBClass = set_bkup_db_table(table_name=table_name, engine=engine)
            with get_bkup_db_func(engine=engine) as db:
                backup_data = db.query(BackupDBClass).all()
            restore_data = [DataCreate(**_data.__dict__) for _data in backup_data]
            response = Response(status_code=200, detail=self.response_format.ok("Restore in Progress : Retrieved Data from Backup DB"))
            logger.info(response.detail)

        except:
            response = Response(status_code=500, detail=self.response_format.error(f"Unexpected Error : Failed to Get Data from Backup DB <{db_config.name}>"))
            logger.error(response.detail)
            return response

        # 6. Remove Current Data from DB
        try:
            if self.api_call == True:
                with self.db_api(collection_name=COLLECTION_NAME) as db:
                    db.query(DBClass).delete()
                    db.commit()
            else:
                with self.db_func(collection_name=COLLECTION_NAME) as db:
                    db.query(DBClass).delete()
                    db.commit()

            response = Response(status_code=200, detail=self.response_format.ok("Restore in Progress : Removed Current Data from DB"))
            logger.info(response.detail)

        # Handle common exceptions that might occur
        except (BaseException, Exception) as e:
            response = Response(status_code=500, detail=self.response_format.error(f"Common Error : Removing Current Data from DB during Restoring Backup", str(e)))
            logger.error(response.detail)
            return response

        # Handle any other exceptions that might occur
        except:
            response = Response(status_code=500, detail=self.response_format.error(f"Unexpected Error : Removing Current Data from DB during Restoring Backup"))
            logger.error(response.detail)
            return response

        # 7. Add Restore Data to Current DB
        batch_create_request = BatchCreateRequest(
            create_requests=[
                CreateRequest(
                    data=DataCreate(**_data)
                ) for _data in restore_data
            ]
        )
        response = self.batch_create(request=batch_create_request)
        if response.status_code < SETTINGS.STAT.SUCC_CODE_END:
            response = Response(status_code=200, detail=self.response_format.ok(f"Success : Restored Data from Backup DB <{config.table}>"))
            logger.info(response.detail)
        else:
            response = Response(status_code=500, detail=self.response_format.error(f"Unexpected Error : Restoring Data to Current DB"))
            logger.error(response.detail)

        return response

    # Verify Content
    def verify_content(self, is_admin: bool, data: DataCreate) -> Response | None:
        # try:
        #     for item in [data.knowledge_parameters, data.knowledge_secrets]:
        #         if item: 
        #             if isinstance(item, dict):
        #                 for para, para_config in item.items():
        #                     for key, value in para_config.items():
        #                         if is_admin == False and key in SETTINGS.CSTR.ADMIN_PARAM_KEY:
        #                             response = Response(status_code=500, detail=self.response_format.error(f"Input Error : Invalid Permission for using Key <{key}>"))
        #                             logger.error(response.detail)
        #                             return response
                                
        #                         # if key in SETTINGS.CSTR.VALID_PARAM_KEY:
        #                         #     if value not in SETTINGS.CSTR.VALID_PARAM_VAL:
        #                         #         response = Response(status_code=500, detail=self.response_format.error(f"Input Error : Invalid Parameter Value <{value}>"))
        #                         #         logger.error(response.detail)      
        #                         #         return response                              

        #                         elif key not in SETTINGS.CSTR.ADMIN_PARAM_KEY:
        #                             response = Response(status_code=500, detail=self.response_format.error(f"Input Error : Invalid Parameter Key <{key}>"))
        #                             logger.error(response.detail)
        #                             return response

        #             else:
        #                 response = Response(status_code=500, detail=self.response_format.error(f"Input Error : Parameter Must be Either None or Dict"))
        #                 logger.error(response.detail)
        #                 return response

        # except:
        #     response = Response(status_code=500, detail=self.response_format.error(f"Unexpected Error : Validating Parameters for Knowledge"))
        #     logger.error(response.detail)
        #     return response

        return None

    # Perform Query
    def query_with_conditions(self, conditions: list, sorting: list, filter_no: int) -> tuple[list[DBClass] | None, Response]:
        db_data = None

        if not conditions:
            response = Response(status_code=500, detail=self.response_format.error("Condition Filter Error : Condition Filter is Empty"))
            logger.error(response.detail)
            return db_data, response
        else:
            nested_conditions = conditions[0]
            for _condition in conditions[1:]:
                nested_conditions &= _condition

        try:
            if self.api_call == True:
                with self.db_api(collection_name=COLLECTION_NAME):
                    if not filter_no or filter_no < 0:
                        db_data = DBClass.objects(nested_conditions).order_by(*sorting)
                    else:
                        db_data = DBClass.objects(nested_conditions).order_by(*sorting).limit(filter_no)
            else:
                with self.db_func(collection_name=COLLECTION_NAME):
                    if not filter_no or filter_no < 0:
                        db_data = DBClass.objects(nested_conditions).order_by(*sorting)
                    else:
                        db_data = DBClass.objects(nested_conditions).order_by(*sorting).limit(filter_no)
            
            if db_data:
                response = Response(status_code=200, detail=self.response_format.ok(f"Success : Retrieved Filtered {DATA_OBJECT} from DB"))
            else:
                response = Response(status_code=200, detail=self.response_format.ok(f"Success : No Matched {DATA_OBJECT} from DB"))

        # Handle common exceptions that might occur
        except (BaseException, Exception) as e:
            response = Response(status_code=500, detail=self.response_format.error(f"Common Error : Getting {DATA_OBJECT}", str(e)))
            logger.error(response.detail)

        # Handle any other exceptions that might occur
        except:
            response = Response(status_code=500, detail=self.response_format.error(f"Unexpected Error : Getting {DATA_OBJECT}"))
            logger.error(response.detail)

        return db_data, response

    # Filter Formatter
    def filter_formatter(self, data_filter: DataFilter) -> tuple[list, list, int]:

        def filter_by_entity(db_model: DBClass, column_name: str, entity_filter: list[str]) -> list:
            conditions = []

            try:
                column = getattr(db_model, column_name)
            except AttributeError as e:
                response = Response(status_code=404, detail=self.response_format.error(f"Attribute Error : Unrecognized Attribute <{column_name}>. The Attribute for ListFilter must be <key_or> or <key_and>", str(e)))
                logger.error(response.detail)
                return conditions
            except Exception:
                response = Response(status_code=500, detail=self.response_format.error(f"Unexpected Error : Unexpected Error during Parsing Attribute <{column_name}> in ListFilter"))
                logger.error(response.detail)       
                return conditions
            
            # Create conditions to match the tag surrounded by delimiters
            conditions.append(Q(**{f"{column_name}__in": entity_filter}))
            
            return conditions

        def filter_by_byte(db_model: DBClass, column_name: str, entity_filter: list[str]) -> list:
            conditions = []

            try:
                column = getattr(db_model, column_name)
            except AttributeError as e:
                response = Response(status_code=404, detail=self.response_format.error(f"Attribute Error : Unrecognized Attribute <{column_name}>. The Attribute for ByteFilter must be <key_filter>", str(e)))
                logger.error(response.detail)
                return conditions
            except Exception:
                response = Response(status_code=500, detail=self.response_format.error(f"Unexpected Error : Unexpected Error during Parsing Attribute <{column_name}> in ByteFilter"))
                logger.error(response.detail)       
                return conditions
            
            _encoded_values = []
            for value in entity_filter:
                encoded_value, response = CryptoServiceManager(api_call=False).encode_content(content=value)
                if response.status_code >= SETTINGS.STAT.SUCC_CODE_END:
                    logger.error(response.detail)
                    return conditions
                _encoded_values.append(encoded_value)

            conditions = [Q(**{f"{column_name}__in": encoded_value})]
            
            return conditions

        conditions = []
        sorting    = []

        # Parsing conditions

        # String Filter
        if data_filter.string_filter is not None:
            string_suffix = '_filter'
            for key, value in data_filter.string_filter.__dict__.items():
                if value is not None:
                    column_name = key.split(string_suffix)[0]
                    try:
                        conditions.append(Q(**{f"{column_name}__in": value}))
                    except AttributeError as e:
                        response = Response(status_code=404, detail=self.response_format.error(f"Attribute Error : Unrecognized Attribute <{column_name}>. The Attribute for StringFilter must be <key_filter>", str(e)))
                        logger.error(response.detail)
                    except Exception:
                        response = Response(status_code=500, detail=self.response_format.error(f"Unexpected Error : Unexpected Error during Parsing Attribute <{column_name}> in StringFilter"))
                        logger.error(response.detail)                    


        # Numeric Filter
        if data_filter.numeric_filter is not None:
            min_suffix = '_min'
            max_suffix = '_max'
            for key, value in data_filter.numeric_filter.__dict__.items():
                if value is not None:
                    if min_suffix in key:
                        column_name = key.split(min_suffix)[0]
                        try:
                            conditions.append(Q(**{f"{column_name}__gte": value}))
                        except AttributeError as e:
                            response = Response(status_code=404, detail=self.response_format.error(f"Attribute Error : Unrecognized Attribute <{column_name}>. The Attribute for NumericFilter must be <key_min> or <key_max>", str(e)))
                            logger.error(response.detail)
                        except Exception:
                            response = Response(status_code=500, detail=self.response_format.error(f"Unexpected Error : Unexpected Error during Parsing Attribute <{column_name}> in NumericFilter"))
                            logger.error(response.detail)              

                    elif max_suffix in key:
                        column_name = key.split(max_suffix)[0]
                        try:
                            conditions.append(Q(**{f"{column_name}__lte": value}))
                        except AttributeError as e:
                            response = Response(status_code=404, detail=self.response_format.error(f"Attribute Error : Unrecognized Attribute <{column_name}>. The Attribute for NumericFilter must be <key_min> or <key_max>", str(e)))
                            logger.error(response.detail)
                        except Exception:
                            response = Response(status_code=500, detail=self.response_format.error(f"Unexpected Error : Unexpected Error during Parsing Attribute <{column_name}> in NumericFilter"))
                            logger.error(response.detail)    

                    else:
                        response = Response(status_code=404, detail=self.response_format.error(f"Attribute Error : Unrecognized Attribute <{column_name}>. The Attribute Suffix for NumericFilter must be Either _min or _max"))
                        logger.error(response.detail)

        # List Filter
        if data_filter.list_filter is not None:
            or_suffix  = '_or'
            and_suffix = '_and'
            for key, value in data_filter.list_filter.__dict__.items():
                if value is not None:
                    if or_suffix in key:
                        column_name = key.split(or_suffix)[0]
                        _conditions = filter_by_entity(db_model=DBClass, column_name=column_name, entity_filter=value)
                        if _conditions:
                            nested_conditions = _conditions[0]
                            for _condition in _conditions:
                                nested_conditions |= _condition
                            conditions.append(nested_conditions)

                    elif and_suffix in key:
                        column_name = key.split(and_suffix)[0]
                        _conditions = filter_by_entity(db_model=DBClass, column_name=column_name, entity_filter=value)
                        if _conditions:
                            nested_conditions = _conditions[0]
                            for _condition in _conditions:
                                nested_conditions &= _condition
                            conditions.append(nested_conditions)

                    else:
                        response = Response(status_code=404, detail=self.response_format.error(f"Attribute Error : Unrecognized Attribute <{column_name}>. The Attribute Suffix for ListFilter must be Either _or or _and"))
                        logger.error(response.detail)


        # Dictionary Filter
        if data_filter.dictionary_filter is not None:
            or_suffix  = '_or'
            and_suffix = '_and'
            for key, value in data_filter.dictionary_filter.__dict__.items():
                if value is not None:
                    if or_suffix in key:
                        column_name = key.split(or_suffix)[0]
                        try:
                            _conditions = [Q(**{f"attribute.{_key}__exists": True}) for _key in value]
                            if _conditions:
                                nested_conditions = _conditions[0]
                                for _condition in _conditions:
                                    nested_conditions |= _condition
                        except AttributeError as e:
                            response = Response(status_code=404, detail=self.response_format.error(f"Attribute Error : Unrecognized Attribute <{column_name}>. The Attribute for DictionaryFilter must be <key_or> or <key_and>", str(e)))
                            logger.error(response.detail)
                        except Exception:
                            response = Response(status_code=500, detail=self.response_format.error(f"Unexpected Error : Unexpected Error during Parsing Attribute <{column_name}> in DictionaryFilter"))
                            logger.error(response.detail)     

                    elif and_suffix in key:
                        column_name = key.split(and_suffix)[0]
                        try:
                            _conditions = [Q(**{f"attribute.{_key}__exists": True}) for _key in value]
                            if _conditions:
                                nested_conditions = _conditions[0]
                                for _condition in _conditions:
                                    nested_conditions &= _condition
                        except AttributeError as e:
                            response = Response(status_code=404, detail=self.response_format.error(f"Attribute Error : Unrecognized Attribute <{column_name}>. The Attribute for DictionaryFilter must be <key_or> or <key_and>", str(e)))
                            logger.error(response.detail)
                        except Exception:
                            response = Response(status_code=500, detail=self.response_format.error(f"Unexpected Error : Unexpected Error during Parsing Attribute <{column_name}> in DictionaryFilter"))
                            logger.error(response.detail)

                    else:
                        response = Response(status_code=404, detail=self.response_format.error(f"Attribute Error : Unrecognized Attribute <{column_name}>. The Attribute Suffix for ListFilter must be Either _or or _and"))
                        logger.error(response.detail)


        # Boolean Filter
        if data_filter.boolean_filter is not None:
            boolean_suffix = '_filter'
            
            for key, value in data_filter.boolean_filter.__dict__.items():
                if value is not None:
                    column_name = key.split(boolean_suffix)[0]
                    try:
                        conditions.append(Q(**{f"{column_name}": value}))
                    except AttributeError as e:
                        response = Response(status_code=404, detail=self.response_format.error(f"Attribute Error : Unrecognized Attribute <{column_name}>. The Attribute for BooleanFilter must be <key_filter>", str(e)))
                        logger.error(response.detail)
                    except Exception:
                        response = Response(status_code=500, detail=self.response_format.error(f"Unexpected Error : Unexpected Error during Parsing Attribute <{column_name}> in BooleanFilter"))
                        logger.error(response.detail)    


        # Datetime Filter
        if data_filter.datetime_filter is not None:
            start_suffix = '_start'
            end_suffix   = '_end'
            
            for key, value in data_filter.datetime_filter.__dict__.items():
                if value is not None:
                    if start_suffix in key:
                        column_name = key.split(start_suffix)[0]
                        try:
                            conditions.append(Q(**{f"{column_name}__gte": value}))
                        except AttributeError as e:
                            response = Response(status_code=404, detail=self.response_format.error(f"Attribute Error : Unrecognized Attribute <{column_name}>. The Attribute for DatetimeFilter must be <key_start> or <key_end>", str(e)))
                            logger.error(response.detail)
                        except Exception:
                            response = Response(status_code=500, detail=self.response_format.error(f"Unexpected Error : Unexpected Error during Parsing Attribute <{column_name}> in DatetimeFilter"))
                            logger.error(response.detail)              

                    if end_suffix in key:
                        column_name = key.split(end_suffix)[0]
                        try:
                            conditions.append(Q(**{f"{column_name}__lte": value}))
                        except AttributeError as e:
                            response = Response(status_code=404, detail=self.response_format.error(f"Attribute Error : Unrecognized Attribute <{column_name}>. The Attribute for DatetimeFilter must be <key_start> or <key_end>", str(e)))
                            logger.error(response.detail)
                        except Exception:
                            response = Response(status_code=500, detail=self.response_format.error(f"Unexpected Error : Unexpected Error during Parsing Attribute <{column_name}> in DatetimeFilter"))
                            logger.error(response.detail)    

                    else:
                        response = Response(status_code=404, detail=self.response_format.error(f"Attribute Error : Unrecognized Attribute <{column_name}>. The Attribute Suffix for DatetimeFilter must be Either _start or _end"))
                        logger.error(response.detail)

        # Byte Filter
        if SETTINGS.BASE.APP_ENCRYPTION and data_filter.byte_filter is not None:
            byte_suffix = '_filter'
            
            for key, value in data_filter.byte_filter.__dict__.items():
                if value is not None:
                    column_name = key.split(byte_suffix)[0]
                    _conditions = filter_by_byte(db_model=DBClass, column_name=column_name, entity_filter=value)
                    if _conditions:
                        conditions += _conditions

                    else:
                        response = Response(status_code=404, detail=self.response_format.error(f"Attribute Error : Unrecognized Attribute <{column_name}>. The Attribute Suffix for ByteFilter must be _filter"))
                        logger.error(response.detail)

        # Define sorting order
        sorting = []
        for col, direction in data_filter.sorting.items():
            column = getattr(DBClass, col, None)  # Returns None if col is not found
            if column is not None:
                if direction.lower() == "desc":
                    sorting.append(f"-{col}")
                else:
                    sorting.append(f"+{col}")

        filter_no = data_filter.filter_no

        return conditions, sorting, filter_no

    def get_keyword_mappings_with_string(self,request: SystemKeywordMappingFuncRequest)-> tuple[SystemKeywordMappingFuncReponse, Response]:

        try:
            numeric_filter = DataNumericFilter(mapping_status_min=1)
            keyword_filter = DataFilter(numeric_filter=numeric_filter)

            system_request = SystemDataRequest(data_filter=keyword_filter)
            response_data, response = self.query_data_by_system(request=system_request)

            if not response_data or not response_data.filtered_data:
                valid_response = SystemKeywordMappingFuncReponse(filtered_data=[])
                return valid_response, response

            filtered_mappings = [
                mapping 
                for mapping in response_data.filtered_data
                if any(request.search_str.upper() == keyword.upper() for keyword in mapping.keywords)
            ]

            valid_response = SystemKeywordMappingFuncReponse(filtered_data=filtered_mappings)
            response = Response(status_code=200, detail=self.response_format.ok(f"Azure Keyword Success : <{SETTINGS.BASE.APP_NAME}> Completed Azure Keyword"))
            return valid_response, response

        except (BaseException, Exception) as e:
            response = Response(status_code=500, detail=self.response_format.error(f"Common Error : <{SETTINGS.BASE.APP_NAME}> Encountered Common Error when Calling Azure Server", str(e)))
            logger.error(response.detail)

        except:
            response = Response(status_code=500, detail=self.response_format.error(f"Unexpected Error : <{SETTINGS.BASE.APP_NAME}> Encountered Unexpected Error when Calling Azure Server", str(e)))
            logger.error(response.detail)
        return [], response