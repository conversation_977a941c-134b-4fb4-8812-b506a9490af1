from pydantic import BaseModel, Field
import uuid
from datetime import datetime, timezone

from ....settings import SETTINGS

""""
    Vector General Operation
"""

class VectorCreate(BaseModel):
    # Trace Information
    data_id:        str=Field(default_factory=lambda: str(uuid.uuid4()))
    data_traceid:   str=Field(default_factory=lambda: str(uuid.uuid4()))
    data_version:   int=1

    # Category Information
    data_type:      str='' # TEXT, TITLE, IMAGE, TABLE, DOCUMENT, etc.
    content_type:   str='default' # default, image-to-text, OCR
    data_url:       str='' # Image URL, Table URL

    # Control Information
    data_status:    int=1

    # Specification
    raw_data:       str='' # Raw Text, Image Description, Text on Image, etc.
    processed_data: list[float]=[] # vector
    data_dimension: int=-1
    data_length:    int=0

    coord_x1:       float=-1.0
    coord_x2:       float=-1.0
    coord_y1:       float=-1.0
    coord_y2:       float=-1.0

    page_start:     int=-1
    page_end:       int=-1
    line_start:     int=-1
    line_end:       int=-1
    seq_no:         int=-1

    # Dependent
    knowledge_id:   str='' # Map to Original Knowledge
    node_id:        str=''
    node_type:      str=''

    # Tags
    data_languages: list[str]=[]
    data_keywords:  list[str]=[]
    data_tags:      list[str]=[]

    # Time Information
    created_at:     datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    updated_at:     datetime = Field(default_factory=lambda: datetime.now(timezone.utc))

class VectorCreateRequest(BaseModel):
    user_requestid: str | None = ""
    user_id:        str | None = ""
    user_name:      str | None = ""
    is_admin:       bool=False
    data:           VectorCreate

class VectorBatchCreateRequest(BaseModel):
    create_requests: list[VectorCreateRequest]

# Vector CRUD
class VectorUpdate(BaseModel):
    # Trace Information
    data_id:        str | None = None
    data_traceid:   str | None = None
    data_version:   int | None = None

    # Category Information
    data_type:      str | None = None
    content_type:   str | None = None
    data_url:       str | None = None

    # Control Information
    data_status:    int | None = None

    # Specification
    raw_data:       str | None = None
    processed_data: list[float] | None = None
    data_dimension: int | None = None
    data_length:    int | None = None

    coord_x1:       float | None = None
    coord_x2:       float | None = None
    coord_y1:       float | None = None
    coord_y2:       float | None = None
    seq_no:         int | None = None

    page_start:     int | None = None
    page_end:       int | None = None
    line_start:     int | None = None
    line_end:       int | None = None

    # Dependent
    knowledge_id:   str | None = None
    node_id:        str | None = None
    node_type:      str | None = None

    # Tags
    data_languages: list[str] | None = None
    data_keywords:  list[str] | None = None
    data_tags:      list[str] | None = None

    # Time Information
    created_at:     datetime | None = None
    updated_at:     datetime | None = None

class VectorUpdateRequest(BaseModel):
    user_requestid:   str | None = None
    user_id:          str=''
    user_name:        str=''
    is_admin:         bool=False
    data_id:          str | None = None
    update_data:      VectorUpdate=VectorUpdate()
    prepmedia_config: dict={}
    overwrite:        bool = False
    
class VectorRequest(BaseModel):
    user_requestid: str | None = None
    user_id:        str | None = None
    user_name:      str | None = None
    data_id:        str | None = None

class VectorBatchRequest(BaseModel):
    vector_requests: list[VectorRequest]



# System-level Access
class SecretVector(BaseModel):
    # Trace Information
    data_id:        str | None = None
    data_traceid:   str | None = None
    data_version:   int | None = None

    # Category Information
    data_type:      str | None = None
    content_type:   str | None = None
    data_url:       str | None = None

    # Control Information
    data_status:    int | None = None

    # Specification
    raw_data:       str | None = None
    processed_data: list[float] | None = None
    data_dimension: int | None = None
    data_length:    int | None = None

    coord_x1:       float | None = None
    coord_x2:       float | None = None
    coord_y1:       float | None = None
    coord_y2:       float | None = None

    page_start:     int | None = None
    page_end:       int | None = None
    line_start:     int | None = None
    line_end:       int | None = None
    seq_no:         int | None = None

    # Dependent
    knowledge_id:   str | None = None
    node_id:        str | None = None
    node_type:      str | None = None

    # Tags
    data_languages: list[str] | None = None
    data_keywords:  list[str] | None = None
    data_tags:      list[str] | None = None

    # Time Information
    created_at:     datetime | None = None
    updated_at:     datetime | None = None

"""
    Vector Filter
"""   
class VectorStringFilter(BaseModel):
    data_id_filter:        list[str] | None = None
    data_traceid_filter:   list[str] | None = None

    data_type_filter:      list[str] | None = None
    content_type_filter:   list[str] | None = None
    data_url_filter:       list[str] | None = None

    raw_data_filter:       list[str] | None = None

    knowledge_id_filter:   list[str] | None = None
    node_id_filter:        list[str] | None = None
    node_type_filter:      list[str] | None = None


class VectorNumericFilter(BaseModel):
    data_version_min:   int | None = None
    data_version_max:   int | None = None

    data_status_min:    int | None = None
    data_status_max:    int | None = None 

    data_dimension_min: int | None = None
    data_dimension_max: int | None = None
    data_length_min:    int | None = None
    data_length_max:    int | None = None

    coord_x1_min:       float | None = None
    coord_x1_max:       float | None = None
    coord_x2_min:       float | None = None
    coord_x2_max:       float | None = None
    coord_y1_min:       float | None = None
    coord_y1_max:       float | None = None
    coord_y2_min:       float | None = None
    coord_y2_max:       float | None = None

    page_start_min:     int | None = None
    page_start_max:     int | None = None
    page_end_min:       int | None = None
    page_end_max:       int | None = None
    line_start_min:     int | None = None
    line_start_max:     int | None = None
    line_end_min:       int | None = None
    line_end_max:       int | None = None
    seq_no_min:         int | None = None
    seq_no_max:         int | None = None

class VectorListFilter(BaseModel):
    data_languages_or:  list[str] | None = None
    data_languages_and: list[str] | None = None
    data_keywords_or:   list[str] | None = None
    data_keywords_and:  list[str] | None = None
    data_tags_or:       list[str] | None = None
    data_tags_and:      list[str] | None = None

class VectorDictionaryFilter(BaseModel):
    not_used_or:  list[str] | None = None
    not_used_and: list[str] | None = None

class VectorBooleanFilter(BaseModel):
    not_used_filter: bool | None = None

class VectorDatetimeFilter(BaseModel):
    created_at_start: datetime  | None = None
    created_at_end:   datetime  | None = None
    updated_at_start: datetime  | None = None
    updated_at_end:   datetime  | None = None

class VectorByteFilter(BaseModel):
    not_used_filter: list[bytes] | None = None

class VectorFilter(BaseModel):
    string_filter:     VectorStringFilter     | None = None
    numeric_filter:    VectorNumericFilter    | None = None
    list_filter:       VectorListFilter       | None = None
    dictionary_filter: VectorDictionaryFilter | None = None
    boolean_filter:    VectorBooleanFilter    | None = None
    datetime_filter:   VectorDatetimeFilter   | None = None
    byte_filter:       VectorByteFilter       | None = None
    sorting:           dict={"data_id": "asc", "updated_at": "desc"}
    filter_no:         int=-1


""" 
    Request and Resposne for System Access Vectors
"""
class SystemVectorRequest(BaseModel):
    vector_requestid: str=Field(default_factory=lambda: str(uuid.uuid4()))
    vector_filter:    VectorFilter | None = None

class SystemVectorResponse(BaseModel):
    vector_requestid: str
    filtered_vectors: list[SecretVector]=[]
    vector_no:        int=0


"""
    Data Backup / Restore Configuration
"""
class BackupConfig(BaseModel):
    format:   str | None = None
    location: str | None = None
    name:     str | None = None
    host:     str | None = None
    port:     str | None = None
    user:     str | None = None
    pswd:     str | None = None
    table:    str | None = None
    rdir:     str | None = None
    sdir:     str | None = None
    limit:    int | None = None

class VectorBackupRequest(BaseModel):
    vector_requestid: str=Field(default_factory=lambda: str(uuid.uuid4()))
    vector_filter:    VectorFilter | None = None
    backup_config:            BackupConfig | None = None

class VectorBackupListRequest(BaseModel):
    vector_requestid: str=Field(default_factory=lambda: str(uuid.uuid4()))
    backup_config:            BackupConfig | None = None

class VectorBackupListResponse(BaseModel):
    vector_requestid: str=Field(default_factory=lambda: str(uuid.uuid4()))
    table_list:               list[str]=[]

class RestoreConfig(BaseModel):
    format:   str | None = None
    location: str | None = None
    name:     str | None = None
    host:     str | None = None
    port:     str | None = None
    user:     str | None = None
    pswd:     str | None = None
    table:    str | None = None
    rdir:     str | None = None
    sdir:     str | None = None

class VectorRestoreRequest(BaseModel):
    vector_requestid: str=Field(default_factory=lambda: str(uuid.uuid4()))
    restore_config:           RestoreConfig | None = None


"""
    Data Import/Export Configuration
"""
class IOConfig(BaseModel):
    format:           str | None = None
    location:         str | None = None
    name:             str | None = None
    host:             str | None = None
    port:             str | None = None
    user:             str | None = None
    pswd:             str | None = None
    table:            str | None = None
    rdir:             str | None = None
    sdir:             str | None = None
    file_rdir:        str | None = None
    file_sdir:        str | None = None
    file_name:        str | None = None

class VectorImportRequest(BaseModel):
    vector_requestid: str=Field(default_factory=lambda: str(uuid.uuid4()))
    io_config:                IOConfig | None = None
    backup:                   bool=True

class VectorExportRequest(BaseModel):
    vector_requestid: str=Field(default_factory=lambda: str(uuid.uuid4()))
    vector_filter:    VectorFilter | None = None
    io_config:        IOConfig | None = None
    include_datetime: bool = True


"""" VectorServiceManager """
"""
    Request and Response for User Access Permitted Vectors
"""
# User-level Access
class Vector(BaseModel):
    # Trace Information
    data_id:        str | None = None
    data_traceid:   str | None = None
    data_version:   int | None = None

    # Category Information
    data_type:      str | None = None
    content_type:   str | None = None
    data_url:       str | None = None

    # Control Information
    data_status:    int | None = None

    # Specification
    raw_data:       str | None = None
    processed_data: list[float] | None = None
    data_dimension:  int | None = None
    data_length:     int | None = None

    coord_x1:       float | None = None
    coord_x2:       float | None = None
    coord_y1:       float | None = None
    coord_y2:       float | None = None

    page_start:     int | None = None
    page_end:       int | None = None
    line_start:     int | None = None
    line_end:       int | None = None
    seq_no:         int | None = None

    # Dependent
    knowledge_id:   str | None = None
    node_id:        str | None = None
    node_type:      str | None = None

    # Tags
    data_languages: list[str] | None = None
    data_keywords:  list[str] | None = None
    data_tags:      list[str] | None = None

    # Time Information
    created_at:     datetime | None = None
    updated_at:     datetime | None = None

    
class UserVectorRequest(BaseModel):
    vector_requestid: str=Field(default_factory=lambda: str(uuid.uuid4()))
    vector_filter:    VectorFilter

class UserVectorResponse(BaseModel):
    vector_requestid: str
    filtered_vectors: list[Vector]=[]