from sqlalchemy import Column, String, Integer, Float, DateTime, JSON, Boolean, UniqueConstraint
from sqlalchemy import Table, MetaData
from sqlalchemy.engine import Engine

from sqlalchemy.orm import declarative_base
from sqlalchemy.sql import func

import uuid
from datetime import datetime, timezone

from ....settings import SETTINGS


def set_io_db_table(table_name: str, engine: Engine):
    Base = declarative_base()

    class IOKnowledgeInputDB(Base):

        __tablename__ = table_name

        # Trace Information
        knowledge_id              = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()), index=True, unique=True)
        knowledge_traceid         = Column(String,  default=lambda: str(uuid.uuid4())) # trace the changes of knowledge
        knowledge_name            = Column(String,  default='')
        knowledge_version         = Column(Integer, default=1)   # 1=original    
        batch_order               = Column(String, default=lambda: datetime.now(timezone.utc).strftime("%Y-%m-%d-%H-%M-%S")) # single if not batch, otherwise timestamp
        
        # Creator Information
        creator_id                = Column(String,  default='') # user or group ID
        creator_name              = Column(String,  default='') # user or group name
        approver_id               = Column(String,  default='admin')
        approver_name             = Column(String,  default='admin')
        
        # Category Information
        knowledge_group           = Column(String,  default='default') # default, user, group, agent
        knowledge_type            = Column(String,  default="default") # general, chain-of-thought
        knowledge_location        = Column(String,  default='default')
        storage_type              = Column(String,  default='')
        storage_type_origin       = Column(String,  default='')
        storage_provider          = Column(String,  default='')
        storage_provider_origin   = Column(String,  default='')
        storage_directory         = Column(String,  default='')
        storage_directory_origin  = Column(String,  default='')
        storage_secrets           = Column(JSON,    default=dict())
        storage_secrets_origin    = Column(JSON,    default=dict())

        # Control Information
        knowledge_status          = Column(Integer, default=1)   # 0=inactvie, 1=active
        knowledge_permission      = Column(Integer, default=1)   # 0=default, related to user permission
        knowledge_management      = Column(Integer, default=10)  # relate to management permission

        # Configuration
        knowledge_vectorstorage   = Column(String,  default='')
        knowledge_vectorlocation  = Column(String,  default='')
        knowledge_vectorinfo      = Column(JSON,    default=dict())
        knowledge_graphstorage    = Column(String,  default='')
        knowledge_graphlocation   = Column(String,  default='')
        knowledge_graphinfo       = Column(JSON,    default=dict())
        knowledge_searchstorage   = Column(JSON,    default=dict())
        knowledge_searchinfo      = Column(JSON,    default=dict())
        knowledge_secrets         = Column(JSON,    default=dict())
        knowledge_record          = Column(Boolean, default=False)
        knowledge_key             = Column(String,  default='')

        # Specification
        knowledge_confidence      = Column(Float,  default=-1.0)
        knowledge_filename        = Column(String,  default='')
        knowledge_fileextension   = Column(String,  default='')
        knowledge_filesize        = Column(Float,   default=0.0)
        knowledge_description     = Column(String,  default='')

        # Statistics
        processing_time           = Column(Float,   default=0.0)
        total_input_tokens        = Column(Integer, default=0)
        total_output_tokens       = Column(Integer, default=0)
        total_tool_tokens         = Column(Integer, default=0)

        # Dependices
        knowledgeinput_id         = Column(String,  default='')
        prepknow_id               = Column(String,  default='DEFAULT')
        graph_id                  = Column(String,  default='DEFAULT')
        graph_config              = Column(JSON,    default=dict())

        # Tags
        _knowledge_searchstorages = Column("knowledge_searchstorages",  String, default='')
        _knowledge_sources        = Column("knowledge_sources",  String, default='')
        _knowledge_sourcetypes    = Column("knowledge_sourcetypes",  String, default='')
        _knowledge_contenttypes   = Column("knowledge_contenttypes",  String, default='')
        _knowledge_cats           = Column("knowledge_cats",  String, default='')
        _knowledge_languages      = Column("knowledge_languages",  String, default='')
        _knowledge_tags           = Column("knowledge_tags",  String, default='')
        _user_groups              = Column("user_groups",  String, default='')
        _agent_groups             = Column("agent_groups",  String, default='')

        # Time Information
        knowledge_issue_date      = Column(DateTime(timezone=True), server_default=func.now())
        knowledge_effective_from  = Column(DateTime(timezone=True), server_default=func.now(), default=datetime.now(timezone.utc))
        knowledge_effective_to    = Column(DateTime(timezone=True), server_default=func.now())

        created_at                = Column(DateTime(timezone=True), server_default=func.now(), default=datetime.now(timezone.utc))
        updated_at                = Column(DateTime(timezone=True), onupdate=lambda: datetime.now(timezone.utc), default=datetime.now(timezone.utc))

        @property
        def knowledge_searchstorages(self):
            """Convert value from str to list."""
            if self._knowledge_searchstorages:
                return [value.strip() for value in self._knowledge_searchstorages.split(SETTINGS.DATB.SEP)]
            return []

        @knowledge_searchstorages.setter
        def knowledge_searchstorages(self, values):
            """Convert value from list to str."""
            if isinstance(values, list):
                if all(constrained_word not in value for constrained_word in SETTINGS.CSTR.NAMING for value in values):
                    self._knowledge_searchstorages = SETTINGS.DATB.SEP.join(values)
                else:
                    ValueError(f"Error : {values} contains constrained characters/symbols.")
            
            elif isinstance(values, str):
                if all(constrained_word not in values for constrained_word in SETTINGS.CSTR.NAMING):
                    self._knowledge_searchstorages = values
                else:
                    ValueError(f"Error : {values} contains constrained characters/symbols.")
            else:
                raise ValueError(f"Error : {values} must be a string or a list.")

        @property
        def knowledge_sources(self):
            """Convert value from str to list."""
            if self._knowledge_sources:
                return [value.strip() for value in self._knowledge_sources.split(SETTINGS.DATB.SEP)]
            return []

        @knowledge_sources.setter
        def knowledge_sources(self, values):
            """Convert value from list to str."""
            if isinstance(values, list):
                if all(constrained_word not in value for constrained_word in SETTINGS.CSTR.NAMING for value in values):
                    self._knowledge_sources = SETTINGS.DATB.SEP.join(values)
                else:
                    ValueError(f"Error : {values} contains constrained characters/symbols.")
            
            elif isinstance(values, str):
                if all(constrained_word not in values for constrained_word in SETTINGS.CSTR.NAMING):
                    self._knowledge_sources = values
                else:
                    ValueError(f"Error : {values} contains constrained characters/symbols.")
            else:
                raise ValueError(f"Error : {values} must be a string or a list.")
            
        @property
        def knowledge_sourcetypes(self):
            """Convert value from str to list."""
            if self._knowledge_sourcetypes:
                return [value.strip() for value in self._knowledge_sourcetypes.split(SETTINGS.DATB.SEP)]
            return []

        @knowledge_sourcetypes.setter
        def knowledge_sourcetypes(self, values):
            """Convert value from list to str."""
            if isinstance(values, list):
                if all(constrained_word not in value for constrained_word in SETTINGS.CSTR.NAMING for value in values):
                    self._knowledge_sourcetypes = SETTINGS.DATB.SEP.join(values)
                else:
                    ValueError(f"Error : {values} contains constrained characters/symbols.")
            
            elif isinstance(values, str):
                if all(constrained_word not in values for constrained_word in SETTINGS.CSTR.NAMING):
                    self._knowledge_sourcetypes = values
                else:
                    ValueError(f"Error : {values} contains constrained characters/symbols.")
            else:
                raise ValueError(f"Error : {values} must be a string or a list.")
        
        @property
        def knowledge_contenttypes(self):
            """Convert value from str to list."""
            if self._knowledge_contenttypes:
                return [value.strip() for value in self._knowledge_contenttypes.split(SETTINGS.DATB.SEP)]
            return []

        @knowledge_contenttypes.setter
        def knowledge_contenttypes(self, values):
            """Convert value from list to str."""
            if isinstance(values, list):
                if all(constrained_word not in value for constrained_word in SETTINGS.CSTR.NAMING for value in values):
                    self._knowledge_contenttypes = SETTINGS.DATB.SEP.join(values)
                else:
                    ValueError(f"Error : {values} contains constrained characters/symbols.")
            
            elif isinstance(values, str):
                if all(constrained_word not in values for constrained_word in SETTINGS.CSTR.NAMING):
                    self._knowledge_contenttypes = values
                else:
                    ValueError(f"Error : {values} contains constrained characters/symbols.")
            else:
                raise ValueError(f"Error : {values} must be a string or a list.")
        
        @property
        def knowledge_cats(self):
            """Convert value from str to list."""
            if self._knowledge_cats:
                return [value.strip() for value in self._knowledge_cats.split(SETTINGS.DATB.SEP)]
            return []

        @knowledge_cats.setter
        def knowledge_cats(self, values):
            """Convert value from list to str."""
            if isinstance(values, list):
                if all(constrained_word not in value for constrained_word in SETTINGS.CSTR.NAMING for value in values):
                    self._knowledge_cats = SETTINGS.DATB.SEP.join(values)
                else:
                    ValueError(f"Error : {values} contains constrained characters/symbols.")
            
            elif isinstance(values, str):
                if all(constrained_word not in values for constrained_word in SETTINGS.CSTR.NAMING):
                    self._knowledge_cats = values
                else:
                    ValueError(f"Error : {values} contains constrained characters/symbols.")
            else:
                raise ValueError(f"Error : {values} must be a string or a list.")
        
        @property
        def knowledge_languages(self):
            """Convert value from str to list."""
            if self._knowledge_languages:
                return [value.strip() for value in self._knowledge_languages.split(SETTINGS.DATB.SEP)]
            return []

        @knowledge_languages.setter
        def knowledge_languages(self, values):
            """Convert value from list to str."""
            if isinstance(values, list):
                if all(constrained_word not in value for constrained_word in SETTINGS.CSTR.NAMING for value in values):
                    self._knowledge_languages = SETTINGS.DATB.SEP.join(values)
                else:
                    ValueError(f"Error : {values} contains constrained characters/symbols.")
            
            elif isinstance(values, str):
                if all(constrained_word not in values for constrained_word in SETTINGS.CSTR.NAMING):
                    self._knowledge_languages = values
                else:
                    ValueError(f"Error : {values} contains constrained characters/symbols.")
            else:
                raise ValueError(f"Error : {values} must be a string or a list.")

        @property
        def knowledge_tags(self):
            """Convert value from str to list."""
            if self._knowledge_tags:
                return [value.strip() for value in self._knowledge_tags.split(SETTINGS.DATB.SEP)]
            return []

        @knowledge_tags.setter
        def knowledge_tags(self, values):
            """Convert value from list to str."""
            if isinstance(values, list):
                if all(constrained_word not in value for constrained_word in SETTINGS.CSTR.NAMING for value in values):
                    self._knowledge_tags = SETTINGS.DATB.SEP.join(values)
                else:
                    ValueError(f"Error : {values} contains constrained characters/symbols.")
            
            elif isinstance(values, str):
                if all(constrained_word not in values for constrained_word in SETTINGS.CSTR.NAMING):
                    self._knowledge_tags = values
                else:
                    ValueError(f"Error : {values} contains constrained characters/symbols.")
            else:
                raise ValueError(f"Error : {values} must be a string or a list.")

        @property
        def user_groups(self):
            """Convert value from str to list."""
            if self._user_groups:
                return [value.strip() for value in self._user_groups.split(SETTINGS.DATB.SEP)]
            return []

        @user_groups.setter
        def user_groups(self, values):
            """Convert value from list to str."""
            if isinstance(values, list):
                if all(constrained_word not in value for constrained_word in SETTINGS.CSTR.NAMING for value in values):
                    self._user_groups = SETTINGS.DATB.SEP.join(values)
                else:
                    ValueError(f"Error : {values} contains constrained characters/symbols.")
            
            elif isinstance(values, str):
                if all(constrained_word not in values for constrained_word in SETTINGS.CSTR.NAMING):
                    self._user_groups = values
                else:
                    ValueError(f"Error : {values} contains constrained characters/symbols.")
            else:
                raise ValueError(f"Error : {values} must be a string or a list.")
        
        @property
        def agent_groups(self):
            """Convert value from str to list."""
            if self._agent_groups:
                return [value.strip() for value in self._agent_groups.split(SETTINGS.DATB.SEP)]
            return []

        @agent_groups.setter
        def agent_groups(self, values):
            """Convert value from list to str."""
            if isinstance(values, list):
                if all(constrained_word not in value for constrained_word in SETTINGS.CSTR.NAMING for value in values):
                    self._agent_groups = SETTINGS.DATB.SEP.join(values)
                else:
                    ValueError(f"Error : {values} contains constrained characters/symbols.")
            
            elif isinstance(values, str):
                if all(constrained_word not in values for constrained_word in SETTINGS.CSTR.NAMING):
                    self._agent_groups = values
                else:
                    ValueError(f"Error : {values} contains constrained characters/symbols.")
            else:
                raise ValueError(f"Error : {values} must be a string or a list.")
            
        def to_dict(self):
            return {**self.__dict__,
                "knowledge_searchstorages": self.knowledge_searchstorages,
                "knowledge_sources":        self.knowledge_sources,
                "knowledge_sourcetypes":    self.knowledge_sourcetypes,
                "knowledge_contenttypes":   self.knowledge_contenttypes,
                "knowledge_cats":           self.knowledge_cats,
                "knowledge_languages":      self.knowledge_languages,
                "knowledge_tags":           self.knowledge_tags,
                "user_groups":              self.user_groups,
                "agent_groups":             self.agent_groups
            }

        # __table_args__ = (
        #     UniqueConstraint("prompt_group", "prompt_name", "prompt_version", name="skill_name-version"),
        # )
    
    Base.metadata.create_all(engine)
    return IOKnowledgeInputDB

def get_io_db_table(table_name: str, engine: Engine) -> Table:
    metadata = MetaData()
    table = Table(table_name, metadata, autoload_with=engine)
    return table