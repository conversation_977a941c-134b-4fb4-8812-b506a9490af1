import os
import inspect

from cryptography.hazmat.primitives.asymmetric import rsa, padding, ec
from cryptography.fernet import <PERSON><PERSON><PERSON>
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
from cryptography.hazmat.primitives import serialization, hashes
from cryptography.hazmat.backends import default_backend
import base64

from ...settings import SETTINGS

from ..schemas.format import (
    ResponseFormatter,
    Response,
    ComplexEncoder
)

from ...logger.log_handler import get_logger

logger = get_logger(__name__)


class CryptoServiceManager:
    response_format = ResponseFormatter(suffix = f"{inspect.stack()[0][3]}")
    
    def __init__(self, api_call: bool):
        self.api_call = api_call
        
    """
        Request Operation
    """
    def generate_asymmetric_key(self, key_encryption: str) -> tuple[dict, Response]:
        generated_key = dict()

        if key_encryption.upper() in ["DEFAULT", 'RSA']:
            try:
                private_key = rsa.generate_private_key(public_exponent=65537, key_size=2048)
                response = Response(status_code=200, detail=self.response_format.ok(f"Private Key Generation Completed : <{SETTINGS.BASE.APP_NAME}> Generated Private Key"))
            except:
                response = Response(status_code=500, detail=self.response_format.error(f"Asymmetric Key Generation Failed : <{SETTINGS.BASE.APP_NAME}> Failed to Generate Private Key for Asymmetric Key"))
                logger.error(response.detail)
        
        elif key_encryption.upper() == "ECC":
            try:
                private_key = ec.generate_private_key(ec.SECP384R1())
                response = Response(status_code=200, detail=self.response_format.ok(f"Private Key Generation Completed : <{SETTINGS.BASE.APP_NAME}> Generated Private Key"))
            except:
                response = Response(status_code=500, detail=self.response_format.error(f"Asymmetric Key Generation Failed : <{SETTINGS.BASE.APP_NAME}> Failed to Generate Private Key for Asymmetric Key"))
                logger.error(response.detail)
        
        else:
            response = Response(status_code=500, detail=self.response_format.error(f"Asymmetric Key Generation Failed : <{SETTINGS.BASE.APP_NAME}> Failed to Generate Asymmetric Key"))
            logger.error(response.detail)
            return generated_key, response

        # Check if Private Key Generated
        if response.status_code >= SETTINGS.STAT.SUCC_CODE_END:
            return generated_key, response

        # Generate Public Key based on Private Key
        try:
            # Serialize the private key to PEM format
            pem_private_key = private_key.private_bytes(
                encoding = serialization.Encoding.PEM,
                format   = serialization.PrivateFormat.TraditionalOpenSSL,
                encryption_algorithm = serialization.NoEncryption()
            )

            # Get the public key from the private key
            public_key = private_key.public_key()

            # Serialize the public key to PEM format
            pem_public_key = public_key.public_bytes(
                encoding=serialization.Encoding.PEM,
                format=serialization.PublicFormat.SubjectPublicKeyInfo
            )

            generated_key["PRIVATE"] = pem_private_key
            generated_key["PUBLIC"]  = pem_public_key

            response = Response(status_code=200, detail=self.response_format.ok(f"Asymmetric Key Generation Success : <{SETTINGS.BASE.APP_NAME}> Generated Asymmetric Key Pair"))

        except:
            response = Response(status_code=500, detail=self.response_format.error(f"Asymmetric Key Generation Failed : <{SETTINGS.BASE.APP_NAME}> Failed to Generate Public Key for Asymmetric Key"))
            logger.error(response.detail)

        return generated_key, response


    def generate_symmetric_key(self, key_encryption: str) -> tuple[bytes | None, Response]:
        generated_key = None

        if key_encryption.upper() in ["DEFAULT", "AES2560"]:
            # Generate the encryption key
            salt = os.urandom(16)
            kdf = PBKDF2HMAC(
                algorithm=hashes.SHA256(),
                length=32,
                salt=salt,
                iterations=100000,
            )
            generated_key = base64.urlsafe_b64encode(kdf.derive(eval(SETTINGS.SCRT.DB_KEYGEN_PSWD)))
            response = Response(status_code=200, detail=self.response_format.ok(f"Symmetric Key Generation Completed : <{SETTINGS.BASE.APP_NAME}> Completed Symmetric Key Generation"))
        
        else:
            response = Response(status_code=500, detail=self.response_format.error(f"Symmetric Key Generation Failed : <{SETTINGS.BASE.APP_NAME}> Failed to Generate Symmetric Key"))
            logger.error(response.detail)
                
        return generated_key, response
        

    # Encoding
    def encode_content(self, content: str) -> tuple[bytes | None, Response]:
        encoded_content = None
        try:
            encoded_content = base64.b64encode(str(content).encode())
            response = Response(status_code=200, detail=self.response_format.ok(f"Content Encoding Completed : <{SETTINGS.BASE.APP_NAME}> Completed Content Encoding"))
        except:
            response = Response(status_code=500, detail=self.response_format.error(f"Content Encoding Failed : <{SETTINGS.BASE.APP_NAME}> Failed to Encode Content"))
            logger.error(response.detail)

        return encoded_content, response

    # Decoding
    def decode_content(self, content: bytes) -> tuple[str | None, Response]:
        decoded_content = None
        try:
            decoded_content = base64.b64decode(content).decode('utf-8')
            response = Response(status_code=200, detail=self.response_format.ok(f"Content Decoding Completed : <{SETTINGS.BASE.APP_NAME}> Completed Content Decoding"))
        except:
            response = Response(status_code=500, detail=self.response_format.error(f"Content Decoding Failed : <{SETTINGS.BASE.APP_NAME}> Failed to Decode Content"))
            logger.error(response.detail)

        return decoded_content, response


    # Encryption with Symmetirc Key
    def encrypt_with_symmetric_key(self, content: bytes, encryption_key: bytes) -> tuple[bytes | None, Response]:
        encrypted_content = None

        fernet = Fernet(encryption_key)
        try:
            encrypted_content = base64.b64encode(fernet.encrypt(content))
            response = Response(status_code=200, detail=self.response_format.ok(f"Content Encryption Completed : <{SETTINGS.BASE.APP_NAME}> Completed Content Encrpytion using Symmetric Key"))
        except:
            response = Response(status_code=500, detail=self.response_format.error(f"Content Encryption Failed : <{SETTINGS.BASE.APP_NAME}> Failed to Encrypt Content using Symmetric Key"))
            logger.error(response.detail)

        return encrypted_content, response
    

    # Decryption with Symmetirc Key
    def decrypt_with_symmetric_key(self, content: bytes, decryption_key: bytes) -> tuple[bytes | None, Response]:
        decrypted_content = None

        fernet = Fernet(decryption_key)
        try:
            decrypted_content = fernet.decrypt(base64.b64decode(content))
            response = Response(status_code=200, detail=self.response_format.ok(f"Content Decryption Completed : <{SETTINGS.BASE.APP_NAME}> Completed Content Decrpytion using Symmetric Key"))
        except:
            response = Response(status_code=500, detail=self.response_format.error(f"Content Decryption Failed : <{SETTINGS.BASE.APP_NAME}> Failed to Decrypt Content using Symmetric Key"))
            logger.error(response.detail)

        return decrypted_content, response
    

    # Encryption with Asymmetirc Key
    def encrypt_with_asymmetric_key(self, content: bytes, encryption_key: bytes) -> tuple[bytes | None, Response]:
        encrypted_content = None

        # Load Encrpytion Key
        public_key = serialization.load_pem_public_key(
                encryption_key,
                backend=default_backend()
            )

        # Encrypt the Key for Key in Transit
        try:
            encrypted_content = public_key.encrypt(
                content,
                padding.OAEP(
                    mgf=padding.MGF1(algorithm=hashes.SHA256()),
                    algorithm=hashes.SHA256(),
                    label=None
                )
            )
            encrypted_content = base64.b64encode(encrypted_content)
            response = Response(status_code=200, detail=self.response_format.ok(f"Content Encryption Completed : <{SETTINGS.BASE.APP_NAME}> Completed Content Encrpytion using Asymmetric Key"))
        except:
            response = Response(status_code=500, detail=self.response_format.error(f"Content Encryption Failed : <{SETTINGS.BASE.APP_NAME}> Failed to Encrypt Content using Asymmetric Key"))
            logger.error(response.detail)

        return encrypted_content, response
    

    def decrypt_with_asymmetric_key(self, content: bytes, decryption_key: bytes) -> tuple[bytes | None, Response]:
        decrypted_content = None

        if not content or not decryption_key:
            response = Response(status_code=404, detail=self.response_format.error(f"Content Decryption Failed : <{SETTINGS.BASE.APP_NAME}> Failed to Decrypt Content as No Encrypted Content and/or Non Decryption Key"))
            return decrypted_content, response
        
        try:        
            private_key_bytes = decryption_key
            private_key = serialization.load_pem_private_key(
                private_key_bytes,
                backend=default_backend(),
                password=None
            )

            encoded_content = base64.b64decode(content)
            decrypted_content = private_key.decrypt(
                encoded_content,
                padding.OAEP(
                    mgf=padding.MGF1(algorithm=hashes.SHA256()),
                    algorithm=hashes.SHA256(),
                    label=None,
                )
            )

            response = Response(status_code=200, detail=self.response_format.ok(f"Content Decryption Completed : <{SETTINGS.BASE.APP_NAME}> Completed Content Decryption using Asymmetric Key"))

        except (BaseException, Exception) as e:
            response = Response(status_code=500, detail=self.response_format.error(f"Content Decryption Failed : <{SETTINGS.BASE.APP_NAME}> Failed to Decrypt Content using Asymmetric Key due to Common Error", str(e)))
            logger.error(response.detail)
        
        except:
            response = Response(status_code=500, detail=self.response_format.error(f"Content Decryption Failed : <{SETTINGS.BASE.APP_NAME}> Failed to Decrypt Content using Asymmetric Key due to Unexpected Error"))
            logger.error(response.detail)

        return decrypted_content, response