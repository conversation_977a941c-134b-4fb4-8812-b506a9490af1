from pydantic import BaseModel, Field
import uuid
from datetime import datetime, timezone

from ..settings import SETTINGS

""" Keyword Extraction Request """
class KeywordEngine(BaseModel):
    keyword_id:         str=''
    keyword_host:       str=''
    keyword_port:       str=''
    keyword_api:        str=''
    keyword_location:   str=''
    keyword_engine:     str=''
    keyword_base:       str=''
    keyword_model:      str=''
    keyword_parameters: dict=dict()
    keyword_secrets:    dict=dict()
    keyword_key:        str=''
    keyword_timeout:    int=300

class KeywordExtractionRequest(BaseModel):
    keyword_requestid: str=Field(default_factory=lambda: str(uuid.uuid4()))
    data_input:        list[str]=Field(description="List of text data to extract keywords.")
    request_at:        datetime=Field(default_factory=lambda: datetime.now(timezone.utc))

class KeywordExtractionResponse(BaseModel):
    keyword_requestid:     str
    data_output:           list[list[str]]=Field(default=[], description="List of keywords extracted from the input data.")
    keyword_model:         str=''
    keyword_input_tokens:  int=-1
    keyword_output_tokens: int=-1
    keyword_time:          float=0.0
    response_at:           datetime | None = None


# Filter schemas
class KeywordMappingStringFilter(BaseModel):
    mapping_id_filter: list[str] | None = None

class KeywordMappingNumericFilter(BaseModel):
    mapping_status_min: int | None = None
    mapping_status_max: int | None = None
    mapping_version_min: int | None = None
    mapping_version_max: int | None = None

class KeywordMappingListFilter(BaseModel):
    keywords_or: list[str] | None = None  # Keywords containing any of these

class KeywordMappingDatetimeFilter(BaseModel):
    created_at_start: datetime | None = None
    created_at_end: datetime | None = None
    updated_at_start: datetime | None = None
    updated_at_end: datetime | None = None

class KeywordMappingFilter(BaseModel):
    string_filter: KeywordMappingStringFilter | None = None
    numeric_filter: KeywordMappingNumericFilter | None = None
    list_filter: KeywordMappingListFilter | None = None
    datetime_filter: KeywordMappingDatetimeFilter | None = None
    sorting: dict = {"mapping_id": "asc"}
    filter_no: int = -1  # -1 means no limit

# System-level request and response
class SystemKeywordMappingRequest(BaseModel):
    mapping_requestid: str = Field(default_factory=lambda: str(uuid.uuid4()))
    data_filter: KeywordMappingFilter | None = None
# Base model for data representation
class KeywordMappingModel(BaseModel):
    mapping_id: str
    keywords: list[str]
    mapping_status: int
    mapping_version: int
    created_at: datetime
    updated_at: datetime

class SystemKeywordMappingResponse(BaseModel):
    mapping_requestid: str
    filtered_data: list[KeywordMappingModel] = []
    data_count: int = 0


class MappingRequest(BaseModel):
    mapping_requestid: str = Field(default_factory=lambda: str(uuid.uuid4()))
    knowledge_id:      str 

class BatchMappingRequest(BaseModel):
    knowledge_id:      list[str]


class MappingResult(BaseModel):
    mapping_requestid: str 
    knowledge_id:      str 
    mapping_result:    list[list[str]] | None = []

class BatchMappingResult(BaseModel):
    mapping_knowledge_result: list[MappingResult]

class MappingResponse(BaseModel):
    mapping_requestid: str 
    knowledge_id:      str 
    mapping_result:    list[list[str]] | None = []
    success:           str

class SystemKeywordMappingFuncRequest(BaseModel):
    search_str: str


class KeywordMappingRequest(BaseModel):
    user_requestid: str | None = None
    user_id: str | None = None
    user_name: str | None = None
    mapping_id: str


class KeywordMappingCreate(BaseModel):
    mapping_id: str = Field(default_factory=lambda: str(uuid.uuid4()))
    keywords: list[str] = Field(..., min_items=1)  # Required, at least one keyword
    mapping_status: int = 1
    mapping_version: int = 1
    created_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    updated_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))

class KeywordMappingCreateRequest(BaseModel):
    user_requestid: str | None = None
    user_id: str = ''
    user_name: str = ''
    is_admin: bool = False
    data: KeywordMappingCreate

class KeywordMappingUpdate(BaseModel):
    keywords: list[str] | None = None  
    mapping_version: int | None = None  

class KeywordMappingUpdateRequest(BaseModel):
    user_requestid: str | None = None
    user_id: str = ''   
    user_name: str = '' 
    is_admin: bool = False
    mapping_id: str
    update_data: KeywordMappingUpdate
    overwrite: bool = False




class KeywordExtractRequest(BaseModel):
    extract_id:        str = Field(default_factory=lambda: str(uuid.uuid4()))
    knowledge_id:      str

class KeywordExtractReponse(BaseModel):
    extract_id:        str = Field(default_factory=lambda: str(uuid.uuid4()))
    knowledge_id:      str
    data_keywords:     list[str]