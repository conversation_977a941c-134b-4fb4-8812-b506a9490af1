from ....settings import SETTINGS

if SETTINGS.VTDB.FORM.upper() == "AISEARCH":
    from azure.core.credentials import AzureKeyCredential
    from azure.search.documents import SearchClient
    from azure.search.documents.indexes import SearchIndexClient
    from azure.search.documents.indexes.models import SearchIndex, SimpleField, SearchableField, SearchFieldDataType
    import azure.search.documents.indexes.models._edm as edm
    from azure.search.documents.indexes.models import SearchResourceEncryptionKey, SearchField

    vector_search_profile = SETTINGS.VTDB.CONFIG.get("vector_search_profile_name", "default_vector_search_profile")

    vb_schema_index_params = {
        "algorithms": [
            {
                "name": "hnsw-1",
                "kind": "hnsw",
                "hnswParameters": {
                    "m": 4,
                    "efConstruction": 400,
                    "efSearch": 500,
                    "metric": "cosine"
                }
            }
        ],
        "profiles": [
            {
                "name": vector_search_profile,
                "algorithm": "hnsw-1"
            }
        ]
    }

    vb_schema = [
        SearchField(
            name="data_id",        
            type=SearchFieldDataType.String, 
            sortable=True, 
            filterable=True,
            key=True
        ),
        SearchField(
            name="knowledge_id",          
            type=SearchFieldDataType.String, 
            sortable=True, 
            filterable=True
        ),
        SearchField(
            name="raw_data",          
            type=SearchFieldDataType.String, 
            sortable=True, 
            filterable=True
        ),
        SearchField( # no need
            name="processed_data", 
            type=SearchFieldDataType.Collection(SearchFieldDataType.Single), 
            vector_search_dimensions=SETTINGS.VTDB.DIMENSION, 
            searchable=True,
            stored=True,
            vector_search_profile_name=vector_search_profile
        ), 
        SearchField(
            name="data_traceid",   # old data id 
            type=SearchFieldDataType.String, 
            sortable=True, 
            filterable=True
        ),
        SearchField(
            name="data_version",     
            type=SearchFieldDataType.Int32, 
            sortable=True, 
            filterable=True
        ),
        SearchField(
            name="data_type",   #text image or table??
            type=SearchFieldDataType.String, 
            sortable=True, 
            filterable=True
        ),
        SearchField(
            name="content_type",   
            type=SearchFieldDataType.String, 
            sortable=True, 
            filterable=True
        ),
        SearchField(
            name="data_url",       
            type=SearchFieldDataType.String, 
            sortable=True, 
            filterable=True
        ),
        SearchField(
            name="data_status",    
            type=SearchFieldDataType.Int32,  
            sortable=True, 
            filterable=True
        ),
        SearchField(
            name="data_dimension",    
            type=SearchFieldDataType.Int32,  
            sortable=True, 
            filterable=True
        ),
        SearchField(
            name="data_length", 
            type=SearchFieldDataType.Int32,  
            sortable=True, 
            filterable=True
        ),
        SearchField(
            name="coord_x1",       
            type=SearchFieldDataType.Double, 
            sortable=True, 
            filterable=True
        ), 
        SearchField(
            name="coord_x2",       
            type=SearchFieldDataType.Double, 
            sortable=True, 
            filterable=True
        ), 
        SearchField(
            name="coord_y1",       
            type=SearchFieldDataType.Double, 
            sortable=True, 
            filterable=True
        ), 
        SearchField(
            name="coord_y2",       
            type=SearchFieldDataType.Double, 
            sortable=True, 
            filterable=True
        ), 
        SearchField(
            name="page_start",     
            type=SearchFieldDataType.Int32,  
            sortable=True, 
            filterable=True
        ), 
        SearchField(
            name="page_end",       
            type=SearchFieldDataType.Int32,  
            sortable=True, 
            filterable=True
        ), 
        SearchField(
            name="line_start",     
            type=SearchFieldDataType.Int32,  
            sortable=True, 
            filterable=True
        ), 
        SearchField(
            name="line_end",       
            type=SearchFieldDataType.Int32,  
            sortable=True, 
            filterable=True
        ), 
        SearchField(
            name="seq_no",       
            type=SearchFieldDataType.Int32,  
            sortable=True, 
            filterable=True
        ),
        SearchField(
            name="node_id",        
            type=SearchFieldDataType.String, 
            sortable=True, 
            filterable=True
        ), 
        SearchField(
            name="node_type",      
            type=SearchFieldDataType.String, 
            sortable=True, 
            filterable=True
        ), 
        SearchField(
            name="data_languages",       
            type=SearchFieldDataType.Collection(SearchFieldDataType.String), 
            filterable=True
        ),
        SearchField(
            name="data_keywords",       
            type=SearchFieldDataType.Collection(SearchFieldDataType.String), 
            filterable=True
        ),
        SearchField(
            name="data_tags",       
            type=SearchFieldDataType.Collection(SearchFieldDataType.String), 
            filterable=True
        ),
        SearchField(
            name="created_at",     
            type=SearchFieldDataType.DateTimeOffset, 
            sortable=True, 
            filterable=True
        ),
        SearchField(
            name="updated_at",     
            type=SearchFieldDataType.DateTimeOffset, 
            sortable=True, 
            filterable=True
        )
    ]

