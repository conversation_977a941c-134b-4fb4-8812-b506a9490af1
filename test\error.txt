https://stahklifedevaz2z5oxk5002.blob.core.windows.net/ai-pil-shared/test-folder/49464e3a-28fa-498b-a359-3c7099049c8f_image_page-1_1_2025-03-11-14-08-45.jpeg?st=2025-03-11T09%3A59%3A20Z&se=2025-03-14T09%3A59%3A20Z&sp=r&spr=https&sv=2024-05-04&sr=b&sig=mVcRRNfe3qT7OmoMg2rpR9rEG/h0oT4sW2UKw7iyY3M%3D"
:

This XML file does not appear to have any style information associated with it. The document tree is shown below.
<Error>
<Code>AuthenticationFailed</Code>
<Message>Server failed to authenticate the request. Make sure the value of Authorization header is formed correctly including the signature. RequestId:f2ee44ea-001e-0069-3967-92deb7000000 Time:2025-03-11T09:26:24.1216046Z</Message>
<AuthenticationErrorDetail>Signature did not match. String to sign used was r 2025-03-11T09:26:08Z 2025-03-14T09:26:08Z /blob/stahklifedevaz2z5oxk5002/ai-pil-shared/test-folder/CIM3_BCIM3_PB_AGY_EN_Oct24/new_name.pdf https 2024-05-04 b </AuthenticationErrorDetail>
</Error>

request
{
  "id": "sadlaasdasd",
  "blob_dict_pth": "test-folder/49464e3a-28fa-498b-a359-3c7099049c8f_image_page-1_1_2025-03-11-14-08-45.jpeg",
  "blob_base_name": "ai-pil-backend-api"
}
Response body
Download
{
  "sas_url": "https://stahklifedevaz2z5oxk5002.blob.core.windows.net/ai-pil-shared/test-folder/49464e3a-28fa-498b-a359-3c7099049c8f_image_page-1_1_2025-03-11-14-08-45.jpeg?st=2025-03-11T09%3A59%3A20Z&se=2025-03-14T09%3A59%3A20Z&sp=r&spr=https&sv=2024-05-04&sr=b&sig=mVcRRNfe3qT7OmoMg2rpR9rEG/h0oT4sW2UKw7iyY3M%3D"
}