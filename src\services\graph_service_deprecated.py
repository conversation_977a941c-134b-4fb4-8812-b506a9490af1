from sqlalchemy.orm import Session
from datetime import datetime
import inspect
import time

from ..settings import SETTINGS

from ..schemas.format import (
    ResponseFormatter,
    Response,
    ComplexEncoder
)

from ..schemas.retrieval import (
    GraphSearchRequest,
    GraphSearchResponse,
    RelationshipObj
)

from ..logger.log_handler import get_logger

from ..database.graph_connection import get_gb_func

logger = get_logger(__name__)


class GraphServiceManager:
    response_format = ResponseFormatter(suffix = f"{inspect.stack()[0][3]}")

    def __init__(
            self, 
            gb_api:          Session, 
            gb_func:         Session, 
            api_call:        bool,
            Graph_storage:  str='COSMO', 
            Graph_location: str='azure', 
            Graph_config:   dict={},
        ):
        self.Graph_storage  = Graph_storage
        self.Graph_location = Graph_location
        self.Graph_config   = Graph_config
        self.gb_api          = gb_api
        self.gb_func         = gb_func
        self.api_call        = api_call
            
    
    def search_by_dataobj(self, request: GraphSearchRequest) -> tuple[GraphSearchResponse, Response]:
        logger.info(f"Processing : <{SETTINGS.BASE.APP_NAME}> Starts Keyword Search")
        start_at = time.time()
        response_Graph = GraphSearchResponse(**request.__dict__)

        if not request.knowdata_objs:
            response = Response(status_code=404, detail=self.response_format.error(f"Keyword Search Completed : <{SETTINGS.BASE.APP_NAME}> No Keywords Provided"))
            return response_Graph, response

        try:
            if request.graph_search_method == "herichacy":
                graph_search_layers = request.graph_search_layers
                graph_search_relationship = request.relationship
                container_name = request.knowdata_objs[0].knowledge_id
                if self.api_call:
                    pass
                    # results = self.gb_api.search(
                    #     Graph_queries = [Graph_query],
                    #     select         = selected_fields,
                    #     top            = Graph_search_config.get("top_k", "10"),
                    #     filter         = filter_str
                    # )
                else:
                    with get_gb_func(container_name) as gremlin_client:
                        knowdata_objs = response_Graph.knowdata_objs
                        relationship_objs = []
                        for index, knowdata_obj in enumerate(knowdata_objs):
                            #update node_id
                            req_node_id = ""
                            query = "g.V().has('data_id', data_id)"
                            param = {
                                "data_id": knowdata_obj.data_id,
                            }
                            res = gremlin_client.submit(query, param)
                            for item in res:
                                if item:
                                    req_node_id = item[0]["id"]
                                else:
                                    break
                            response_Graph.knowdata_objs[index].__dict__.update(
                                node_id = req_node_id
                            )
                            #retrieve realtionship
                            Graph_query = "g.V().has('node_id', node_id)"
                            Graph_param = {
                                "node_id": req_node_id,
                                "relationship": graph_search_relationship
                            }
                            for i in range(graph_search_layers):
                                if i == 0:
                                    if graph_search_relationship:
                                        Graph_query += f".bothE(relationship)"
                                    else:
                                        Graph_query += ".bothE()"
                                else:
                                    if graph_search_relationship:
                                        Graph_query += f".bothV().bothE(relationship)"
                                    else:
                                        Graph_query += ".bothV().bothE()"
                                
                                if graph_search_layers > 1 and i == range(graph_search_layers) - 1:
                                    Graph_query += ".dedup()"

                            results = gremlin_client.submit(Graph_query, Graph_param)
                            for result in results:
                                if result:
                                    for edge in result:
                                        origin_nodeid=edge["inV"]
                                        destination_nodeid=edge["outV"]
                                        relationship=edge["label"]
                                        relationship_obj = RelationshipObj(origin_nodeid=origin_nodeid, destination_nodeid=destination_nodeid, relationship=relationship)
                                        relationship_objs.append(relationship_obj)
                            
                response_Graph.__dict__.update(
                    retrieved_data        = relationship_objs,
                    Graph_retrieval_time  = time.time() - start_at
                )

                response = Response(status_code=200, detail=self.response_format.ok(f"Graph Search Success : <{SETTINGS.BASE.APP_NAME}> Completed Graph Search"))
                logger.info(response.detail)

            else:
                response = Response(status_code=404, detail=self.response_format.error(f"Graph Search Failed : <{SETTINGS.BASE.APP_NAME}> Encountered Unknown Graph Search Configuration"))
                logger.error(response.detail)
                return response_Graph, response

        # Handle common exceptions that might occur
        except (BaseException, Exception) as e:
            response = Response(status_code=500, detail=self.response_format.error(f"Common Error : <{SETTINGS.BASE.APP_NAME}> Encounterd Common Error during Graph Search", str(e)))
            logger.error(response.detail)

        # Handle any other exceptions that might occur
        except:
            response = Response(status_code=500, detail=self.response_format.error(f"Unexpected Error : <{SETTINGS.BASE.APP_NAME}> Encounterd Unexpected Error during Graph Search", str(e)))
            logger.error(response.detail)

        return response_Graph, response