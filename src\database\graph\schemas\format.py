from pydantic import BaseModel
from uuid import UUID
from datetime import datetime, timezone
import json

    

"""
    General System Response
"""
class Response(BaseModel):
    status_code: int
    detail:      str


class ResponseFormatter(BaseModel):
    prefix:  str=''
    suffix:  str=''

    def ok(self, content: str) -> str:
        response = content
        if self.prefix:
            response += f" | {self.prefix}"
        return response

    def error(self, content: str, error: str='') -> str:
        response = content
        if self.prefix:
            response += f" | {self.prefix}"

        if self.suffix:
            response += f" | {self.suffix}"

        if error:
            response += f" | {error}"

        return response


""" 
    JSON Response Encoder 
"""
class ComplexEncoder(json.JSONEncoder):
    def default(self, obj):
        if isinstance(obj, datetime):
            return obj.isoformat()
        elif isinstance(obj, UUID):
            return obj.hex
        return json.JSONEncoder.default(self, obj)