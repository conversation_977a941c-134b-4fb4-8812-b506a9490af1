from pydantic import BaseModel, Field
import uuid
from datetime import datetime

from ..settings import SETTINGS

""" Embedding Request """
class EmbeddingEngine(BaseModel):
    embedding_id:           str=''
    embedding_host:         str=''
    embedding_port:         str=''
    embedding_api:          str=''
    embedding_location:     str=''
    embedding_engine:       str=''
    embedding_base:         str=''
    embedding_model:        str=''
    embedding_parameters:   dict=dict()
    embedding_secrets:      dict=dict()
    embedding_key:          str=''
    embedding_timeout:      int=300

class EmbeddingRequest(BaseModel):
    embedding_requestid: str=Field(default_factory=lambda: str(uuid.uuid4()))
    data_input:          list[str]=Field(description="List of text data to generate embeddings.")
    request_at:          datetime=Field(default_factory=datetime.now)

class EmbeddingResponse(BaseModel):
    embedding_requestid: str
    data_output:         list[list[float]]=Field(default=[], description="List of embedded vectors generated from the input data.")
    embedding_model:     str=''
    embedding_tokens:    int=-1
    embedding_time:      float=0.0
    response_at:         datetime | None = None