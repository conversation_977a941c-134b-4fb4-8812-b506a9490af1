from fastapi import <PERSON><PERSON><PERSON>
from fastapi.middleware.cors import CORSMiddleware
from contextlib import asynccontextmanager
from fastapi.openapi.utils import get_openapi
from .settings import SETTINGS
from .routers import router
from .logger.log_handler import get_logger

logger = get_logger(__name__)

@asynccontextmanager
async def lifespan(app: FastAPI):
    # Startup Server
    logger.info(f'Startup {SETTINGS.BASE.APP_NAME} API Server')
    yield

    # Shutdown Server
    logger.info(f'Shutdown {SETTINGS.BASE.APP_NAME} API Server')

application = FastAPI(lifespan=lifespan)

# set fastapi server config
application.include_router(router)
application.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

def custom_openapi():
    if application.openapi_schema:
        return application.openapi_schema
    openapi_schema = get_openapi(
        title="AI-PIL - Search API",
        version="1.0.0",
        openapi_version="3.0.0",
        description="Search API for AI-PIL",
        routes=application.routes,
    )
    application.openapi_schema = openapi_schema
    return application.openapi_schema

application.openapi = custom_openapi