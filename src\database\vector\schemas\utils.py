from pydantic import BaseModel, Field
import uuid
from datetime import datetime, timezone

"""
    Request and Response of PrepKnowPipeline
"""
class KnowDataObject(BaseModel):
    # Trace Information
    data_id:        str=Field(default_factory=lambda: str(uuid.uuid4()))
    data_traceid:   str=Field(default_factory=lambda: str(uuid.uuid4()))
    data_version:   int=1

    # Category Information
    data_type:      str='' # TEXT, TITLE, IMAGE, TABLE, DOCUMENT, etc.
    content_type:   str='default' # default, image-to-text, OCR
    data_url:       str='' # Image URL, Table URL

    # Control Information
    data_status:    int=1

    # Specification
    raw_data:       str='' # Raw Text, Image Description, Text on Image, etc.
    processed_data: list[float]=[] # vector
    data_length:    int=0

    coord_x1:       float=-1.0
    coord_x2:       float=-1.0
    coord_y1:       float=-1.0
    coord_y2:       float=-1.0

    page_start:     int=-1
    page_end:       int=-1
    line_start:     int=-1
    line_end:       int=-1
    seq_no:         int=-1

    # Dependent
    knowledge_id:   str='' # Map to Original Knowledge
    node_id:        str=''
    node_type:      str=''

    # Tags
    data_languages: list[str]=[]
    data_keywords:  list[str]=[]
    data_tags:      list[str]=[]

    # Time Information
    created_at:     datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    updated_at:     datetime = Field(default_factory=lambda: datetime.now(timezone.utc))

class KnowRelationshipObject(BaseModel):
    # Trace Information
    data_id:       str=Field(default_factory=lambda: str(uuid.uuid4()))
    data_traceid:   str=Field(default_factory=lambda: str(uuid.uuid4()))
    data_version:   int=1

    # Category Information
    data_type:      str='' # TEXT, IMAGE, TABLE, DOCUMENT, etc.

    # Control Information
    data_status:    int=1

    # Dependent
    knowledge_id:   str='' # Map to Original Knowledge
    node_id:        str=''
    node_type:      str=''
    node_name:      str=''

    # Relation
    relationship:    list[dict]=[] # [{target: str, relationship: str}]

    # Time Information
    created_at:     datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    updated_at:     datetime = Field(default_factory=lambda: datetime.now(timezone.utc))


# System-level Access
class SecretPrepKnow(BaseModel):
    # Trace Information
    prepknow_id:           str | None = None
    prepknow_traceid:      str | None = None
    prepknow_version:      int | None = None
    prepknow_name:         str | None = None

    # Creator Information
    creator_id:            str | None = None
    creator_name:          str | None = None

    # Category Information
    prepknow_group:        str | None = None
    prepknow_type:         str | None = None
    prepknow_location:     str | None = None

    # Control Information
    prepknow_status:       int | None = None
    prepknow_permission:   int | None = None
    prepknow_management:   int | None = None

    # Configuration
    prepknow_layout_func:  dict | None = None
    prepknow_parameters:   dict | None = None
    prepknow_secrets:      dict | None = None
    prepknow_inputs:       dict | None = None
    prepknow_outputs:      dict | None = None
    prepknow_retry:        dict | None = None
    prepknow_termination:  dict | None = None
    prepknow_key:          str  | None = None
    prepknow_timeout:      int  | None = None    
    input_format:          str  | None = None
    output_format:         str  | None = None

    # Dependent
    prepmedia_selection:   str  | None = None
    prepmedia_ids:         dict | None = None

    # Specification
    prepknow_complexity:   int  | None = None
    prepknow_use_gpu:      bool | None = None
    prepknow_instance_num: int  | None = None
    prepknow_description:  str  | None = None

    # Tags
    input_types:           dict | None = None
    output_types:          dict | None = None
    prepknow_languages:    list[str] | None = None
    prepknow_tags:         list[str] | None = None
    user_groups:           list[str] | None = None
    agent_groups:          list[str] | None = None

    # Time Information
    created_at:            datetime | None = None
    updated_at:            datetime | None = None

class PrepKnowInput(BaseModel):
    # Trace Information
    knowledge_id:             str=Field(default_factory=lambda: str(uuid.uuid4()))
    knowledge_name:           str=''

    # Creator Information
    creator_id:               str=''
    creator_name:             str=''

    # Category Information
    storage_type:             str='' # Storage Type of Knowledge
    storage_provider:         str='' # Storage Provider of Knowledge
    storage_directory:        str='' # File Path / Url
    storage_secrets:          dict=dict() # Access Secrets

    # Configuration
    knowledge_vectorstorage:  str=''
    knowledge_vectorlocation: str=''
    knowledge_vectorinfo:     dict=dict()
    knowledge_searchstorage:  dict=dict()
    knowledge_searchinfo:     dict=dict()
    knowledge_secrets:        dict=dict()

    # Specification
    knowledge_description:    str=''

class SecretPrepMedia(BaseModel):
    # Trace Information
    prepmedia_id:           str | None = None
    prepmedia_traceid:      str | None = None
    prepmedia_version:      int | None = None
    prepmedia_name:         str | None = None

    # Creator Information
    creator_id:             str | None = None
    creator_name:           str | None = None

    # Category Information
    prepmedia_group:        str | None = None
    prepmedia_type:         str | None = None
    prepmedia_location:     str | None = None

    # Control Information
    prepmedia_status:       int | None = None
    prepmedia_permission:   int | None = None
    prepmedia_management:   int | None = None

    # Configuration
    prepmedia_parameters:   dict | None = None
    prepmedia_secrets:      dict | None = None
    prepmedia_inputs:       dict | None = None
    prepmedia_outputs:      dict | None = None
    prepmedia_retry:        dict | None = None
    prepmedia_termination:  dict | None = None
    prepmedia_key:          str  | None = None
    prepmedia_timeout:      int  | None = None
    input_format:           str  | None = None
    output_format:          str  | None = None

    # Dependent
    preptool_selection:     str  | None = None
    preptool_ids:           dict | None = None

    # Specification
    prepmedia_complexity:   int  | None = None
    prepmedia_use_gpu:      bool | None = None
    prepmedia_instance_num: int  | None = None
    prepmedia_info:         dict | None = None
    prepmedia_description:  str  | None = None

    # Tags
    input_types:            dict | None = None
    output_types:           dict | None = None
    prepmedia_languages:    list[str] | None = None
    prepmedia_tags:         list[str] | None = None
    user_groups:            list[str] | None = None
    agent_groups:           list[str] | None = None

    # Time Information
    created_at:             datetime | None = None
    updated_at:             datetime | None = None

class SecretPrepTool(BaseModel):
    # Trace Information
    preptool_id:           str | None = None
    preptool_traceid:      str | None = None
    preptool_version:      int | None = None
    preptool_name:         str | None = None

    # Creator Information
    creator_id:            str | None = None
    creator_name:          str | None = None

    # Category Information
    preptool_group:        str | None = None
    preptool_type:         str | None = None
    preptool_location:     str | None = None

    # Control Information
    preptool_permission:   int | None = None
    preptool_management:   int | None = None
    preptool_status:       int | None = None

    # Configuration
    preptool_host:         str  | None = None
    preptool_port:         str  | None = None
    preptool_api:          str  | None = None
    preptool_engine:       str  | None = None
    preptool_base:         str  | None = None
    preptool_model:        str  | None = None
    preptool_parameters:   dict | None = None
    preptool_secrets:      dict | None = None
    preptool_inputs:       dict | None = None
    preptool_outputs:      dict | None = None
    preptool_environment:  str  | None = None
    preptool_retry:        dict | None = None
    preptool_termination:  dict | None = None
    preptool_key:          str  | None = None
    preptool_timeout:      int  | None = None
    input_format:          str  | None = None
    output_format:         str  | None = None

    # Specification
    preptool_complexity:   int  | None = None
    preptool_use_gpu:      bool | None = None
    preptool_instance_num: int  | None = None
    preptool_info:         dict | None = None
    preptool_description:  str  | None = None

    # Tags
    input_types:           dict | None = None
    output_types:          dict | None = None
    preptool_languages:    list[str] | None = None
    preptool_tags:         list[str] | None = None
    user_groups:           list[str] | None = None
    agent_groups:          list[str] | None = None

    # Time Information
    created_at:            datetime | None = None
    updated_at:            datetime | None = None

class PrepKnowCustomConfiguration(BaseModel):
    # Override Stored Configuration
    prepknow:             SecretPrepKnow  | None = None
    prepmedia:            SecretPrepMedia | None = None
    preptool:             SecretPrepTool  | None = None
    encrypt_knowledge:    str  | None = None

class PrepKnowOutput(BaseModel):
    know_data: list[KnowDataObject]=[]
    
class PrepToolMetric(BaseModel):
    preptool_id:               str='custom'
    preptool_requestid:        str=Field(default_factory=lambda: str(uuid.uuid4()))
    preptool_pipeline_id:      str=Field(default_factory=lambda: str(uuid.uuid4()))
    preptool_pipeline_traceid: str=Field(default_factory=lambda: str(uuid.uuid4()))

    preptool_code:             str=''
    preptool_reason:           str='DEFAULT'

    # PrepTool Metrics
    preptool_time:             float=0.0
    preptool_input_tokens:     int=0
    preptool_output_tokens:    int=0
    preptool_tool_tokens:      int=0
    preptool_config:           dict=dict()

    preptool_request_at:       datetime=Field(default_factory=lambda: datetime.now(timezone.utc))
    preptool_response_at:      datetime | None = None


class PrepMediaMetric(BaseModel):
    prepmedia_id:               str='custom'
    prepmedia_requestid:        str=Field(default_factory=lambda: str(uuid.uuid4()))
    prepmedia_pipeline_id:      str=Field(default_factory=lambda: str(uuid.uuid4()))
    prepmedia_pipeline_traceid: str=Field(default_factory=lambda: str(uuid.uuid4()))
    
    prepmedia_code:             str=''
    prepmedia_reason:           str='FAIL'

    # PrepMedia Metrics
    prepmedia_request_at:       datetime=Field(default_factory=lambda: datetime.now(timezone.utc))
    prepmedia_response_at:      datetime | None = None
    prepmedia_turn_no:          int=1
    prepmedia_retry_no:         int=0
    prepmedia_time:             float=0.0

    total_input_tokens:         int=0
    total_output_tokens:        int=0
    total_tool_tokens:          int=0

    # PrepMdia Metrics
    preptool_metrics:           list[PrepToolMetric]=[]

class PrepKnowMetric(BaseModel):
    prepknow_requestid:        str=Field(default_factory=lambda: str(uuid.uuid4()))
    prepknow_traceid:          str=Field(default_factory=lambda: str(uuid.uuid4()))
    prepknow_id:               str=Field(default_factory=lambda: str(uuid.uuid4()))
    prepknow_pipeline_id:      str=Field(default_factory=lambda: str(uuid.uuid4()))
    prepknow_pipeline_traceid: str=Field(default_factory=lambda: str(uuid.uuid4()))

    prepknow_code:             str=''
    prepknow_reason:           str='FAIL'

    # PrepKnow Metrics
    prepknow_request_at:       datetime=Field(default_factory=lambda: datetime.now(timezone.utc))
    prepknow_response_at:      datetime | None = None
    prepknow_turn_no:          int=1
    prepknow_retry_no:         int=0
    prepknow_time:             float=0.0
    
    prepknow_input_tokens:     int=0
    prepknow_output_tokens:    int=0
    prepknow_tool_tokens:      int=0

    # PrepMedia Metrics
    prepmedia_metrics:         list[PrepMediaMetric]=[]

class PrepKnowPipelineRecord(BaseModel):
    prepknow_pipeline_termination: bool=True
    prepknow_pipeline_code:        str=''
    prepknow_pipeline_completion:  bool=False
    prepknow_pipeline_reason:      str='DEFAULT'

    prepknow_output:               PrepKnowOutput=PrepKnowOutput()
    prepknow_metric:               PrepKnowMetric=PrepKnowMetric()
    
    request_at:                    datetime | None = None
    response_at:                   datetime | None = None

class PrepKnowPipelineRequest(BaseModel):
    prepknow_requestid:   str=Field(default_factory=lambda: str(uuid.uuid4()))
    user_requestid:       str | None = None
    user_id:              str | None = None

    prepknow_pipeline_id: str=Field(default_factory=lambda: str(uuid.uuid4()))
    prepknow_id:          str=''
    prepknow_input:       PrepKnowInput
    custom_config:        PrepKnowCustomConfiguration | None = None
    resume_pipeline:      bool=False

    prepknow:             SecretPrepKnow | None = None # If prepknow is None, will init prepknow from prepknow_id
    
    encrypt_knowledge:    str='default'
    get_full_chain:       bool=False

    request_at:           datetime = Field(default_factory=lambda: datetime.now(timezone.utc))

class PrepKnowPipelineResponse(BaseModel):
    prepknow_requestid:           str
    user_requestid:               str | None = None
    user_id:                      str | None = None

    prepknow_pipeline_id:         str=Field(default_factory=lambda: str(uuid.uuid4()))
    prepknow_id:                  str=Field(default_factory=lambda: str(uuid.uuid4()))

    prepknow_pipeline_code:       str=''
    prepknow_pipeline_reason:     str='DEFAULT'
    
    prepknow_pipeline_output:     PrepKnowPipelineRecord=PrepKnowPipelineRecord()

    prepknow_chain:               list[PrepKnowPipelineRecord]=[]  

    # Overall Pipeline Statistics
    prepmedias_total_turn_no:     int=0
    prepmedias_total_retry_no:    int=0
    prepknow_total_input_tokens:  int=0
    prepknow_total_output_tokens: int=0
    prepknow_total_tool_tokens:   int=0
    prepknow_total_turn_no:       int=1  
    prepknow_total_retry_no:      int=0
    prepknow_total_time:          float=0.0

    request_at:                   datetime
    response_at:                  datetime | None = None

"""
    Request and Response of PrepMediaPipeline
"""
class PrepKnowMedia(BaseModel):
    prepmedia: SecretPrepMedia
    preptools: list[SecretPrepTool]=[]

class PrepMediaCustomConfiguration(BaseModel):
    pass

class PrepMediaPipelineRequest(BaseModel):
    prepmedia_requestid:  str=Field(default_factory=lambda: str(uuid.uuid4()))
    user_requestid:       str | None = None
    user_id:              str | None = None

    prepmedia_id:         str=''
    prepmedia_input:      list[KnowDataObject]=[]
    custom_config:        PrepMediaCustomConfiguration | None = None
    resume_pipeline:      bool=False

    prepknow_media:       PrepKnowMedia | None = None
    encrypt_knowledge:    str='default'
    
    request_at:           datetime=Field(default_factory=lambda: datetime.now(timezone.utc))

class PrepMediaPipelineResponse(BaseModel):
    prepmedia_requestid:           str

    prepknow_termination:          bool=False
    prepmedia_pipeline_code:       str=''
    prepmedia_pipeline_reason:     str='DEFAULT'
    prepmedia_output:              list[KnowDataObject]=[]
    prepmedia_relationship:        list[KnowRelationshipObject]=[]

    prepmedia_turn_no:             int=1
    prepmedia_retry_no:            int=0
    prepmedia_total_time:          float=0.0
    prepmedia_total_input_tokens:  int=0
    prepmedia_total_output_tokens: int=0
    prepmedia_total_tool_tokens:   int=0

    prepmedia_metrics:             list[PrepMediaMetric]=[]
    
    request_at:                    datetime=Field(default_factory=lambda: datetime.now(timezone.utc))
    response_at:                   datetime | None = None