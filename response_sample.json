{"prepknow_requestid": "", "layout_success_objects": {"text": [{"data_id": "138799d3-cb3e-4cc7-9d4a-38a91bc40560", "data_traceid": "f3b609e8-ec3b-49ba-8bba-1fa257c60af8", "data_version": 1, "data_type": "TEXT", "content_type": "default", "data_url": "", "data_status": 1, "raw_data": "Enrol in Evergreen Wealth Multi-Currency Plan and enjoy up to a  31\\%  premium refund", "processed_data": [], "data_dimension": -1, "coord_x1": 34, "coord_x2": 468, "coord_y1": 221, "coord_y2": 269, "page_start": 1, "page_end": 1, "line_start": 4, "line_end": 5, "knowledge_id": "", "node_id": "", "node_type": "", "data_languages": [], "data_keywords": [], "data_tags": [], "created_at": "2025-02-25T14:15:44.744930", "updated_at": "2025-02-25T14:15:44.744930"}, {"data_id": "67582a1d-54bd-44e6-8064-fdecc56bdb1f", "data_traceid": "7c06800e-89ca-4a69-ba9b-e5f70ba23a92", "data_version": 1, "data_type": "TEXT", "content_type": "default", "data_url": "", "data_status": 1, "raw_data": "During the Promotion Period of 1 October to 31 December 2024, when you successfully take out  Evergreen Wealth Multi-Currency Plan1,2 with the designated first year total annualised premium3, we will  give you up to a 23\\%  premium refund4 (the “Basic Offer”).", "processed_data": [], "data_dimension": -1, "coord_x1": 34, "coord_x2": 541, "coord_y1": 290, "coord_y2": 334, "page_start": 1, "page_end": 1, "line_start": 6, "line_end": 8, "knowledge_id": "", "node_id": "", "node_type": "", "data_languages": [], "data_keywords": [], "data_tags": [], "created_at": "2025-02-25T14:15:44.744930", "updated_at": "2025-02-25T14:15:44.744930"}, {"data_id": "842e2bbb-95c3-49fc-839c-2168a811727d", "data_traceid": "f489b327-f8c7-450b-b26b-19e314bcf8c6", "data_version": 1, "data_type": "TEXT", "content_type": "default", "data_url": "", "data_status": 1, "raw_data": "Based on your insurance needs, if you also take out any of our Selected Medical Insurance Plan(s)5,8,9 and/ or Selected Critical Illness Insurance Plan(s)6,7,9 during the Promotion Period, we will give you up to an extra 8\\%  premium refund of the first year annualised premium (the “Extra Offer 1” and “Extra Offer 2”) on  Evergreen Wealth Multi-Currency Plan, giving you a total of up to a 31% premium refund4.", "processed_data": [], "data_dimension": -1, "coord_x1": 33, "coord_x2": 543, "coord_y1": 341, "coord_y2": 400, "page_start": 1, "page_end": 1, "line_start": 9, "line_end": 12, "knowledge_id": "", "node_id": "", "node_type": "", "data_languages": [], "data_keywords": [], "data_tags": [], "created_at": "2025-02-25T14:15:44.744930", "updated_at": "2025-02-25T14:15:44.744930"}, {"data_id": "f0d18a0d-b96e-4a40-8153-77797f991d8d", "data_traceid": "6725a68b-f015-4648-990e-b6d8f60fe319", "data_version": 1, "data_type": "TEXT", "content_type": "default", "data_url": "", "data_status": 1, "raw_data": "The 3-year premium term option for Evergreen Wealth Multi-Currency Plan is excluded from this promotion.  2\t This promotion is not applicable to the Selected Savings Plan (as defined in clause 4(\\mathrm{i})  of the relevant terms and conditions) with the first  year annualised premium (excluding levy) above USD1,500,000 (or its equivalent). It means the sum of the first year annualised premium (excluding levy) of all Evergreen Wealth Multi-Currency Plan (with a 5-year  premium term and AUD, CAD, GBP, HKD, RMB or USD as the policy currency), in the name of the same individual policyholder, applied  for during the Promotion Period and issued on or before 31 January 2025. We calculate the first year total annualised premium in USD. There are more details in clauses 11 and 12 of the relevant terms and conditions. The premium refund amount for the Eligible Plan (as defined in clause 4 of the relevant terms and conditions) will be credited to the", "processed_data": [], "data_dimension": -1, "coord_x1": 34, "coord_x2": 556, "coord_y1": 34, "coord_y2": 120, "page_start": 2, "page_end": 2, "line_start": 1, "line_end": 8, "knowledge_id": "", "node_id": "", "node_type": "", "data_languages": [], "data_keywords": [], "data_tags": [], "created_at": "2025-02-25T14:15:44.747930", "updated_at": "2025-02-25T14:15:44.747930"}, {"data_id": "80f0d41c-390e-484b-959c-4b537b0b85b2", "data_traceid": "3df77925-0e79-41a5-9204-7e30e05982aa", "data_version": 1, "data_type": "TEXT", "content_type": "default", "data_url": "", "data_status": 1, "raw_data": "The premium refund amount for the Eligible Plan (as defined in clause 4 of the relevant terms and conditions) will be credited to the  premium deposit account of the eligible policy. For details, please refer to clauses 5 and 6 of the relevant terms and conditions.  5 Selected Medical Insurance Plan(s) (if applicable) consist of VHIS plan(s) and medical plan(s) as follows:", "processed_data": [], "data_dimension": -1, "coord_x1": 35, "coord_x2": 537, "coord_y1": 107, "coord_y2": 140, "page_start": 2, "page_end": 2, "line_start": 9, "line_end": 10, "knowledge_id": "", "node_id": "", "node_type": "", "data_languages": [], "data_keywords": [], "data_tags": [], "created_at": "2025-02-25T14:15:44.747930", "updated_at": "2025-02-25T14:15:44.747930"}, {"data_id": "ced71f89-6b79-4e8b-b1cf-6618dba13a62", "data_traceid": "24fb6e69-83a4-4837-a3e6-a7969e8fe7c4", "data_version": 1, "data_type": "TEXT", "content_type": "default", "data_url": "", "data_status": 1, "raw_data": "* The promotion is applicable to the Outpatient Care Benefit of PremierFlex Medical Plan. elected Critical Illness Insurance Plan(s) (if applicable) consist of PRUHealth Guardian Critical Illness Pla PRUHealth Baby Guardian Critical Illness Plan, PRUHealth Critical Illness Extended Care III and PRUHealth Critical Illness First Protect II. The sum of the first year total annualised premium (excluding levy) of Selected Critical Illness Insurance Plan(s) (if applicable) must be  HKD 10,000 or above. We calculate the first year total annualised premium in HKD. For policies in other currencies, the exchange rate of  HKD 7.8 to USD 1 will be applied to determine the HKD-equivalent first year total annualised premium. There are more details in clause 12  of the relevant terms and conditions. You may apply for tax deduction in Hong Kong on your qualifying premiums paid for the VHIS Plan(s). The premium refund amount is not", "processed_data": [], "data_dimension": -1, "coord_x1": 52, "coord_x2": 562, "coord_y1": 221, "coord_y2": 308, "page_start": 2, "page_end": 2, "line_start": 14, "line_end": 21, "knowledge_id": "", "node_id": "", "node_type": "", "data_languages": [], "data_keywords": [], "data_tags": [], "created_at": "2025-02-25T14:15:44.748720", "updated_at": "2025-02-25T14:15:44.748720"}, {"data_id": "d29d0548-8452-4282-8b54-33fa47395ff5", "data_traceid": "6a62a403-b1ad-42af-b100-c85e52fea34c", "data_version": 1, "data_type": "TEXT", "content_type": "default", "data_url": "", "data_status": 1, "raw_data": "You may apply for tax deduction in Hong Kong on your qualifying premiums paid for the VHIS Plan(s). The premium refund amount is not  eligible for tax deduction. For tax deduction details, please refer to the product brochures. All Selected Medical Insurance Plan(s) (if applicable) or Selected Critical Illness Insurance Plan(s) (if applicable), in the name of the same individual policyholder, applied for during the Promotion Period must have been issued on or before 28 February 2025.", "processed_data": [], "data_dimension": -1, "coord_x1": 53, "coord_x2": 560, "coord_y1": 295, "coord_y2": 339, "page_start": 2, "page_end": 2, "line_start": 22, "line_end": 24, "knowledge_id": "", "node_id": "", "node_type": "", "data_languages": [], "data_keywords": [], "data_tags": [], "created_at": "2025-02-25T14:15:44.748720", "updated_at": "2025-02-25T14:15:44.748720"}, {"data_id": "d35b96b4-c7dc-4517-899d-5560214ad12c", "data_traceid": "2dd14390-feb1-48b4-876b-30068d0add11", "data_version": 1, "data_type": "TEXT", "content_type": "default", "data_url": "", "data_status": 1, "raw_data": "Terms and Conditions", "processed_data": [], "data_dimension": -1, "coord_x1": 34, "coord_x2": 122, "coord_y1": 349, "coord_y2": 359, "page_start": 2, "page_end": 2, "line_start": 25, "line_end": 25, "knowledge_id": "", "node_id": "", "node_type": "", "data_languages": [], "data_keywords": [], "data_tags": [], "created_at": "2025-02-25T14:15:44.748720", "updated_at": "2025-02-25T14:15:44.748720"}, {"data_id": "d09c6536-09d8-4a14-9b79-95f5ff4a2ced", "data_traceid": "ae54377d-790d-48da-bf1c-183219077535", "data_version": 1, "data_type": "TEXT", "content_type": "default", "data_url": "", "data_status": 1, "raw_data": "1.\t The premium refund on the Evergreen Wealth Multi-Currency Plan (with a 5-year premium term) (the “Premium Refund”) promotion  (the “Promotion”) is offered by Prudential Hong Kong Limited (“Prudential” or “we”). The Promotion Period is from 1 October to 31  December 2024, both dates inclusive (the “Promotion Period”). The Promotion consists of 3 offers – the “Basic Offer”, the “Extra Offer 1”  and the “Extra Offer 2”. The Promotion is applicable to the policies applied for through the Agency channel or the Broker channel of Prudential. The 3-year premium term option for Evergreen Wealth Multi-Currency Plan is excluded from this Promotion. In order to be eligible for the Premium Refund under the Promotion,  (i)Customers must have successfullyappliedfor and submited the completed applicationforthe Evergreen Wealth Multi-Currency Plan (with a 5-year premium term and AUD, CAD, GBP, HKD, RMB or USD as the policy currency) (the “Selected Savings Plan”) within the", "processed_data": [], "data_dimension": -1, "coord_x1": 34, "coord_x2": 553, "coord_y1": 367, "coord_y2": 476, "page_start": 2, "page_end": 2, "line_start": 26, "line_end": 34, "knowledge_id": "", "node_id": "", "node_type": "", "data_languages": [], "data_keywords": [], "data_tags": [], "created_at": "2025-02-25T14:15:44.748720", "updated_at": "2025-02-25T14:15:44.748720"}, {"data_id": "68093975-b46b-43b4-97b2-c6520a5d904a", "data_traceid": "84c1fabc-1082-4d8e-a27e-2e2f15b442e2", "data_version": 1, "data_type": "TEXT", "content_type": "default", "data_url": "", "data_status": 1, "raw_data": "(with a 5-year premium term and AUD, CAD, GBP, HKD, RMB or USD as the policy currency) (the “Selected Savings Plan”) within the  Promotion Period, and any selected medical insurance plan(s) (if applicable) listed in the table mentioned in remark 5 (the “Selected  Medical Insurance Plan(s)”) and/or any selected critical illness insurance plan(s) (if applicable) listed in remark 6 (the “Selected Critical  Illness Insurance Plan(s)”) of this flyer to us within the Promotion Period, in the name of the same individual policyholder; (ii)\t the Selected Savings Plan must have been issued by us on or before 31 January 2025; the Selected Medical Insurance Plan(s) (if  applicable) and/or the Selected Critical Illness Insurance Plan(s) (if applicable) must have been issued by us on or before 28  February 2025; (iii)\t the first year annualised premium (excluding levy) of each Selected Savings Plan must be equal to or less than USD1,500,000  (or its equivalent);", "processed_data": [], "data_dimension": -1, "coord_x1": 54, "coord_x2": 561, "coord_y1": 463, "coord_y2": 572, "page_start": 2, "page_end": 2, "line_start": 35, "line_end": 42, "knowledge_id": "", "node_id": "", "node_type": "", "data_languages": [], "data_keywords": [], "data_tags": [], "created_at": "2025-02-25T14:15:44.748720", "updated_at": "2025-02-25T14:15:44.748720"}, {"data_id": "85e27fbf-ffcf-40cd-b753-79a0b8964274", "data_traceid": "46df04e3-c8ec-4f7b-8a4d-ff842be0a329", "data_version": 1, "data_type": "TEXT", "content_type": "default", "data_url": "", "data_status": 1, "raw_data": "(or its equivalent); (iv) the frst year annualised premium of the Selected Savings Plan, or the sum of the frst year annualised premium (excluding levy) of all Selected Savings Plans in the name of the same individual policyholder (in the case of more than 1 Selected Savings Plan), applied  for during the Promotion Period and issued on or before 31 January 2025 must meet the amount listed in the table on the 1st page  of this flyer; (v)\t the sum of first year annualised premium (excluding levy) of the Selected Critical Illness Insurance Plan(s) (if applicable), in the name of the same individual policyholder, applied for during the Promotion Period and issued on or before 28 February 2025 must be HKD 10,000 or above; (vi)\t the Selected Savings Plan, the Selected Medical Insurance Plan(s) (if applicable) and/or the Selected Critical Illness Insurance Plan(s) (if applicable) must remain in force when we apply the Premium Refund to the Selected Savings Plan; and", "processed_data": [], "data_dimension": -1, "coord_x1": 54, "coord_x2": 560, "coord_y1": 560, "coord_y2": 680, "page_start": 2, "page_end": 2, "line_start": 43, "line_end": 51, "knowledge_id": "", "node_id": "", "node_type": "", "data_languages": [], "data_keywords": [], "data_tags": [], "created_at": "2025-02-25T14:15:44.748720", "updated_at": "2025-02-25T14:15:44.748720"}, {"data_id": "94cf433a-ed6f-4f1d-8989-c8e78d0047e5", "data_traceid": "640f16dd-d32e-4678-a405-a73f65cec194", "data_version": 1, "data_type": "TEXT", "content_type": "default", "data_url": "", "data_status": 1, "raw_data": "(if applicable) must remain in force when we apply the Premium Refund to the Selected Savings Plan; and (vii)\tall the premiums and levy(ies) must have been fully settled when due. Selected Savings Plan will be eligible for the Premium Refund (the “Eligible Plan”) if the Selected Savings Plan, the Selected Medical  Insurance Plan(s) (if applicable) and the Selected Critical Illness Insurance Plan(s) (if applicable) meet all applicable requirements set  out in clause 4. Otherwise, the Premium Refund will be forfeited.", "processed_data": [], "data_dimension": -1, "coord_x1": 53, "coord_x2": 542, "coord_y1": 667, "coord_y2": 728, "page_start": 2, "page_end": 2, "line_start": 52, "line_end": 55, "knowledge_id": "", "node_id": "", "node_type": "", "data_languages": [], "data_keywords": [], "data_tags": [], "created_at": "2025-02-25T14:15:44.748720", "updated_at": "2025-02-25T14:15:44.748720"}, {"data_id": "f942c263-ecaa-4bd2-9043-64343ed1ce8e", "data_traceid": "47db6b9b-62cd-4135-930a-40ece6d87510", "data_version": 1, "data_type": "TEXT", "content_type": "default", "data_url": "", "data_status": 1, "raw_data": "The Premium Refund amount will be denominated in the policy currency and credited to the premium deposit account (“PDA”) of the  eligible policy as follows:", "processed_data": [], "data_dimension": -1, "coord_x1": 43, "coord_x2": 544, "coord_y1": 34, "coord_y2": 59, "page_start": 3, "page_end": 3, "line_start": 1, "line_end": 2, "knowledge_id": "", "node_id": "", "node_type": "", "data_languages": [], "data_keywords": [], "data_tags": [], "created_at": "2025-02-25T14:15:44.748720", "updated_at": "2025-02-25T14:15:44.748720"}, {"data_id": "d6034ec5-32fd-4def-8860-0b282e0357a6", "data_traceid": "f1282f57-1c5d-483f-b687-61908f8955b2", "data_version": 1, "data_type": "TEXT", "content_type": "default", "data_url": "", "data_status": 1, "raw_data": "The above premium payment mode means the premium payment mode at the time of policy issuance. A PDA is a policyholder’s premium  account set up by us for our policyholder to keep excess premium for future settlement of the relevant modal premium due (and the  corresponding levy, if there is a remaining balance in the PDA) until Premium Refund amount is fully utilised. Any undistributed or unused  Premium Refund will be forfeited if the policy is no longer in force.", "processed_data": [], "data_dimension": -1, "coord_x1": 53, "coord_x2": 561, "coord_y1": 209, "coord_y2": 257, "page_start": 3, "page_end": 3, "line_start": 6, "line_end": 9, "knowledge_id": "", "node_id": "", "node_type": "", "data_languages": [], "data_keywords": [], "data_tags": [], "created_at": "2025-02-25T14:15:44.748720", "updated_at": "2025-02-25T14:15:44.748720"}, {"data_id": "48920e19-6a90-43a7-8926-221a940a59c3", "data_traceid": "0c01180b-7981-4f92-a12e-b0525b8e5a38", "data_version": 1, "data_type": "TEXT", "content_type": "default", "data_url": "", "data_status": 1, "raw_data": "We shall restrict any withdrawal of the Premium Refund from the PDA and the Premium Refund is only intended for the settlement of future premiums (and levy(ies), if there is a remaining balance in the PDA). The Premium Refund is non-transferable to others or other  policies and cannot be exchanged or redeemed for cash even when the policy is surrendered, matured or lapsed.", "processed_data": [], "data_dimension": -1, "coord_x1": 53, "coord_x2": 544, "coord_y1": 258, "coord_y2": 293, "page_start": 3, "page_end": 3, "line_start": 10, "line_end": 12, "knowledge_id": "", "node_id": "", "node_type": "", "data_languages": [], "data_keywords": [], "data_tags": [], "created_at": "2025-02-25T14:15:44.748720", "updated_at": "2025-02-25T14:15:44.748720"}, {"data_id": "b388375c-dcb1-48fc-ab0a-0d58e2cf2bd2", "data_traceid": "e12dbc11-9b73-4218-99b7-10974d131728", "data_version": 1, "data_type": "TEXT", "content_type": "default", "data_url": "", "data_status": 1, "raw_data": "7.\t The Premium Refund is offered to each Eligible Plan. If a customer has successfully applied for more than 1 Eligible Plan during the  Promotion Period and fulfilled all other requirements stated under these terms and conditions, each Eligible Plan will qualify for the Premium Refund.", "processed_data": [], "data_dimension": -1, "coord_x1": 32, "coord_x2": 533, "coord_y1": 292, "coord_y2": 329, "page_start": 3, "page_end": 3, "line_start": 13, "line_end": 15, "knowledge_id": "", "node_id": "", "node_type": "", "data_languages": [], "data_keywords": [], "data_tags": [], "created_at": "2025-02-25T14:15:44.748720", "updated_at": "2025-02-25T14:15:44.748720"}, {"data_id": "b64d9dab-80bc-4174-9bbc-0ee39be09814", "data_traceid": "bfe96a58-1465-46e6-976f-60e27b9737fd", "data_version": 1, "data_type": "TEXT", "content_type": "default", "data_url": "", "data_status": 1, "raw_data": "8.\t For any alterations of the Eligible Plan(s) and/or the Selected Medical Insurance Plan(s) (if applicable) and/or the Selected Critical Illness Insurance Plan(s) (if applicable) after policy issuance (within or after the cooling-off period) which result in a reduction of premium  payable within the premium term (including but not limited to a decrease in notional amount, change of premium term, an increase in deductible, a downgrade of room level, a downgrade of plan level, cancellation or a downgrade of plan level of PRUHealth Major or PRUmed Major, if applicable, or a reduction in territorial scope of cover), the Premium Refund for the respective Eligible Plan(s) will  be totally forfeited. Nevertheless, if the policyholder has more than 1 Eligible Plan, the remaining Eligible Plan(s) will still qualify for the  Premium Refund, provided that the first year total annualised premium of such remaining Eligible Plan(s) under the same policyholder", "processed_data": [], "data_dimension": -1, "coord_x1": 34, "coord_x2": 553, "coord_y1": 329, "coord_y2": 413, "page_start": 3, "page_end": 3, "line_start": 16, "line_end": 22, "knowledge_id": "", "node_id": "", "node_type": "", "data_languages": [], "data_keywords": [], "data_tags": [], "created_at": "2025-02-25T14:15:44.748720", "updated_at": "2025-02-25T14:15:44.748720"}, {"data_id": "e93cb38e-c45a-47ed-b279-25ecc50d1d6c", "data_traceid": "27861d0d-e5d7-472a-871d-d584962b02e6", "data_version": 1, "data_type": "TEXT", "content_type": "default", "data_url": "", "data_status": 1, "raw_data": "Premium Refund, provided that the first year total annualised premium of such remaining Eligible Plan(s) under the same policyholder meets the amount listed in the table on the 1st page of this flyer. We will calculate the Premium Refund amount based on the relevant  percentage of the first year total annualised premium listed in the same table (please refer to clauses 11 and 12 for calculation of the first  year annualised premium). For any alterations of the Eligible Plan(s) and/or the Selected Medical Insurance Plan(s) (if applicable) and/or  the Selected Critical Illness Insurance Plan(s) (if applicable) after policy issuance (within or after the cooling-off period) which result in  an increase of premium payable within the premium term (including but not limited to an increase in notional amount, change of  premium term, a reduction in deductible, an upgrade of room level, an upgrade of plan level, addition or an upgrade of plan level of", "processed_data": [], "data_dimension": -1, "coord_x1": 52, "coord_x2": 559, "coord_y1": 401, "coord_y2": 486, "page_start": 3, "page_end": 3, "line_start": 23, "line_end": 28, "knowledge_id": "", "node_id": "", "node_type": "", "data_languages": [], "data_keywords": [], "data_tags": [], "created_at": "2025-02-25T14:15:44.748720", "updated_at": "2025-02-25T14:15:44.748720"}, {"data_id": "d7dbc5c7-eca3-45f2-b4c1-a76e72426feb", "data_traceid": "1a8b8996-a154-4986-b18d-9140092e3dbf", "data_version": 1, "data_type": "TEXT", "content_type": "default", "data_url": "", "data_status": 1, "raw_data": "premium term, a reduction in deductible, an upgrade of room level, an upgrade of plan level, addition or an upgrade of plan level of  PRUHealth Major or PRUmed Major, if applicable, or an expansion in territorial scope of cover), the increased portion of the increased  premium will NOT be eligible for this Promotion. Notwithstanding the above, if there is any change of premium payment mode during  the first policy year, the Eligible Plan(s) will still qualify for the Premium Refund, provided that the first year total annualised premium of the Eligible Plan(s) under the same policyholder meets the amount listed in the table on the 1st page of this flyer, and we will use the  lowest first year annualised premium of such Eligible Plan(s) to calculate the Premium Refund amount. In addition, for any alterations  after policy issuance (within or after the cooling-off period) and before the payment of Premium Refund which result in a change of", "processed_data": [], "data_dimension": -1, "coord_x1": 54, "coord_x2": 548, "coord_y1": 474, "coord_y2": 558, "page_start": 3, "page_end": 3, "line_start": 29, "line_end": 34, "knowledge_id": "", "node_id": "", "node_type": "", "data_languages": [], "data_keywords": [], "data_tags": [], "created_at": "2025-02-25T14:15:44.748720", "updated_at": "2025-02-25T14:15:44.748720"}, {"data_id": "9ec81bc8-f0b3-4919-a9bc-0d3d26049d02", "data_traceid": "e4dc9c31-90bd-4df0-86b0-b5b7eb60c872", "data_version": 1, "data_type": "TEXT", "content_type": "default", "data_url": "", "data_status": 1, "raw_data": "after policy issuance (within or after the cooling-off period) and before the payment of Premium Refund which result in a change of  policyholder under the Selected Savings Plan and/or the Selected Medical Insurance Plan(s) (if applicable) and/or the Selected Critical  Illness Insurance Plan(s) (if applicable), the Premium Refund for the respective Eligible Plan(s) will be totally forfeited.", "processed_data": [], "data_dimension": -1, "coord_x1": 54, "coord_x2": 546, "coord_y1": 546, "coord_y2": 582, "page_start": 3, "page_end": 3, "line_start": 35, "line_end": 36, "knowledge_id": "", "node_id": "", "node_type": "", "data_languages": [], "data_keywords": [], "data_tags": [], "created_at": "2025-02-25T14:15:44.748720", "updated_at": "2025-02-25T14:15:44.748720"}, {"data_id": "5597391e-fc5a-4d65-b2c6-b21ff6518c39", "data_traceid": "7bf73989-1056-4996-bada-56f0b3f873bb", "data_version": 1, "data_type": "TEXT", "content_type": "default", "data_url": "", "data_status": 1, "raw_data": "9.\t The Promotion will not be offered to the Selected Savings Plan and/or the Selected Medical Insurance Plan(s) (if applicable) and/or the  Selected Critical Illness Insurance Plan(s) (if applicable) applied for or already in force on or before 30 September 2024, or to any other  basic plan(s) or supplementary benefit(s), or to any policy conversion (except promotion (MKTX/PF0738E(10/24)) for selected customers). 10.\t We will calculate the Premium Refund amount based on each Eligible Plan’s first year annualised premium (excluding levy).", "processed_data": [], "data_dimension": -1, "coord_x1": 33, "coord_x2": 558, "coord_y1": 582, "coord_y2": 631, "page_start": 3, "page_end": 3, "line_start": 37, "line_end": 40, "knowledge_id": "", "node_id": "", "node_type": "", "data_languages": [], "data_keywords": [], "data_tags": [], "created_at": "2025-02-25T14:15:44.748720", "updated_at": "2025-02-25T14:15:44.748720"}, {"data_id": "ff3a29bb-98f4-4e9a-a7cc-056bf9649d07", "data_traceid": "77e03a79-afd0-4d40-8d86-95702613214c", "data_version": 1, "data_type": "TEXT", "content_type": "default", "data_url": "", "data_status": 1, "raw_data": "11.\t The first year total annualised premium is calculated in USD. For policies in other currencies, the exchange rate of AUD 1.3 to USD 1,  CAD 1.2 to USD 1, GBP 0.65 to USD 1, RMB 6.5 to USD 1 or HKD 7.8 to USD 1 will be applied to determine the USD-equivalent first year total annualised premium.", "processed_data": [], "data_dimension": -1, "coord_x1": 33, "coord_x2": 549, "coord_y1": 629, "coord_y2": 665, "page_start": 3, "page_end": 3, "line_start": 41, "line_end": 43, "knowledge_id": "", "node_id": "", "node_type": "", "data_languages": [], "data_keywords": [], "data_tags": [], "created_at": "2025-02-25T14:15:44.748720", "updated_at": "2025-02-25T14:15:44.748720"}, {"data_id": "705a92c4-ebd5-41d7-b864-b8bbb2fbe07f", "data_traceid": "9b152c38-528e-4827-b625-cd63bbe3b599", "data_version": 1, "data_type": "TEXT", "content_type": "default", "data_url": "", "data_status": 1, "raw_data": "12.\t If the premium of the Eligible Plan(s) is/are paid on a non-annual basis, its first year annualised premium shall be the total amount of  premium payments made in the first 12 months. For example, if the premium of the Eligible Plan(s) is/are paid on a monthly basis, the  respective first year annualised premium shall be equal to 12 times the monthly payment.", "processed_data": [], "data_dimension": -1, "coord_x1": 33, "coord_x2": 545, "coord_y1": 664, "coord_y2": 701, "page_start": 3, "page_end": 3, "line_start": 44, "line_end": 46, "knowledge_id": "", "node_id": "", "node_type": "", "data_languages": [], "data_keywords": [], "data_tags": [], "created_at": "2025-02-25T14:15:44.748720", "updated_at": "2025-02-25T14:15:44.748720"}, {"data_id": "b4797c4b-2ffb-4f11-b8b0-9582a7f275f6", "data_traceid": "ac971e21-ac59-4d76-8fd2-719df8532911", "data_version": 1, "data_type": "TEXT", "content_type": "default", "data_url": "", "data_status": 1, "raw_data": "13.\t The Promotion can be used in conjunction with any other promotional offer unless otherwise specified, while the Promotion cannot be used in conjunction with Evergreen Wealth Multi-Currency Plan Promotion (MKTX/PF0735E(10/24)).", "processed_data": [], "data_dimension": -1, "coord_x1": 34, "coord_x2": 546, "coord_y1": 701, "coord_y2": 725, "page_start": 3, "page_end": 3, "line_start": 47, "line_end": 48, "knowledge_id": "", "node_id": "", "node_type": "", "data_languages": [], "data_keywords": [], "data_tags": [], "created_at": "2025-02-25T14:15:44.748720", "updated_at": "2025-02-25T14:15:44.748720"}, {"data_id": "195ccd14-52b5-496b-8461-0575f9db89d1", "data_traceid": "e56e4da1-3eeb-4474-95b7-b5c67d2b0f1a", "data_version": 1, "data_type": "TEXT", "content_type": "default", "data_url": "", "data_status": 1, "raw_data": "14.\t The Premium Refund under the Promotion will form part of the policy contract upon the respective policy and/or the respective  supplementary benefit (if applicable) being issued if the requirements of the Premium Refund under the terms and conditions of the  Promotion are satisfactorily fulfilled.", "processed_data": [], "data_dimension": -1, "coord_x1": 34, "coord_x2": 540, "coord_y1": 724, "coord_y2": 760, "page_start": 3, "page_end": 3, "line_start": 49, "line_end": 51, "knowledge_id": "", "node_id": "", "node_type": "", "data_languages": [], "data_keywords": [], "data_tags": [], "created_at": "2025-02-25T14:15:44.748720", "updated_at": "2025-02-25T14:15:44.748720"}, {"data_id": "a35f2c1e-8621-4eda-adbb-ec4d17a34bff", "data_traceid": "1e748e4e-3c54-4bce-8cf4-2f26d061e97d", "data_version": 1, "data_type": "TEXT", "content_type": "default", "data_url": "", "data_status": 1, "raw_data": "15.\t The Selected Savings Plan, the Selected Medical Insurance Plan(s) and the Selected Critical Illness Insurance Plan(s) are underwritten by  Prudential Hong Kong Limited, and are subject to all respective policy terms and conditions. For product information, please refer to the  terms and conditions set out in the product brochure and specimen policy(ies) issued by us.", "processed_data": [], "data_dimension": -1, "coord_x1": 34, "coord_x2": 554, "coord_y1": 761, "coord_y2": 798, "page_start": 3, "page_end": 3, "line_start": 52, "line_end": 54, "knowledge_id": "", "node_id": "", "node_type": "", "data_languages": [], "data_keywords": [], "data_tags": [], "created_at": "2025-02-25T14:15:44.748720", "updated_at": "2025-02-25T14:15:44.748720"}, {"data_id": "0223e4d2-96e3-4384-846c-de359b6c59eb", "data_traceid": "1c0dfe79-2950-41ac-a670-e5b855208947", "data_version": 1, "data_type": "TEXT", "content_type": "default", "data_url": "", "data_status": 1, "raw_data": "16.\t We reserve the right to change any terms and conditions of this Promotion without issuing further notices. In the event of any disputes, we shall have the absolute discretion to make the final decision.", "processed_data": [], "data_dimension": -1, "coord_x1": 33, "coord_x2": 550, "coord_y1": 795, "coord_y2": 822, "page_start": 3, "page_end": 3, "line_start": 55, "line_end": 56, "knowledge_id": "", "node_id": "", "node_type": "", "data_languages": [], "data_keywords": [], "data_tags": [], "created_at": "2025-02-25T14:15:44.748720", "updated_at": "2025-02-25T14:15:44.748720"}, {"data_id": "7e4d9f83-0cb7-4606-9031-fef6ea424880", "data_traceid": "88a591d1-51d5-4ae9-8d2d-f3648634fb01", "data_version": 1, "data_type": "TEXT", "content_type": "default", "data_url": "", "data_status": 1, "raw_data": "Notes", "processed_data": [], "data_dimension": -1, "coord_x1": 33, "coord_x2": 61, "coord_y1": 33, "coord_y2": 44, "page_start": 4, "page_end": 4, "line_start": 1, "line_end": 1, "knowledge_id": "", "node_id": "", "node_type": "", "data_languages": [], "data_keywords": [], "data_tags": [], "created_at": "2025-02-25T14:15:44.748720", "updated_at": "2025-02-25T14:15:44.748720"}, {"data_id": "d6cb8b34-aa33-4598-b978-42ee48ef0258", "data_traceid": "8d893a4f-f6ec-4da3-ab46-b1b894d3d3c6", "data_version": 1, "data_type": "TEXT", "content_type": "default", "data_url": "", "data_status": 1, "raw_data": "You can always choose to take out the above-mentioned plan(s) as a standalone plan without enrolling with other type(s) of  insurance product at the same time, unless such plan(s) is/are only available as a supplementary benefit which needs to be attached to  a basic plan.", "processed_data": [], "data_dimension": -1, "coord_x1": 34, "coord_x2": 561, "coord_y1": 51, "coord_y2": 87, "page_start": 4, "page_end": 4, "line_start": 2, "line_end": 4, "knowledge_id": "", "node_id": "", "node_type": "", "data_languages": [], "data_keywords": [], "data_tags": [], "created_at": "2025-02-25T14:15:44.748720", "updated_at": "2025-02-25T14:15:44.748720"}, {"data_id": "8afff1a2-3d69-4439-80e6-12760f8550f5", "data_traceid": "23ea8b57-61fb-4182-ae89-7c5ee229ebea", "data_version": 1, "data_type": "TEXT", "content_type": "default", "data_url": "", "data_status": 1, "raw_data": "The product details and other relevant information listed above are for reference only. It does not constitute any contract or any part thereof  between us and any persons or entities (unless otherwise stated). During the sales process, this flyer should be read in conjunction with the  relevant product brochure. For full terms and conditions, and risk disclosures of the relevant insurance plan, please refer to relevant product brochure and policy document and read carefully. Prudential will be happy to provide a specimen of the policy document upon your  request.", "processed_data": [], "data_dimension": -1, "coord_x1": 34, "coord_x2": 554, "coord_y1": 99, "coord_y2": 159, "page_start": 4, "page_end": 4, "line_start": 5, "line_end": 9, "knowledge_id": "", "node_id": "", "node_type": "", "data_languages": [], "data_keywords": [], "data_tags": [], "created_at": "2025-02-25T14:15:44.748720", "updated_at": "2025-02-25T14:15:44.748720"}, {"data_id": "5a9e566b-076c-4c10-897a-10b5eec4afa5", "data_traceid": "2a3d2035-a2c4-4695-8cec-b33c7a8e8bd4", "data_version": 1, "data_type": "TEXT", "content_type": "default", "data_url": "", "data_status": 1, "raw_data": "Policyholders must meet all the eligibility requirements set out under the Inland Revenue Ordinance and any guidance issued by the  Inland Revenue Department of the Hong Kong Special Administrative Region before they can claim the relevant tax deduction. All of the above general tax information provided is for reference only. You should always consult with a professional tax advisor if you have any doubts. For further information on tax concessions applicable to VHIS plans, please refer to www.vhis.gov.hk/en.", "processed_data": [], "data_dimension": -1, "coord_x1": 33, "coord_x2": 558, "coord_y1": 171, "coord_y2": 219, "page_start": 4, "page_end": 4, "line_start": 10, "line_end": 13, "knowledge_id": "", "node_id": "", "node_type": "", "data_languages": [], "data_keywords": [], "data_tags": [], "created_at": "2025-02-25T14:15:44.748720", "updated_at": "2025-02-25T14:15:44.748720"}, {"data_id": "4d71d85a-04ea-4489-addd-40443c9bd794", "data_traceid": "322348c8-12f5-4959-a14c-ecb8549e222d", "data_version": 1, "data_type": "TEXT", "content_type": "default", "data_url": "", "data_status": 1, "raw_data": "This flyer is for distribution in Hong Kong only. It is not an offer to sell or solicitation to buy or provide any insurance product outside Hong Kong.  Prudential does not offer or sell any insurance product in any jurisdictions outside Hong Kong where such offering or sale of the insurance product  is illegal under the laws of such jurisdictions.", "processed_data": [], "data_dimension": -1, "coord_x1": 33, "coord_x2": 556, "coord_y1": 230, "coord_y2": 267, "page_start": 4, "page_end": 4, "line_start": 14, "line_end": 16, "knowledge_id": "", "node_id": "", "node_type": "", "data_languages": [], "data_keywords": [], "data_tags": [], "created_at": "2025-02-25T14:15:44.748720", "updated_at": "2025-02-25T14:15:44.748720"}, {"data_id": "928283f8-503b-4a53-ad85-aaa76e634d12", "data_traceid": "d824a9cd-e709-4d85-9e09-79f27bb683b3", "data_version": 1, "data_type": "TEXT", "content_type": "default", "data_url": "", "data_status": 1, "raw_data": "This flyer is issued by Prudential Hong Kong Limited (Part of Prudential plc (United Kingdom)).", "processed_data": [], "data_dimension": -1, "coord_x1": 35, "coord_x2": 369, "coord_y1": 279, "coord_y2": 291, "page_start": 4, "page_end": 4, "line_start": 17, "line_end": 17, "knowledge_id": "", "node_id": "", "node_type": "", "data_languages": [], "data_keywords": [], "data_tags": [], "created_at": "2025-02-25T14:15:44.748720", "updated_at": "2025-02-25T14:15:44.748720"}], "image": [{"data_id": "23736746-21db-4803-b5e6-d22600deac82", "data_traceid": "1e91c82e-d780-48a6-9b15-dacd394a99df", "data_version": 1, "data_type": "IMAGE", "content_type": "default", "data_url": "C:\\Users\\<USER>\\Documents\\pru-ai-pil\\.prudential_share_storage_temp\\_image_page-1_1_2025-02-25-14-15-44.jpg", "data_status": 1, "raw_data": "", "processed_data": [], "data_dimension": -1, "coord_x1": 13, "coord_x2": 590, "coord_y1": 0, "coord_y2": 196, "page_start": 1, "page_end": 1, "line_start": -1, "line_end": -1, "knowledge_id": "", "node_id": "", "node_type": "", "data_languages": [], "data_keywords": [], "data_tags": [], "created_at": "2025-02-25T14:15:44.744930", "updated_at": "2025-02-25T14:15:44.744930"}], "table": [{"data_id": "74c7c83d-8938-4939-9787-d74cbae8dc7f", "data_traceid": "ddeb6d7e-88ec-4062-9b8d-1b68f4296155", "data_version": 1, "data_type": "TABLE", "content_type": "default", "data_url": "C:\\Users\\<USER>\\Documents\\pru-ai-pil\\.prudential_share_storage_temp\\_table_page-1_1_2025-02-25-14-15-44.jpg", "data_status": 1, "raw_data": "<html><body><table><tr><td rowspan=\"2\">Selected Savings Plan Premium (AUD/CAD/GBP/HKD/ Term RMB/USD plan)</td><td rowspan=\"2\">First Year Total Annualised Premium3 (USD)</td><td colspan=\"4\">Premium Refund of the First Year Annualised Premium'</td></tr><tr><td>Basic Offer</td><td>Extra Offer 1</td><td>Extra Offer 2</td><td>Basic Offer Extra Offer 1 Extra Offer 2</td></tr><tr><td rowspan=\"5\">Evergreen Wealth Multi-Currency 5 years Plan1.2</td><td>Below 50,000</td><td>8%</td><td rowspan=\"5\">4% (If enrol together with anySelected</td><td rowspan=\"5\"></td><td>16%</td></tr><tr><td>50,000-99,999</td><td>13%</td><td>4% (If enrol</td><td>21%</td></tr><tr><td>100,000-299,999</td><td>17%</td><td>together with any Selected Critical Illness</td><td>25%</td></tr><tr><td>300.000-499.999</td><td>19%</td><td>Medical Insurance Insurance Plan(s)5.8,9) Plan(s)6.7.9)</td><td>27%</td></tr><tr><td>500,000 or above2</td><td>23%</td><td></td><td>31%</td></tr></table></body></html>", "processed_data": [], "data_dimension": -1, "coord_x1": 36, "coord_x2": 564, "coord_y1": 422, "coord_y2": 671, "page_start": 1, "page_end": 1, "line_start": -1, "line_end": -1, "knowledge_id": "", "node_id": "", "node_type": "", "data_languages": [], "data_keywords": [], "data_tags": [], "created_at": "2025-02-25T14:15:44.747930", "updated_at": "2025-02-25T14:15:44.747930"}, {"data_id": "38e77f82-7198-4913-acd5-29bf89dd853e", "data_traceid": "ee0f88fb-926f-4680-ba9a-5594b2482583", "data_version": 1, "data_type": "TABLE", "content_type": "default", "data_url": "C:\\Users\\<USER>\\Documents\\pru-ai-pil\\.prudential_share_storage_temp\\_table_page-2_1_2025-02-25-14-15-44.jpg", "data_status": 1, "raw_data": "<html><body><table><tr><td>Medical Plan(s)</td><td>VHISPIan(s)</td></tr><tr><td>PremierFlexMedicalPlan</td><td>PRUHeaIthVHISVIPPlan</td></tr><tr><td>PRUhealthmedical plus</td><td>PRUHealthCoreChoiceMedicalPlan</td></tr><tr><td>PRUmyhealthprestigemedical plan</td><td>PRUHealthVHIS EasyChoicePlan</td></tr><tr><td>PRUmed lifelong careplan</td><td>PRUHealthFlexiChoiceMedicalPlan</td></tr></table></body></html>", "processed_data": [], "data_dimension": -1, "coord_x1": 54, "coord_x2": 562, "coord_y1": 144, "coord_y2": 217, "page_start": 2, "page_end": 2, "line_start": -1, "line_end": -1, "knowledge_id": "", "node_id": "", "node_type": "", "data_languages": [], "data_keywords": [], "data_tags": [], "created_at": "2025-02-25T14:15:44.748720", "updated_at": "2025-02-25T14:15:44.748720"}, {"data_id": "2ceb43cf-066c-437a-8380-d0f53e24f452", "data_traceid": "7080c60f-e83f-463f-9505-b2336ada06d7", "data_version": 1, "data_type": "TABLE", "content_type": "default", "data_url": "C:\\Users\\<USER>\\Documents\\pru-ai-pil\\.prudential_share_storage_temp\\_table_page-3_1_2025-02-25-14-15-44.jpg", "data_status": 1, "raw_data": "<html><body><table><tr><td rowspan=\"2\">Premium payment mode</td><td colspan=\"5\">Date of Premium Refund</td></tr><tr><td>Basic Offer</td><td colspan=\"2\">Extra Offer 1 1st batch 2nd batch (2% of the first year (2% of the first year (2% of the first year (2% of the first year</td><td colspan=\"2\">Extra Offer 2 1st batch 2nd batch</td></tr><tr><td></td><td>On or before</td><td>annualised premium) On orbefore</td><td>annualised premium) Onorbefore</td><td>annualised premium) Onorbefore</td><td>annualised premium) Onorbefore</td></tr><tr><td>Annual mode Semi-annualmode</td><td>31August 2025</td><td>31August 2026</td><td>31August 2027</td><td>31August 2026</td><td>31August2027</td></tr><tr><td>Quarterly mode Monthlymode</td><td>On or before 28February2026</td><td>On or before 28February2027</td><td>On or before 29February2028</td><td>Onorbefore 28 February 2027</td><td>On or before 29February2028</td></tr></table></body></html>", "processed_data": [], "data_dimension": -1, "coord_x1": 53, "coord_x2": 560, "coord_y1": 62, "coord_y2": 202, "page_start": 3, "page_end": 3, "line_start": -1, "line_end": -1, "knowledge_id": "", "node_id": "", "node_type": "", "data_languages": [], "data_keywords": [], "data_tags": [], "created_at": "2025-02-25T14:15:44.748720", "updated_at": "2025-02-25T14:15:44.748720"}]}, "layout_fail_objects": [], "layout_extracted_types": {"text": 33, "image": 1, "table": 3}, "layout_analysis_metrics": {"layout_code": "200", "layout_reason": "Layout Analysis Completed : <PreprocessingHub> Completed Layout Analysis", "layout_total_no": 37, "layout_success_no": 37, "layout_fail_no": 0, "layout_total_page_no": 4, "layout_input_tokens": 0, "layout_output_tokens": 0, "layout_analysis_time": 92.9876971244812}, "response_at": "2025-02-25T14:15:44.759834"}