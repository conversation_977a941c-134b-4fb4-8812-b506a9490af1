import os
import shutil

from ..settings import SETTINGS

from ..logger.log_handler import get_logger

logger = get_logger(__name__)

if SETTINGS.BASE.APP_FUNC == False and SETTINGS.BASE.APP_API == False:
    err_msg = f"Failed to Inititate <{SETTINGS.BASE.APP_NAME}> : APP_FUNC and APP_API cannot be ALL False"
    logger.error(err_msg)
    raise Exception(err_msg)


# # Import Functions if APP_FUNC == True
# if SETTINGS.BASE.APP_FUNC == True:
#     # Import KnowledgeInput Function
#     try:
#         module = __import__(SETTINGS.KWIN.REQUEST_INGEST_MODULE, fromlist=[SETTINGS.KWIN.REQUEST_INGEST_FUNC])
#         request_knowledgeinput_ingest = getattr(module, SETTINGS.KWIN.REQUEST_INGEST_FUNC)
#     except:
#         err_msg = f"Import Error : <{SETTINGS.BASE.APP_NAME}> Failed to Import <KnowledgeInputHub> Module"
#         logger.error(err_msg)
#         raise Exception(err_msg)

#     # Import Preprocessing Function
#     try:
#         module = __import__(SETTINGS.PREP.REQUEST_PREPKNOW_MODULE, fromlist=[SETTINGS.PREP.REQUEST_PREPKNOW_FUNC])
#         request_exec_prepknow = getattr(module, SETTINGS.PREP.REQUEST_PREPKNOW_FUNC)
#     except:
#         err_msg = f"Import Error : <{SETTINGS.BASE.APP_NAME}> Failed to Import <PreprocessingHub> Module"
#         logger.error(err_msg)
#         raise Exception(err_msg)


#     # Import Security Function
#     try:
#         module = __import__(SETTINGS.SCRT.REQUEST_USERKEY_MODULE, fromlist=[SETTINGS.SCRT.REQUEST_USERKEY_FUNC])
#         request_retrieve_userkey = getattr(module, SETTINGS.SCRT.REQUEST_USERKEY_FUNC)        
#     except:
#         err_msg = f"Import Error : <{SETTINGS.BASE.APP_NAME}> Failed to Import <SecurityHub> Module"
#         logger.error(err_msg)
#         raise Exception(err_msg)
