from pydantic import BaseModel, <PERSON>
from typing import Literal
import uuid
from datetime import datetime

from ..settings import SETTINGS

from ..database.vector.schemas.vector import *

"""
    Keyword Search
"""
class KeywordSearchRequest(BaseModel):
    retrieval_requestid:   str=Field(default_factory=lambda: str(uuid.uuid4()))
    knowledge_ids:         list[str] | None = []
    keywords:              list[str] | None = []
    keyword_search_config: dict=Field(default=dict(), description="Keyword search configuration")
    blacklist:              list[str] | None = []

class KeywordSearchResponse(BaseModel):
    retrieval_requestid:     str
    knowledge_ids:           list[str]=[]
    keyword_retrieval_count: int=0
    keyword_retrieval_time:  float=0.0

"""
    Vector Search
"""
class VectorData(BaseModel):
    # Trace Information
    data_id:        str=Field(default_factory=lambda: str(uuid.uuid4()))
    data_traceid:   str=Field(default_factory=lambda: str(uuid.uuid4()))
    data_version:   int=1

    # Category Information
    data_type:      str='' # TEXT, IMAGE, TABLE, DOCUMENT, etc.
    content_type:   str='default' # default, image-to-text, OCR
    data_url:       str='' # Image URL, Table URL

    # Control Information
    data_status:    int=1

    # Specification
    raw_data:       str='' # Raw Text, Image Description, Text on Image, etc.
    processed_data: list[float]=[] # vector
    data_length:    int=0

    coord_x1:       float=-1.0
    coord_x2:       float=-1.0
    coord_y1:       float=-1.0
    coord_y2:       float=-1.0

    page_start:     int=-1
    page_end:       int=-1
    line_start:     int=-1
    line_end:       int=-1
    seq_no:         int=-1

    # Dependent
    knowledge_id:   str='' # Map to Original Knowledge
    node_id:        str=''
    node_type:      str=''

    # Tags
    data_languages: list[str]=[]
    data_keywords:  list[str]=[]
    data_tags:      list[str]=[]

    # Time Information
    created_at:     datetime = Field(default_factory=datetime.now)
    updated_at:     datetime = Field(default_factory=datetime.now)

    # Scoring
    score:          float=-1.0

"""
    Vector Search
"""
class VectorSearchRequest(BaseModel):
    retrieval_requestid: str=Field(default_factory=lambda: str(uuid.uuid4()))
    knowledge_ids:       list[str] | None = []
    data_ids:            list[str] | None = []
    query:               str=''
    processed_query:     list[float] | None = []
    search_method:       str='hybrid'
    search_config:       dict=Field(default=dict(), description="Vector search configuration")




class VectorSearchResponse(BaseModel):
    retrieval_requestid:    str
    retrieved_data:         list[VectorData] = Field(default=[], description="List of CitationObjects")
    vector_retrieval_count: int=0
    vector_retrieval_time:  float=0.0

class KnowledgeVectorSearchRequest(BaseModel):
    retrieval_requestid: str=Field(default_factory=lambda: str(uuid.uuid4()))
    knowledge_ids:       str | None = None