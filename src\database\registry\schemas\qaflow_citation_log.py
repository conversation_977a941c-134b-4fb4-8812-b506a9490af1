from pydantic import BaseModel, Field
import uuid
from datetime import datetime, timezone

from ....settings import SETTINGS

""""
    QAFlowCitationLog General Operation
"""
class QAFlowCitationLogCreate(BaseModel):
    # Trace Information
    qaflow_citation_id: str=Field(default_factory=lambda: str(uuid.uuid4()))
    
    # Citation Content Information
    data_id:            str=Field(default_factory=lambda: str(uuid.uuid4()))
    data_version:       int=1
    data_status:        int=1

    # PIL Information
    library_name_en:    str=''
    library_name_tc:    str='' # Missing Now
    category_name_en:   str=''
    category_name_tc:   str=''
    title_name_en:      str=''  
    title_name_tc:      str=''
    document_id:        str=''
    file_name:          str=''
    library_id:         str='' # Missing
    category_id:        str='' # Missing

    # Category Information
    data_type:          str=''
    content_type:       str=''    
    
    # Specification
    raw_data:           str=''
    
    coord_x1:           float=-1.0
    coord_x2:           float=-1.0
    coord_y1:           float=-1.0
    coord_y2:           float=-1.0

    page_start:         int=-1
    page_end:           int=-1
    line_start:         int=-1
    line_end:           int=-1
    seq_no:             int=-1

    # Dependent
    knowledge_id:       str=''
    node_id:            str=''
    node_type:          str=''

    # Tags
    data_languages:     list[str]=[]
    data_keywords:      list[str]=[]
    data_tags:          list[str]=[]

    # Retrieval Score
    score:              float=-1.0

    # Time Information
    created_at:         datetime=Field(default_factory=lambda: datetime.now(timezone.utc))
    updated_at:         datetime=Field(default_factory=lambda: datetime.now(timezone.utc))

class QAFlowCitationLogCreateRequest(BaseModel):
    user_requestid: str | None = None
    user_id:        str=''
    user_name:      str=''
    is_admin:       bool=False
    data:           QAFlowCitationLogCreate

class QAFlowCitationLogBatchCreateRequest(BaseModel):
    create_requests: list[QAFlowCitationLogCreateRequest]

# QAFlowCitationLog CRUD
class QAFlowCitationLogUpdate(BaseModel):
    # Trace Information
    qaflow_citation_id: str | None = None
    
    # Citation Content Information
    data_id:            str | None = None
    data_version:       int | None = None
    data_status:        int | None = None

    # Category Information
    data_type:          str | None = None
    content_type:       str | None = None    

    # PIL Information
    library_name_en:    str | None = None
    library_name_tc:    str | None = None # Missing Now
    category_name_en:   str | None = None
    category_name_tc:   str | None = None
    title_name_en:      str | None = None  
    title_name_tc:      str | None = None
    document_id:        str | None = None
    file_name:          str | None = None
    library_id:         str | None = None # Missing
    category_id:        str | None = None # Missing

    # Specification
    raw_data:           str | None = None
    
    coord_x1:           float | None = None
    coord_x2:           float | None = None
    coord_y1:           float | None = None
    coord_y2:           float | None = None

    page_start:         int | None = None
    page_end:           int | None = None
    line_start:         int | None = None
    line_end:           int | None = None
    seq_no:             int | None = None

    # Dependent
    knowledge_id:       str | None = None
    node_id:            str | None = None
    node_type:          str | None = None

    # Tags
    data_languages:     list[str] | None = None
    data_keywords:      list[str] | None = None
    data_tags:          list[str] | None = None

    # Retrieval Score
    score:              float | None = None

    # Time Information
    created_at:         datetime | None = None
    updated_at:         datetime | None = None


class QAFlowCitationLogUpdateRequest(BaseModel):
    user_requestid:     str | None = None
    user_id:            str=''
    user_name:          str=''
    is_admin:           bool=False
    qaflow_citation_id: str | None = None
    update_data:        QAFlowCitationLogUpdate=QAFlowCitationLogUpdate()
    overwrite:          bool = False
    
class QAFlowCitationLogRequest(BaseModel):
    user_requestid:     str | None = None
    user_id:            str | None = None
    user_name:          str | None = None
    qaflow_citation_id: str | None = None

class QAFlowCitationLogBatchRequest(BaseModel):
    batch_requests: list[QAFlowCitationLogRequest]

# System-level Access
class SecretQAFlowCitationLog(BaseModel):
    # Trace Information
    qaflow_citation_id: str | None = None
    
    # Citation Content Information
    data_id:            str | None = None
    data_version:       int | None = None
    data_status:        int | None = None

    # Category Information
    data_type:          str | None = None
    content_type:       str | None = None    

    # PIL Information
    library_name_en:    str | None = None
    library_name_tc:    str | None = None # Missing Now
    category_name_en:   str | None = None
    category_name_tc:   str | None = None
    title_name_en:      str | None = None  
    title_name_tc:      str | None = None
    document_id:        str | None = None
    file_name:          str | None = None
    library_id:         str | None = None # Missing
    category_id:        str | None = None # Missing
    
    # Specification
    raw_data:           str | None = None
    
    coord_x1:           float | None = None
    coord_x2:           float | None = None
    coord_y1:           float | None = None
    coord_y2:           float | None = None

    page_start:         int | None = None
    page_end:           int | None = None
    line_start:         int | None = None
    line_end:           int | None = None
    seq_no:             int | None = None

    # Dependent
    knowledge_id:       str | None = None
    node_id:            str | None = None
    node_type:          str | None = None

    # Tags
    data_languages:     list[str] | None = None
    data_keywords:      list[str] | None = None
    data_tags:          list[str] | None = None

    # Retrieval Score
    score:              float | None = None

    # Time Information
    created_at:         datetime | None = None
    updated_at:         datetime | None = None

"""
    QAFlowCitationLog Filter
"""   
class QAFlowCitationLogStringFilter(BaseModel):
    qaflow_citation_id_filter: list[str] | None = None

    data_id_filter:            list[str] | None = None

    data_type_filter:          list[str] | None = None
    content_type_filter:       list[str] | None = None

    library_name_en_filter:    list[str] | None = None
    library_name_tc_filter:    list[str] | None = None # Missing Now
    category_name_en_filter:   list[str] | None = None
    category_name_tc_filter:   list[str] | None = None
    title_name_en_filter:      list[str] | None = None  
    title_name_tc_filter:      list[str] | None = None
    document_id_filter:        list[str] | None = None
    file_name_filter:          list[str] | None = None
    library_id_filter:         list[str] | None = None # Missing
    category_id_filter:        list[str] | None = None # Missing

    raw_data_filter:           list[str] | None = None

    knowledge_id_filter:       list[str] | None = None
    node_id_filter:            list[str] | None = None
    node_type_filter:          list[str] | None = None


class QAFlowCitationLogNumericFilter(BaseModel):
    data_version_min: int | None = None
    data_version_max: int | None = None
    data_status_min:  int | None = None
    data_status_max:  int | None = None

    coord_x1_min:     float | None = None
    coord_x1_max:     float | None = None
    coord_x2_min:     float | None = None
    coord_x2_max:     float | None = None
    coord_y1_min:     float | None = None
    coord_y1_max:     float | None = None
    coord_y2_min:     float | None = None
    coord_y2_max:     float | None = None

    page_start_min:   int   | None = None
    page_start_max:   int   | None = None
    page_end_min:     int   | None = None
    page_end_max:     int   | None = None
    line_start_min:   int   | None = None
    line_start_max:   int   | None = None
    line_end_min:     int   | None = None
    line_end_max:     int   | None = None
    seq_no_min:       int   | None = None
    seq_no_max:       int   | None = None

    score_min:        float | None = None
    score_max:        float | None = None
    
class QAFlowCitationLogListFilter(BaseModel):
    data_languages_or:  list[str] | None = None
    data_languages_and: list[str] | None = None    
    data_keywords_or:   list[str] | None = None
    data_keywords_and:  list[str] | None = None
    data_tags_or:       list[str] | None = None
    data_tags_and:      list[str] | None = None

class QAFlowCitationLogDictionaryFilter(BaseModel):
    not_used_or:  list[str] | None = None
    not_used_and: list[str] | None = None

class QAFlowCitationLogBooleanFilter(BaseModel):
    not_used_filter: bool | None = None

class QAFlowCitationLogDatetimeFilter(BaseModel):
    created_at_start: datetime  | None = None
    created_at_end:   datetime  | None = None
    updated_at_start: datetime  | None = None
    updated_at_end:   datetime  | None = None

class QAFlowCitationLogByteFilter(BaseModel):
    not_used_filter: list[bytes] | None = None

class QAFlowCitationLogFilter(BaseModel):
    string_filter:     QAFlowCitationLogStringFilter     | None = None
    numeric_filter:    QAFlowCitationLogNumericFilter    | None = None
    list_filter:       QAFlowCitationLogListFilter       | None = None
    dictionary_filter: QAFlowCitationLogDictionaryFilter | None = None
    boolean_filter:    QAFlowCitationLogBooleanFilter    | None = None
    datetime_filter:   QAFlowCitationLogDatetimeFilter   | None = None
    byte_filter:       QAFlowCitationLogByteFilter       | None = None
    sorting:           dict={"qaflow_citation_id": "asc"}
    filter_no:         int=-1


""" 
    Request and Resposne for System Access QAFlowCitationLogs
"""
class SystemQAFlowCitationLogRequest(BaseModel):
    knowledge_requestid: str=Field(default_factory=lambda: str(uuid.uuid4()))
    data_filter:         QAFlowCitationLogFilter | None = None

class SystemQAFlowCitationLogResponse(BaseModel):
    knowledge_requestid: str
    filtered_data:       list[SecretQAFlowCitationLog]=[]
    data_count:          int=0


"""
    Data Backup / Restore Configuration
"""
class BackupConfig(BaseModel):
    format:   str | None = None
    location: str | None = None
    name:     str | None = None
    host:     str | None = None
    port:     str | None = None
    user:     str | None = None
    pswd:     str | None = None
    table:    str | None = None
    rdir:     str | None = None
    sdir:     str | None = None
    limit:    int | None = None

class QAFlowCitationLogBackupRequest(BaseModel):
    knowledge_requestid: str=Field(default_factory=lambda: str(uuid.uuid4()))
    data_filter:         QAFlowCitationLogFilter | None = None
    backup_config:       BackupConfig | None = None

class QAFlowCitationLogBackupListRequest(BaseModel):
    knowledge_requestid: str=Field(default_factory=lambda: str(uuid.uuid4()))
    backup_config:       BackupConfig | None = None

class QAFlowCitationLogBackupListResponse(BaseModel):
    knowledge_requestid: str=Field(default_factory=lambda: str(uuid.uuid4()))
    table_list:          list[str]=[]

class RestoreConfig(BaseModel):
    format:   str | None = None
    location: str | None = None
    name:     str | None = None
    host:     str | None = None
    port:     str | None = None
    user:     str | None = None
    pswd:     str | None = None
    table:    str | None = None
    rdir:     str | None = None
    sdir:     str | None = None

class QAFlowCitationLogRestoreRequest(BaseModel):
    knowledge_requestid: str=Field(default_factory=lambda: str(uuid.uuid4()))
    restore_config:      RestoreConfig | None = None


"""
    Data Import/Export Configuration
"""
class IOConfig(BaseModel):
    format:           str | None = None
    location:         str | None = None
    name:             str | None = None
    host:             str | None = None
    port:             str | None = None
    user:             str | None = None
    pswd:             str | None = None
    table:            str | None = None
    rdir:             str | None = None
    sdir:             str | None = None
    file_rdir:        str | None = None
    file_sdir:        str | None = None
    file_name:        str | None = None

class QAFlowCitationLogImportRequest(BaseModel):
    knowledge_requestid: str=Field(default_factory=lambda: str(uuid.uuid4()))
    io_config:           IOConfig | None = None
    backup:              bool=True

class QAFlowCitationLogExportRequest(BaseModel):
    knowledge_requestid: str=Field(default_factory=lambda: str(uuid.uuid4()))
    data_filter:    QAFlowCitationLogFilter | None = None
    io_config:           IOConfig | None = None
    include_datetime:    bool = True


"""" QAFlowCitationLogServiceManager """
"""
    Request and Response for User Access Permitted QAFlowCitationLogs
"""
# User-level Access
class QAFlowCitationLog(BaseModel):
    # Trace Information
    qaflow_citation_id: str | None = None
    
    # Citation Content Information
    data_id:            str | None = None
    data_version:       int | None = None
    data_status:        int | None = None

    # Category Information
    data_type:          str | None = None
    content_type:       str | None = None    

    # PIL Information
    library_name_en:    str | None = None
    library_name_tc:    str | None = None # Missing Now
    category_name_en:   str | None = None
    category_name_tc:   str | None = None
    title_name_en:      str | None = None  
    title_name_tc:      str | None = None
    document_id:        str | None = None
    file_name:          str | None = None
    library_id:         str | None = None # Missing
    category_id:        str | None = None # Missing

    # Specification
    raw_data:           str | None = None
    
    coord_x1:           float | None = None
    coord_x2:           float | None = None
    coord_y1:           float | None = None
    coord_y2:           float | None = None

    page_start:         int | None = None
    page_end:           int | None = None
    line_start:         int | None = None
    line_end:           int | None = None
    seq_no:             int | None = None

    # Dependent
    knowledge_id:       str | None = None
    node_id:            str | None = None
    node_type:          str | None = None

    # Tags
    data_languages:     list[str] | None = None
    data_keywords:      list[str] | None = None
    data_tags:          list[str] | None = None

    # Retrieval Score
    score:              float | None = None

    # Time Information
    created_at:         datetime | None = None
    updated_at:         datetime | None = None

class UserQAFlowCitationLogRequest(BaseModel):
    knowledge_requestid: str=Field(default_factory=lambda: str(uuid.uuid4()))
    data_filter:         QAFlowCitationLogFilter

class UserQAFlowCitationLogResponse(BaseModel):
    knowledge_requestid: str
    filtered_data:       list[QAFlowCitationLog]=[]