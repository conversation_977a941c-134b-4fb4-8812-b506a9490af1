from fastapi import APIRouter, status

from ..settings import SETTINGS
from ..utils import router_response_handler

from ..services.retrieval_service import RetrievalServiceManager

from ..schemas.retrieval import (
        KnowledgeRetrievalRequest,
        KnowledgeRetrievalResponse,
        RawKnowledgeRetrievalResponse,
        CitationRetrievalRequest,
        CitationRetrievalResponse,
        RetrievalTestRequest,
        RetrievalTestResponse
    )

router = APIRouter(tags=["Chatflow"])

# API DB Session
if SETTINGS.BASE.APP_API == True:
    default_api_call = True
else:
    default_api_call = False

@router.post("/chatflow/knowledge_retrieval", status_code=status.HTTP_200_OK, response_model=RawKnowledgeRetrievalResponse)
def chatflow_knowledge_retrieval(request: KnowledgeRetrievalRequest, api_call: bool = default_api_call) -> RawKnowledgeRetrievalResponse:
    request = KnowledgeRetrievalRequest(**request.__dict__)
    response_data, response = RetrievalServiceManager(api_call=api_call).knowledge_retrieval(request=request)
    router_response_handler(response=response, api_call=api_call)
    return response_data

@router.post("/request/retrieval_test", status_code=status.HTTP_200_OK, response_model=RetrievalTestResponse)
def request_retrieval_test(request: RetrievalTestRequest, api_call: bool = default_api_call) -> RetrievalTestResponse:
    request = RetrievalTestRequest(**request.__dict__)
    response_data, response = RetrievalServiceManager(api_call=api_call).retrieval_test(request=request)
    router_response_handler(response=response, api_call=api_call)
    return response_data

@router.post("/request/citation_retrieval", include_in_schema=not(SETTINGS.BASE.APP_PRODUCTION), status_code=status.HTTP_200_OK, response_model=CitationRetrievalResponse)
def request_raw_citation_retrieval(request: CitationRetrievalRequest, api_call: bool = default_api_call) -> CitationRetrievalResponse:
    request = CitationRetrievalRequest(**request.__dict__)
    response_data, response = RetrievalServiceManager(api_call=api_call).citaiton_retrieval(request=request)
    router_response_handler(response=response, api_call=api_call)
    return response_data



