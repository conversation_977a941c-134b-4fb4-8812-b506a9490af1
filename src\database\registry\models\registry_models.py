import uuid
from datetime import datetime, timezone

from ....settings import SETTINGS

if SETTINGS.DATB.FORM.upper() in ["MODB"]:
    from mongoengine import *

    # class KnowledgeDB(DynamicDocument):
    #     meta = {
    #         'collection': SETTINGS.DATB.REGISTRY_TABLE
    #     }

    #     def to_dict(self):
    #         return {**self.to_mongo().to_dict()}

    # class KnowledgeDB(Document):

    #     # Trace Information
    #     knowledge_id                = StringField(default=lambda: str(uuid.uuid4()), unique=True)
    #     knowledge_traceid           = StringField(default=lambda: str(uuid.uuid4()))
    #     knowledge_name              = StringField(default='')
    #     knowledge_version           = IntField(default=1)   # 1=original    
    #     knowledge_ingestion_stage   = StringField(default="Ingested for Review")
    #     # knowledge_ingestion_stage   = StringField(choices=[
    #     #                                     "Pending to Ingest", 
    #     #                                     "Ingesting", 
    #     #                                     "Ingested for Review", 
    #     #                                     "Initialize Q&A Dataset", 
    #     #                                     "Reviewing Q&A Dataset",
    #     #                                     "Pending to Evaluate",
    #     #                                     "Evaluating",
    #     #                                     "Evaluated for Review",
    #     #                                     "Onboarding",
    #     #                                     "Onboard"
    #     #                                 ], default="Pending to Ingest")
    #     knowledge_ingestion_reason  = StringField(default='')
    #     knowledge_processing_stage  = StringField(default='')
    #     # knowledge_processing_stage  = StringField(choices=[
    #     #                                     "Metadata Registered",
    #     #                                     "KnowledgeInput Registered",
    #     #                                     "Preprocessing Completed",
    #     #                                     "Vector Registered",
    #     #                                     "Metadata Updated",
    #     #                                     # "Graph Registered",
    #     #                                     ], default="Metadata Registered")
    #     knowledge_processing_reason = StringField(default='') # Failed reason
    #     batch_order                 = StringField(default='') # single if not batch, otherwise timestamp

    #     # PIL Information
    #     library_name_en             = StringField(default='')
    #     library_name_tc             = StringField(default='')
    #     category_name_en            = StringField(default='')
    #     category_name_tc            = StringField(default='')
    #     title_name_en               = StringField(default='')
    #     title_name_tc               = StringField(default='')
    #     item_type                   = StringField(default='')
    #     item_url                    = StringField(default='')
    #     item_status                 = StringField(default='')
    #     document_id                 = StringField(default='')
    #     file_name                   = StringField(default='')
    #     file_description            = StringField(default='')
    #     file_created_datetime       = DateTimeField(default=datetime.now(timezone.utc))
    #     file_last_modified_datetime = DateTimeField(default=datetime.now(timezone.utc))
    #     group_id                    = ListField(default=[])
    #     file_sync_up_url            = StringField(default='')
    #     library_id                  = StringField(default='')
    #     category_id                 = StringField(default='')
    #     reference_start_date        = DateTimeField(default=datetime.now(timezone.utc))
    #     reference_end_date          = DateTimeField(default=datetime.strptime("2079-12-31", "%Y-%m-%d"))

    #     updated_by                  = StringField(default='')
    #     retention_at                = DateTimeField(default=None)
    #     pil_active_status           = BooleanField(default=False)

    #     # Creator Information
    #     creator_id                  = StringField(default='')
    #     creator_name                = StringField(default='')
    #     approver_id                 = StringField(default='admin')
    #     approver_name               = StringField(default='admin')
        
    #     # Category Information
    #     knowledge_group             = StringField(default='default')
    #     knowledge_type              = StringField(default='default')
    #     knowledge_location          = StringField(default='default')
    #     storage_type                = StringField(default='')
    #     storage_type_origin         = StringField(default='')
    #     storage_provider            = StringField(default='')
    #     storage_provider_origin     = StringField(default='')
    #     storage_directory           = StringField(default='')
    #     storage_directory_origin    = StringField(default='')
    #     storage_secrets             = DictField(default=dict())
    #     storage_secrets_origin      = DictField(default=dict())

    #     # Control Information
    #     knowledge_status            = IntField(default=1)
    #     knowledge_permission        = IntField(default=1)   # 0=default, related to user permission
    #     knowledge_management        = IntField(default=10)  # relate to management permission

    #     # Configuration
    #     knowledge_vectorstorage     = StringField(default='')
    #     knowledge_vectorlocation    = StringField(default='')
    #     knowledge_vectorinfo        = DictField(default=dict())
    #     knowledge_graphstorage      = StringField(default='')
    #     knowledge_graphlocation     = StringField(default='')
    #     knowledge_graphinfo         = DictField(default=dict())
    #     knowledge_searchstorage     = DictField(default=dict())
    #     knowledge_searchinfo        = DictField(default=dict())
    #     knowledge_secrets           = DictField(default=dict())
    #     knowledge_record            = BooleanField(default=False)
    #     knowledge_key               = StringField(default='')

    #     # Specification
    #     knowledge_confidence        = FloatField(default=-1.0)
    #     knowledge_filename          = StringField(default='')
    #     knowledge_fileextension     = StringField(default='')
    #     knowledge_filesize          = FloatField(default=0.0)
    #     knowledge_description       = StringField(default='')

    #     # Dependices
    #     knowledgeinput_id           = StringField(default='')
    #     prepknow_id                 = StringField(default='DEFAULT')

    #     # Statistics
    #     processing_time             = FloatField(default=0.0)
    #     total_input_tokens          = IntField(default=0)
    #     total_output_tokens         = IntField(default=0)
    #     total_tool_tokens           = IntField(default=0)

    #     # Tags
    #     knowledge_keywords          = ListField(default=[])
    #     knowledge_searchstorages    = ListField(default=[])
    #     knowledge_sources           = ListField(default=[])
    #     knowledge_sourcetypes       = ListField(default=[])
    #     knowledge_contenttypes      = ListField(default=[])
    #     knowledge_cats              = ListField(default=[])
    #     knowledge_languages         = ListField(default=[])
    #     knowledge_tags              = ListField(default=[])
    #     user_groups                 = ListField(default=[])
    #     agent_groups                = ListField(default=[])

    #     # Evaluation Results
    #     average_groundedness        = FloatField(default=0.0)
    #     average_relevance           = FloatField(default=0.0)
    #     average_retrieval           = FloatField(default=0.0)
    #     average_similarity          = FloatField(default=0.0)

    #     # Time Information
    #     knowledge_issue_date        = DateTimeField(default=datetime.now(timezone.utc))
    #     knowledge_effective_from    = DateTimeField(default=datetime.now(timezone.utc))
    #     knowledge_effective_to      = DateTimeField(default=datetime.strptime("2079-12-31", "%Y-%m-%d"))

    #     created_at                  = DateTimeField(default=datetime.now(timezone.utc))
    #     updated_at                  = DateTimeField(default=datetime.now(timezone.utc))

    #     meta = {
    #         'collection': SETTINGS.DATB.REGISTRY_TABLE, 
    #         'indexes': [
    #             'knowledge_id',
    #             'knowledge_traceid',
    #             'knowledge_name',
    #             'knowledge_version',
    #             'knowledge_ingestion_stage',
    #             'knowledge_ingestion_reason',
    #             'knowledge_processing_stage',
    #             'knowledge_processing_reason',
    #             'batch_order',
    #             'library_name_en',
    #             'library_name_tc',
    #             'category_name_en',
    #             'category_name_tc',
    #             'title_name_en',
    #             'title_name_tc',
    #             'item_type',
    #             'item_url',
    #             'item_status',
    #             'document_id',
    #             'file_name',
    #             'file_description',
    #             'file_created_datetime',
    #             'file_last_modified_datetime',
    #             'group_id',
    #             'file_sync_up_url',
    #             'library_id',
    #             'category_id',
    #             'reference_start_date',
    #             'reference_end_date',
    #             'creator_id',
    #             'creator_name',
    #             'approver_id',
    #             'approver_name',
    #             'updated_by',
    #             'retention_at',
    #             'pil_active_status',
    #             'knowledge_group',
    #             'knowledge_type',
    #             'knowledge_location',
    #             'storage_type',
    #             'storage_provider',
    #             'storage_directory',
    #             'knowledge_status',
    #             'knowledge_permission',
    #             'knowledge_management',
    #             'knowledge_vectorstorage',
    #             'knowledge_vectorlocation',
    #             'knowledge_confidence',
    #             'knowledge_filename',
    #             'knowledge_fileextension',
    #             'knowledge_filesize',
    #             'knowledge_description',
    #             'knowledgeinput_id',
    #             'prepknow_id',
    #             'processing_time',
    #             'total_input_tokens',
    #             'total_output_tokens',
    #             'total_tool_tokens',
    #             'knowledge_issue_date',
    #             'knowledge_effective_from',
    #             'knowledge_effective_to',
    #             'created_at',
    #             'updated_at'
    #         ]
    #     }

    #     def to_dict(self):
    #         return {**self.to_mongo().to_dict()}

    class KnowledgeDB(Document):

        # Trace Information
        knowledge_id                = StringField(default=lambda: str(uuid.uuid4()), unique=True)
        knowledge_traceid           = StringField(default=lambda: str(uuid.uuid4()))
        knowledge_name              = StringField(default='')
        knowledge_version           = IntField(default=1)   # 1=original    
        knowledge_ingestion_stage   = StringField(default="Ingested for Review")
        # knowledge_ingestion_stage   = StringField(choices=[
        #                                     "Pending to Ingest", 
        #                                     "Ingesting", 
        #                                     "Ingestion Failed",
        #                                     "Ingested for Review", 
        #                                     "Initialize Q&A Dataset", 
        #                                     "Reviewing Q&A Dataset",
        #                                     "Pending to Evaluate",
        #                                     "Evaluating",
        #                                     "Evaluated for Review",
        #                                     "Onboarding",
        #                                     "Onboard"
        #                                 ], default="Pending to Ingest")
        knowledge_ingestion_reason  = StringField(default='')
        knowledge_processing_stage  = StringField(default='')
        # knowledge_processing_stage  = StringField(choices=[
        #                                     "Metadata Registered",
        #                                     "KnowledgeInput Registered",
        #                                     "Preprocessing Completed",
        #                                     "Vector Registered",
        #                                     "Graph Registered",
        #                                     "Ingest Done",
        #                                     ], default="Metadata Registered")
        knowledge_processing_reason = StringField(default='') # Failed reason
        batch_order                 = StringField(default='') # single if not batch, otherwise timestamp

        # PIL Information
        library_name_en             = StringField(default='')
        library_name_tc             = StringField(default='')
        category_name_en            = StringField(default='')
        category_name_tc            = StringField(default='')
        title_name_en               = StringField(default='')
        title_name_tc               = StringField(default='')
        item_type                   = StringField(default='')
        item_url                    = StringField(default='')
        item_status                 = StringField(default='')
        document_id                 = StringField(default='')
        file_name                   = StringField(default='')
        file_description            = StringField(default='')
        file_created_datetime       = DateTimeField(default=datetime.now(timezone.utc))
        file_last_modified_datetime = DateTimeField(default=datetime.now(timezone.utc))
        group_id                    = ListField(default=[])
        file_sync_up_url            = StringField(default='')
        library_id                  = StringField(default='')
        category_id                 = StringField(default='')
        reference_start_date        = DateTimeField(default=datetime.now(timezone.utc))
        reference_end_date          = DateTimeField(default=datetime.strptime("2079-12-31", "%Y-%m-%d"))

        updated_by                  = StringField(default='')
        retention_at                = DateTimeField(default=None)
        pil_active_status           = BooleanField(default=True)

        # Creator Information
        creator_id                  = StringField(default='')
        creator_name                = StringField(default='')
        approver_id                 = StringField(default='admin')
        approver_name               = StringField(default='admin')
        
        # Category Information
        knowledge_group             = StringField(default='default')
        knowledge_type              = StringField(default='default')
        knowledge_location          = StringField(default='default')
        storage_type                = StringField(default='')
        storage_type_origin         = StringField(default='')
        storage_provider            = StringField(default='')
        storage_provider_origin     = StringField(default='')
        storage_directory           = StringField(default='')
        storage_directory_origin    = StringField(default='')
        storage_secrets             = DictField(default=dict())
        storage_secrets_origin      = DictField(default=dict())

        # Control Information
        knowledge_status            = IntField(default=1)
        knowledge_permission        = IntField(default=1)   # 0=default, related to user permission
        knowledge_management        = IntField(default=10)  # relate to management permission

        # Configuration
        knowledge_vectorstorage     = StringField(default='')
        knowledge_vectorlocation    = StringField(default='')
        knowledge_vectorinfo        = DictField(default=dict())
        knowledge_graphstorage      = StringField(default='')
        knowledge_graphlocation     = StringField(default='')
        knowledge_graphinfo         = DictField(default=dict())
        knowledge_searchstorage     = DictField(default=dict())
        knowledge_searchinfo        = DictField(default=dict())
        knowledge_secrets           = DictField(default=dict())
        knowledge_record            = BooleanField(default=False)
        knowledge_key               = StringField(default='')

        # Specification
        knowledge_confidence        = FloatField(default=-1.0)
        knowledge_filename          = StringField(default='')
        knowledge_fileextension     = StringField(default='')
        knowledge_filesize          = FloatField(default=0.0)
        knowledge_description       = StringField(default='')

        # Dependices
        knowledgeinput_id           = StringField(default='')
        prepknow_id                 = StringField(default='DEFAULT')

        # Statistics
        processing_time             = FloatField(default=0.0)
        total_input_tokens          = IntField(default=0)
        total_output_tokens         = IntField(default=0)
        total_tool_tokens           = IntField(default=0)

        # Tags
        knowledge_keywords          = ListField(default=[])
        knowledge_searchstorages    = ListField(default=[])
        knowledge_sources           = ListField(default=[])
        knowledge_sourcetypes       = ListField(default=[])
        knowledge_contenttypes      = ListField(default=[])
        knowledge_cats              = ListField(default=[])
        knowledge_languages         = ListField(default=[])
        knowledge_tags              = ListField(default=[])
        user_groups                 = ListField(default=[])
        agent_groups                = ListField(default=[])

        # Evaluation Results
        average_groundedness        = FloatField(default=0.0)
        average_relevance           = FloatField(default=0.0)
        average_retrieval           = FloatField(default=0.0)
        average_similarity          = FloatField(default=0.0)

        # Time Information
        knowledge_issue_date        = DateTimeField(default=datetime.now(timezone.utc))
        knowledge_effective_from    = DateTimeField(default=datetime.now(timezone.utc))
        knowledge_effective_to      = DateTimeField(default=datetime.strptime("2079-12-31", "%Y-%m-%d"))

        created_at                  = DateTimeField(default=datetime.now(timezone.utc))
        updated_at                  = DateTimeField(default=datetime.now(timezone.utc))

        meta = {
            'collection': SETTINGS.DATB.REGISTRY_TABLE, 
            'indexes': [
                'knowledge_id',
                'knowledge_name',
                'knowledge_version',
                'knowledge_ingestion_stage',
                'knowledge_processing_stage',
                'batch_order',
                'library_name_en',
                'library_name_tc',
                'category_name_en',
                'category_name_tc',
                'title_name_en',
                'title_name_tc',
                'item_type',
                'item_status',
                'document_id',
                'file_name',
                'file_created_datetime',
                'file_last_modified_datetime',
                'group_id',
                'library_id',
                'category_id',
                'reference_start_date',
                'reference_end_date',
                'updated_by',
                'retention_at',
                'pil_active_status',
                'knowledge_group',
                'knowledge_type',
                'knowledge_status',
                'knowledge_filename',
                'knowledge_fileextension',
                'knowledge_filesize',
                'knowledge_issue_date',
                'knowledge_effective_from',
                'knowledge_effective_to',
                'created_at',
                'updated_at'
            ]
        }

        def to_dict(self):
            return {**self.to_mongo().to_dict()}
    """
        QAFlow Citation Log
        - This log is used to track the citation information of the chunks used in the QAFlow process.
    """
    class QAFlowCitationLog(Document):
        # Trace Information
        qaflow_citation_id = StringField(default=lambda: str(uuid.uuid4()), unique=True)

        # Citation Content Information
        data_id            = StringField(default="") # Chunk ID
        data_version       = IntField(default=1)     # Chunk Version Number
        data_status        = IntField(default=1)

        # Category Information
        data_type          = StringField(default="") # TEXT, IMAGE, TABLE, DOCUMENT, etc.
        content_type       = StringField(default="") # default, image-to-text, OCR

        # PIL Information
        library_name_en    = StringField(default='')
        library_name_tc    = StringField(default='')
        category_name_en   = StringField(default='')
        category_name_tc   = StringField(default='')
        title_name_en      = StringField(default='')
        title_name_tc      = StringField(default='')
        document_id        = StringField(default='')
        file_name          = StringField(default='')
        library_id         = StringField(default='')
        category_id        = StringField(default='')

        # Specification
        raw_data           = StringField(default="") # Raw Chunk Content in String

        coord_x1           = FloatField(default=-1.0)
        coord_x2           = FloatField(default=-1.0)
        coord_y1           = FloatField(default=-1.0)
        coord_y2           = FloatField(default=-1.0)

        page_start         = IntField(default=-1)
        page_end           = IntField(default=-1)
        line_start         = IntField(default=-1)
        line_end           = IntField(default=-1)
        seq_no             = IntField(default=-1)

        # Dependent
        knowledge_id       = StringField(default="")
        node_id            = StringField(default="") # Node ID in Graph
        node_type          = StringField(default="")

        # Tags
        data_languages     = ListField(default=[])
        data_keywords      = ListField(default=[])
        data_tags          = ListField(default=[])

        # Retrieval Score
        score              = FloatField(default=-1.0)

        # Time Information
        created_at         = DateTimeField(default=datetime.now(timezone.utc))
        updated_at         = DateTimeField(default=datetime.now(timezone.utc))

        meta = {
            'collection': SETTINGS.DATB.QACITATION_TABLE, 
            'shard_key': ('knowledge_id'),
            'indexes': [
                'qaflow_citation_id',
                'data_id',
                'data_version',
                'data_type',
                'content_type',
                'library_name_en',
                'library_name_tc',    
                'category_name_en',   
                'category_name_tc',   
                'title_name_en',      
                'title_name_tc',      
                'document_id',        
                'file_name',          
                'library_id',         
                'category_id',
                'page_start',
                'page_end',
                'seq_no',
                'knowledge_id',
                'score',
                'created_at',
                'updated_at',
            ]
        }

        def to_dict(self):
            return {**self.to_mongo().to_dict()}



    class KeywordMapping(Document):
        mapping_id = StringField(default=lambda: str(uuid.uuid4()), unique=True)
        keywords = ListField(StringField(), required=True)  # List of keywords with same meaning
        ranking  = IntField(default=None)  # 1 for primary, 2 for secondary
        mapping_status = IntField(default=1)  # 1 for active, 0 for inactive
        mapping_version = IntField(default=1)  # Starts at 1, increments on updates
        created_at = DateTimeField(default=datetime.now(timezone.utc))
        updated_at = DateTimeField(default=datetime.now(timezone.utc))

        meta = {
            'collection': SETTINGS.DATB.KM_TABLE,
            'indexes': [
                'mapping_id',  # Primary index for lookups
                'keywords'     # Index for querying by keywords
            ]
        }

        def to_dict(self):
            return {**self.to_mongo().to_dict()}
else:
    err_msg = f"Unknown DB Schema Error : <{SETTINGS.DATB.NAME}> Database"
    raise Exception(err_msg)