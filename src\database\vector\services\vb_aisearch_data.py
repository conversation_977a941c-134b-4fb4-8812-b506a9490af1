import os
from datetime import datetime, timezone
import uuid
import inspect
from datetime import datetime, timezone
import time
import httpx

from typing import Generator

import json
import csv
from azure.core.exceptions import ResourceNotFoundError
from ....settings import SETTINGS

from ..schemas.format import (
    ResponseFormatter,
    Response,
    ComplexEncoder
)

from azure.search.documents import SearchClient

from ..schemas.vector import(
    VectorCreate,
    VectorCreateRequest, 
    VectorBatchCreateRequest,
    VectorUpdateRequest, 
    VectorRequest,
    VectorBatchRequest,
    Vector, 
    SecretVector,
    VectorFilter,
    UserVectorRequest,
    UserVectorResponse,
    SystemVectorRequest,
    SystemVectorResponse,
    BackupConfig,
    RestoreConfig,
    VectorBackupRequest,
    VectorBackupListRequest,
    VectorBackupListResponse,
    VectorRestoreRequest,
    IOConfig,
    VectorImportRequest,
    VectorExportRequest,
)

from ..schemas.utils import (
    PrepMediaPipelineRequest,
    PrepMediaPipelineResponse,
    KnowDataObject
)

from ....database.vector.connections.vector_connection import get_vb_func, get_vb_api

# API DB Session
if SETTINGS.BASE.APP_API == True:
    vb_api = get_vb_api()
    default_api_call = True
else:
    vb_api = None
    default_api_call = False

# Function DB Session
if SETTINGS.BASE.APP_FUNC == True:
    vb_func = get_vb_func
else:
    vb_func = None

from ....logger.log_handler import get_logger

logger = get_logger(__name__)


class AISearchDataManager:
    response_format = ResponseFormatter(suffix = f"{inspect.stack()[0][3]}")

    # default_backup_config = BackupConfig(
    #     format=SETTINGS.BKUP.FORM,
    #     location=SETTINGS.BKUP.LOCA,
    #     name=SETTINGS.BKUP.NAME,
    #     host=SETTINGS.BKUP.HOST,
    #     port=SETTINGS.BKUP.PORT,
    #     user=SETTINGS.BKUP.USER,
    #     pswd=SETTINGS.BKUP.PSWD,
    #     table=SETTINGS.BKUP.TABLE,
    #     rdir=SETTINGS.BKUP.RDIR,
    #     sdir=SETTINGS.BKUP.SDIR,
    #     limit=SETTINGS.BKUP.LIMIT
    # )

    default_export_config = IOConfig(
        format=SETTINGS.EXPT.FORM,
        location=SETTINGS.EXPT.LOCA,
        name=SETTINGS.EXPT.NAME,
        host=SETTINGS.EXPT.HOST,
        port=SETTINGS.EXPT.PORT,
        user=SETTINGS.EXPT.USER,
        pswd=SETTINGS.EXPT.PSWD,
        table=SETTINGS.EXPT.TABLE,
        rdir=SETTINGS.EXPT.RDIR,
        sdir=SETTINGS.EXPT.SDIR,
        file_rdir=SETTINGS.EXPT.FILE_RDIR,
        file_sdir=SETTINGS.EXPT.FILE_SDIR,
        file_name=SETTINGS.EXPT.FILE_NAME
    )

    def __init__(
            self, 
            api_call:        bool | None = default_api_call,
            vb_api:          SearchClient | None = vb_api, 
            vb_func:         Generator    | None = vb_func, 
            vector_storage:  str='AISEARCH', 
            vector_location: str='azure', 
            vector_config:   dict={},
        ):
        self.api_call        = api_call
        self.vb_api          = vb_api
        self.vb_func         = vb_func
        self.vector_storage  = vector_storage
        self.vector_location = vector_location
        self.vector_config   = vector_config

    """
        Data-level General Operation
    """
    # Create
    def create(
            self, 
            request: VectorCreateRequest
        ) -> Response:
        
        # # Validate parameters with creator role
        # response = self.verify_vector_content(is_admin=request.is_admin, data=request.data)
        # if response:
        #     return response

        data = request.data        
        try:
            entities = [{key: value  for key, value in data.__dict__.items()}]
            if self.api_call == True:
                self.vb_api.upload_documents(
                    documents = entities
                )
            else:
                with self.vb_func() as vb:
                    vb.upload_documents(
                    documents = entities
                )
            response = Response(status_code=201, detail=self.response_format.ok(f"Success : Registered Vector <data_id: {data.data_id}>"))
            logger.info(response.detail)

        # Handle common exceptions that might occur
        except (BaseException, Exception) as e:
            response = Response(status_code=500, detail=self.response_format.error(f"Common Error : Registering Vector <data_id: {data.data_id}>", str(e)))
            logger.error(response.detail)

        # Handle any other exceptions that might occur
        except:
            response = Response(status_code=500, detail=self.response_format.error(f"Unexpected Error : Registering Vector <data_id: {data.data_id}>"))
            logger.error(response.detail)

        return response

    # Batch Create
    def batch_create(
            self, 
            request: VectorBatchCreateRequest
        ) -> Response:
        
        _data_list = []
        admin_list = []

        for _request in request.create_requests:
            if isinstance(_request.data, VectorCreate):
                _data_list.append(_request.data)
                admin_list.append(_request.is_admin)
            elif isinstance(_request.data, dict):
                _data_list.append(VectorCreate(**_request.data))
                admin_list.append(_request.is_admin)
            else:
                logger.error(f"Invalid Data Type for <{_request.data}>")

        # Validate parameters with creator role
        # for is_admin, data in zip(admin_list, _data_list):
        #     response = self.verify_vector_content(is_admin=is_admin, data=data)
        #     if response:
        #         return response

        vb_data_batch = [_data for _data in _data_list]
        if not vb_data_batch:
            response = Response(status_code=404, detail=self.response_format.error("Unfound Data Batch for Creation"))
            return response

        entities = [{key: value  for key, value in data.__dict__.items()} for data in vb_data_batch]
        with self.vb_func() as vb:
            vb.upload_documents(
            documents = entities
        )
        try:
            entities = [{key: value  for key, value in data.__dict__.items()} for data in vb_data_batch]
            if self.api_call == True:
                self.vb_api.upload_documents(
                    documents = entities
                )
            else:
                with self.vb_func() as vb:
                    vb.upload_documents(
                    documents = entities
                )
            response = Response(status_code=201, detail=self.response_format.ok(f"Success : Batch Registered All <{len(request.create_requests)}> Vectors"))
            logger.info(response.detail)

        # Handle common exceptions that might occur
        except (BaseException, Exception) as e:
            response = Response(status_code=500, detail=self.response_format.error(f"Common Error : Batch Registering Vector", str(e)))
            logger.error(response.detail)

        # Handle any other exceptions that might occur
        except:
            response = Response(status_code=500, detail=self.response_format.error(f"Unexpected Error : Batch Registering Vector"))
            logger.error(response.detail)
        
        return response
    
    def update(
            self, 
            request: VectorUpdateRequest
        ) -> Response:
        
        # Retrieve data from database
        db_data = self.get_data_by_data_id(
            id     = request.data_id,
            active = False
        )

        # Check if data exists in database
        if not db_data:
            response = Response(status_code=404, detail=self.response_format.error(f"Unfound Error : Updating Vector Data <data_id: {request.data_id}>"))
            logger.error(response.detail)
            return response

        if request.update_data.raw_data and not request.update_data.processed_data:
            if request.prepmedia_config.get("text", None):
                _prepmedia_config = request.prepmedia_config.get("text", None)
            else:
                _prepmedia_config = SETTINGS.VTDB.PREPMEIDA_CONFIG.get("text", None)

            if not _prepmedia_config:
                response = Response(status_code=404, detail=self.response_format.error(f"Unfound Error : Invalid PrepMedia Config for Updating Raw Content"))
                logger.error(response.detail)
                return response
            
            knowdataobject = KnowDataObject(raw_data=request.update_data.raw_data)
            prepmedia_request = PrepMediaPipelineRequest(
                prepmedia_config = _prepmedia_config,
                prepmedia_input  = [knowdataobject]
            )
            response_data, response = self.preprocess_text(request=prepmedia_request)
            if response.status_code >= SETTINGS.STAT.SUCC_CODE_END:
                return response
            
            # Update Keywords if not Specified Keywords
            if not request.update_data.data_keywords:
                request.update_data.data_keywords = response_data.prepmedia_output[0].data_keywords

            request.update_data.processed_data = response_data.prepmedia_output[0].processed_data

        # Parse Update Data        
        update_data = {key: value for key, value in request.update_data.__dict__.items() if value is not None}

        if not update_data:
            response = Response(status_code=404, detail=self.response_format.error(f"Unfound Error : Invalid Update Vector Data"))
            logger.error(response.detail)
            return response

        new_data = db_data.copy()
        new_data.update(update_data)
        if "data_version" not in update_data.keys() and request.overwrite == False:
            new_data["data_version"] += 1
        new_data["updated_at"] = datetime.now(timezone.utc)

        old_data = db_data.copy()
        old_data["data_id"]     = str(uuid.uuid4())
        old_data["data_status"] = 0
        old_data["updated_at"]  = datetime.now(timezone.utc)
        
        if request.overwrite == False:
            update_db_data = [old_data, new_data]
        else:
            update_db_data = [new_data]
        
        try:
            if self.api_call == True:
                self.vb_api.merge_or_upload_documents(documents=update_db_data)
            else:
                with self.vb_func() as vb:
                    vb.merge_or_upload_documents(documents=update_db_data)       

            response = Response(status_code=200, detail=self.response_format.ok(f"Success : Updated Vector <data_id: {new_data['data_id']}>"))
            logger.info(response.detail)

        # Handle Unfound errors
        except ResourceNotFoundError as e:
            response = Response(status_code=404, detail=self.response_format.error(f"Unfound Error : Data are not Found in Vector DB <data_id: {new_data['data_id']}>", str(e)))
            logger.error(response.detail)

        # Handle common exceptions that might occur
        except (BaseException, Exception) as e:
            response = Response(status_code=500, detail=self.response_format.error(f"Common Error : Updating Data <data_id: {new_data['data_id']}>", str(e)))
            logger.error(response.detail)

        # Handle any other exceptions that might occur
        except:
            response = Response(status_code=500, detail=self.response_format.error(f"Unexpected Error : Updating  Data <data_id: {new_data['data_id']}"))
            logger.error(response.detail)

        return response

    # Activate
    def activate(
            self, 
            request: VectorRequest
        ) -> Response:
        try:
            db_data = self.get_data_by_data_id(
                id       = request.data_id,
                activate = False
            )

            if db_data:
                db_data["data_status"] = 1
                db_data["updated_at"]  = datetime.now(timezone.utc)

                if self.api_call == True:
                    self.vb_api.merge_or_upload_documents(documents=db_data)
                else:
                    with self.vb_func() as vb:
                        vb.merge_or_upload_documents(documents=db_data)      

                response = Response(status_code=200, detail=self.response_format.ok(f"Success : Activated Data <data_id: {request.data_id}>"))
                logger.info(response.detail)
                
            else:
                response = Response(status_code=404, detail=self.response_format.error(f"Unfound Error : Activating Data <data_id: {request.data_id}>"))
                logger.error(response.detail)

        # Handle Unfound errors
        except ResourceNotFoundError as e:
            response = Response(status_code=404, detail=self.response_format.error(f"Unfound Error : Data are not Found in Vector DB <data_id: {request.data_id}>", str(e)))
            logger.error(response.detail)

        # Handle common exceptions that might occur
        except (BaseException, Exception) as e:
            response = Response(status_code=500, detail=self.response_format.error(f"Common Error : Updating Data <data_id: {request.data_id}>", str(e)))
            logger.error(response.detail)

        # Handle any other exceptions that might occur
        except:
            response = Response(status_code=500, detail=self.response_format.error(f"Unexpected Error : Updating  Data <data_id: {request.data_id}"))
            logger.error(response.detail)

        return response

    # Batch Activate
    def batch_activate(
            self, 
            request: VectorBatchRequest
        ) -> Response:
        conditions = []
        try:
            for data in request.vector_requests:
                db_data = self.get_data_by_data_id(
                    id     = data.data_id,
                    active = False
                )

                # Check if data exists
                if db_data:
                    db_data["data_status"] = 1
                    db_data["updated_at"]  = datetime.now(timezone.utc)
                    conditions.append(db_data)
                
                else:
                    response = Response(status_code=404, detail=self.response_format.error(f"Unfound Error : Activating Vector <data_id: {data.data_id}>"))
                    logger.error(response.detail)
                    return response

            if not conditions:
                response = Response(status_code=404, detail=self.response_format.error("Unfound Erorr : Failed to Load Conditions for Batch Activation"))
                logger.error(response.detail)
                return response
            
            # Update status
            if self.api_call == True:
                self.vb_api.merge_or_upload_documents(documents=db_data)
            else:
                with self.vb_func() as vb:
                    vb.merge_or_upload_documents(documents=db_data)    

            response = Response(status_code=200, detail=self.response_format.ok(f"Success : Batch Activated <{len(request.vector_requests)}> Vectors"))
            logger.info(response.detail)

        # Handle common exceptions that might occur
        except (BaseException, Exception) as e:
            response = Response(status_code=500, detail=self.response_format.error(f"Common Error : Batch Activating Vector", str(e)))
            logger.error(response.detail)

        # Handle any other exceptions that might occur
        except:
            response = Response(status_code=500, detail=self.response_format.error(f"Unexpected Error : Batch Activating Vector"))
            logger.error(response.detail)

        return response


    # Deactivate
    def deactivate(
            self, 
            request: VectorRequest
        ) -> Response:
        try:
            db_data = self.get_data_by_data_id(
                id      = request.data_id, 
                active  = True
            )

            if db_data:
                db_data["data_status"] = 0
                db_data["updated_at"]  = datetime.now(timezone.utc)

                if self.api_call == True:
                    self.vb_api.merge_or_upload_documents(documents=db_data)
                else:
                    with self.vb_func() as vb:
                        vb.merge_or_upload_documents(documents=db_data)      

                response = Response(status_code=200, detail=self.response_format.ok(f"Success : Deactivated Vector <data_id: {request.data_id}>"))
                logger.info(response.detail)
            
            else:
                response = Response(status_code=404, detail=self.response_format.error(f"Unfound Error : Deactivating Vector <data_id: {request.data_id}>"))
                logger.error(response.detail)

        # Handle common exceptions that might occur
        except (BaseException, Exception) as e:
            response = Response(status_code=500, detail=self.response_format.error(f"Common Error : Deactivating Vector <data_id: {request.data_id}>", str(e)))
            logger.error(response.detail)

        # Handle any other exceptions that might occur
        except:
            response = Response(status_code=500, detail=self.response_format.error(f"Unexpected Error : Deactivating Vector <data_id: {request.data_id}>"))
            logger.error(response.detail)

        return response


    # Batch Deactivate
    def batch_deactivate(
            self, 
            request: VectorBatchRequest
        ) -> Response:
        conditions = []
        try:
            for data in request.vector_requests:
                db_data = self.get_data_by_data_id(
                    id     = data.data_id,
                    active = True
                )

                # Check if data exists
                if db_data:
                    db_data["data_status"] = 0
                    db_data["updated_at"]  = datetime.now(timezone.utc)
                    conditions.append(db_data)
                
                else:
                    response = Response(status_code=404, detail=self.response_format.error(f"Unfound Error : Deactivating Vector <data_id: {data.data_id}>"))
                    logger.error(response.detail)
                    return response

            if not conditions:
                response = Response(status_code=404, detail=self.response_format.error("Unfound Erorr : Failed to Load Conditions for Batch Deactivation"))
                logger.error(response.detail)
                return response
            
            # Update status
            if self.api_call == True:
                self.vb_api.merge_or_upload_documents(documents=db_data)
            else:
                with self.vb_func() as vb:
                    vb.merge_or_upload_documents(documents=db_data)    

            response = Response(status_code=200, detail=self.response_format.ok(f"Success : Batch Deactivated <{len(request.vector_requests)}> Vectors"))
            logger.info(response.detail)

        # Handle common exceptions that might occur
        except (BaseException, Exception) as e:
            response = Response(status_code=500, detail=self.response_format.error(f"Common Error : Batch Deactivating Vector", str(e)))
            logger.error(response.detail)

        # Handle any other exceptions that might occur
        except:
            response = Response(status_code=500, detail=self.response_format.error(f"Unexpected Error : Batch Deactivating Vector"))
            logger.error(response.detail)

        return response


    # Delete
    def delete(self, request: VectorRequest) -> Response:
        response = self.deactivate(request=request)
    
    # Batch Delete
    def batch_delete(self, request: VectorRequest) -> Response:
        response = self.batch_deactivate(request=request)
        return response

    # Drop
    def drop(self, request: VectorRequest) -> Response:
        try:
            db_data = self.get_data_by_data_id(
                id     = request.data_id,
                active = False
            )

            if db_data:
                conditions = [{"data_id": db_data["data_id"]}]
                if self.api_call:
                   self.vb_api.delete_documents(documents=conditions)
                else:
                    with self.vb_func() as vb:
                       vb.delete_documents(documents=conditions)

                response = Response(status_code=200, detail=self.response_format.ok(f"Success : Dropped Vector <data_id: {request.data_id}>"))
                logger.info(response.detail)
            
            else:
                response = Response(status_code=404, detail=self.response_format.error(f"Unfound Error : Dropping Vector <data_id: {request.data_id}>"))
                logger.error(response.detail)

        # Handle common exceptions that might occur
        except (BaseException, Exception) as e:
            response = Response(status_code=500, detail=self.response_format.error(f"Common Error : Dropping Vector <data_id: {request.data_id}>", str(e)))
            logger.error(response.detail)

        # Handle any other exceptions that might occur
        except:
            response = Response(status_code=500, detail=self.response_format.error(f"Unexpected Error : Dropping Vector <data_id: {request.data_id}>"))
            logger.error(response.detail)


    # Batch Drop
    def batch_drop(self, request: VectorBatchRequest) -> Response:
        conditions = []
        try:
            for data in request.vector_requests:
                db_data = self.get_data_by_data_id(
                    id      = data.data_id, 
                    active  = False
                )

                # Check if data exists
                if db_data:
                    conditions.append({"data_id": db_data["data_id"]})
                else:
                    response = Response(status_code=404, detail=self.response_format.error(f"Unfound Error : Dropping Vector <data_id: {data.data_id}>"))
                    logger.error(response.detail)
                    return response

            if not conditions:
                response = Response(status_code=404, detail=self.response_format.error(f"Unfound Error : Dropping Vector <data_id: {data.data_id}>"))
                logger.error(response.detail)
                return response

            # Delete data
            if self.api_call == True:
                self.vb_api.delete_documents(documents=conditions)
            else:
                with self.vb_func() as vb:
                    vb.delete_documents(documents=conditions)  

            response = Response(status_code=200, detail=self.response_format.ok(f"Success : Batch Dropped <{len(request.vector_requests)}> Vector"))
            logger.info(response.detail)

        # Handle common exceptions that might occur
        except (BaseException, Exception) as e:
            response = Response(status_code=500, detail=self.response_format.error(f"Common Error : Batch Dropping Vector", str(e)))
            logger.error(response.detail)

        # Handle any other exceptions that might occur
        except:
            response = Response(status_code=500, detail=self.response_format.error(f"Unexpected Error : Batch Dropping Vector"))
            logger.error(response.detail)

        return response


    """
        System Operation
    """
    def drop_inactive_by_system(self) -> Response:
        conditions = []
        filter_query = 'data_status eq 0'
        try:
            if self.api_call == True:
                results = self.vb_api.search(search_text="*", filter=filter_query)
            else:
                with self.vb_func() as vb:
                    results = vb.search(search_text="*", filter=filter_query)
        
            if results:
                conditions = [{"data_id": data_id} for data_id in list(results)]

            if self.api_call:
               self.vb_api.delete_documents(documents=conditions)
            else:
                with self.vb_func() as vb:
                   vb.delete_documents(documents=conditions)
        
        # Handle common exceptions that might occur
        except (BaseException, Exception) as e:
            response = Response(status_code=500, detail=self.response_format.error(f"Common Error : Dropping Inactive Vectors", str(e)))
            logger.error(response.detail)

        # Handle any other exceptions that might occur
        except:
            response = Response(status_code=500, detail=self.response_format.error(f"Unexpected Error : Dropping Inactive Vectors"))
            logger.error(response.detail)

        return response

    # System Query Data
    def query_data_by_system(self, request: SystemVectorRequest) -> tuple[SystemVectorResponse, Response]:
        response_data = SystemVectorResponse(**request.__dict__)

        if request.vector_filter:
            data_filter = VectorFilter(**request.vector_filter.__dict__)
        else:
            data_filter = VectorFilter()

        conditions, sorting, filter_no = self.filter_formatter(data_filter=data_filter)
        db_data, response              = self.query_with_conditions(conditions=conditions, sorting=sorting, filter_no=filter_no)
        
        if db_data:
            response_data.__dict__.update(filtered_vectors=db_data, vector_no=len(db_data))
            response = Response(status_code=200, detail=self.response_format.ok("Success : Get Filtered Data for System"))
       
        return response_data, response

    # System Export Data
    def export_data_by_system(self, request: VectorExportRequest) -> Response:

        # Get Data
        if request.vector_filter:
            data_filter = VectorFilter(**request.vector_filter.__dict__)
        else:
            data_filter = VectorFilter()

        conditions, sorting, filter_no = self.filter_formatter(data_filter=data_filter)
        db_data, response              = self.query_with_conditions(conditions=conditions, sorting=sorting, filter_no=filter_no)

        if db_data:
            filtered_data = [data.__dict__ for data in db_data]
            response = Response(status_code=200, detail=self.response_format.ok("Success : Get Filtered Data for System"))
        else:
            return response # Return No Data

        response = self.export_data(config=request.io_config, data=filtered_data, include_datetime=request.include_datetime)

        return response    

    """
        User Operation
    """
    def query_data_by_user(self, request: UserVectorRequest) -> tuple[UserVectorResponse, Response]:
        response_data = UserVectorResponse(**request.__dict__)

        data_filter = VectorFilter(**request.vector_filter.__dict__)
        conditions, sorting, filter_no = self.filter_formatter(data_filter=data_filter)

        if not conditions:
            response = Response(status_code=500, detail=self.response_format.error(f"Missing Filter Error : Filter cannot be All Empty"))
            logger.error(response.detail)
            return response_data, response

        else:
            db_data, response = self.query_with_conditions(conditions=conditions, sorting=sorting, filter_no=filter_no)

        if db_data:
            response_data.__dict__.update(filtered_vectors=[Vector(**data.__dict__) for data in db_data])
            response = Response(status_code=200, detail=self.response_format.ok("Success : Get Permitted Data for User"))
       
        return response_data, response

    """
        Class Operation
    """
    def get_data_by_data_id(self, id: str=None, active: bool=True) -> dict:
        if id:
            if self.api_call == True:
                db_data = self.vb_api.get_document(key=id)
            else:
                with self.vb_func() as vb:
                    db_data = vb.get_document(key=id)
        else:
            return {}
        
        return db_data

    # Perform Query
    def query_with_conditions(self, conditions: list, sorting: list, filter_no: int) -> tuple[list[SecretVector] | None, Response]:
        db_data = None
        filter_query = ''
        sort_query   = ''

        if not conditions:
            response = Response(status_code=500, detail=self.response_format.error("Condition Filter Error : Condition Filter is Empty"))
            logger.error(response.detail)
            return db_data, response
        else:
            filter_query = ' and '.join(conditions)

        if sorting:
            sort_query = ', '.join(sorting)

        try:
            if self.api_call == True:
                if not filter_no or filter_no < 0:
                    results = self.vb_api.search(
                        search_text="*", 
                        filter=filter_query,
                        order_by=sort_query
                    )

                else:
                    results = self.vb_api.search(
                        search_text="*", 
                        filter=filter_query,
                        order_by=sort_query,
                        top=filter_no
                    )
                
            else:
                with self.vb_func() as vb:
                    if not filter_no or filter_no < 0:
                        results = vb.search(
                            search_text="*", 
                            filter=filter_query,
                            order_by=sort_query
                        )

                    else:
                        results = vb.search(
                            search_text="*", 
                            filter=filter_query,
                            order_by=sort_query,
                            top=filter_no
                        )
                        
            if results:
                response = Response(status_code=200, detail=self.response_format.ok("Success : Retrieved Filtered Vectors from DB"))
                db_data = [SecretVector(**_result) for _result in list(results)]

            else:
                response = Response(status_code=200, detail=self.response_format.ok("Success : No Matched Vectors from DB"))

        # Handle common exceptions that might occur
        except (BaseException, Exception) as e:
            response = Response(status_code=500, detail=self.response_format.error(f"Common Error : Getting Vectors", str(e)))
            logger.error(response.detail)

        # Handle any other exceptions that might occur
        except:
            response = Response(status_code=500, detail=self.response_format.error(f"Unexpected Error : Getting Vectors"))
            logger.error(response.detail)

        return db_data, response

    # Filter Formatter
    def filter_formatter(self, data_filter: VectorFilter) -> tuple[list, list, int]:

        def filter_by_byte(column_name: str, entity_filter: list[str]) -> list:
            conditions = []

            try:
                pass
                # column = getattr(db_model, column_name)
            except AttributeError as e:
                response = Response(status_code=404, detail=self.response_format.error(f"Attribute Error : Unrecognized Attribute <{column_name}>. The Attribute for ByteFilter must be <key_filter>", str(e)))
                logger.error(response.detail)
                return conditions
            except Exception:
                response = Response(status_code=500, detail=self.response_format.error(f"Unexpected Error : Unexpected Error during Parsing Attribute <{column_name}> in ByteFilter"))
                logger.error(response.detail)       
                return conditions
            
            # _encoded_values = []
            # for value in entity_filter:
            #     encoded_value, response = CryptoServiceManager(api_call=False).encode_content(content=value)
            #     if response.status_code >= SETTINGS.STAT.SUCC_CODE_END:
            #         logger.error(response.detail)
            #         return conditions
            #     _encoded_values.append(encoded_value)

            # conditions = [Q(**{f"{column_name}__in": encoded_value})]
            
            return conditions

        conditions = []
        sorting    = []

        # Parsing conditions

        # String Filter
        if data_filter.string_filter is not None:
            string_suffix = '_filter'
            for key, value in data_filter.string_filter.__dict__.items():
                if value is not None:
                    column_name = key.split(string_suffix)[0]
                    try:
                        _conditions   = [f"{column_name} eq \'{_value}\'" for _value in value]
                        condition_str = " or ".join(_conditions)
                        conditions.append(condition_str)
                    except AttributeError as e:
                        response = Response(status_code=404, detail=self.response_format.error(f"Attribute Error : Unrecognized Attribute <{column_name}>. The Attribute for StringFilter must be <key_filter>", str(e)))
                        logger.error(response.detail)
                    except Exception:
                        response = Response(status_code=500, detail=self.response_format.error(f"Unexpected Error : Unexpected Error during Parsing Attribute <{column_name}> in StringFilter"))
                        logger.error(response.detail)                    


        # Numeric Filter
        if data_filter.numeric_filter is not None:
            min_suffix = '_min'
            max_suffix = '_max'
            for key, value in data_filter.numeric_filter.__dict__.items():
                if value is not None:
                    if min_suffix in key:
                        column_name = key.split(min_suffix)[0]
                        try:
                            conditions.append(f"{column_name} ge {value}")
                        except AttributeError as e:
                            response = Response(status_code=404, detail=self.response_format.error(f"Attribute Error : Unrecognized Attribute <{column_name}>. The Attribute for NumericFilter must be <key_min> or <key_max>", str(e)))
                            logger.error(response.detail)
                        except Exception:
                            response = Response(status_code=500, detail=self.response_format.error(f"Unexpected Error : Unexpected Error during Parsing Attribute <{column_name}> in NumericFilter"))
                            logger.error(response.detail)              

                    elif max_suffix in key:
                        column_name = key.split(max_suffix)[0]
                        try:
                            conditions.append(f"{column_name} le {value}")
                        except AttributeError as e:
                            response = Response(status_code=404, detail=self.response_format.error(f"Attribute Error : Unrecognized Attribute <{column_name}>. The Attribute for NumericFilter must be <key_min> or <key_max>", str(e)))
                            logger.error(response.detail)
                        except Exception:
                            response = Response(status_code=500, detail=self.response_format.error(f"Unexpected Error : Unexpected Error during Parsing Attribute <{column_name}> in NumericFilter"))
                            logger.error(response.detail)    

                    else:
                        response = Response(status_code=404, detail=self.response_format.error(f"Attribute Error : Unrecognized Attribute <{column_name}>. The Attribute Suffix for NumericFilter must be Either _min or _max"))
                        logger.error(response.detail)


        # List Filter
        if data_filter.list_filter is not None:
            or_suffix  = '_or'
            and_suffix = '_and'
            for key, value in data_filter.list_filter.__dict__.items():
                if value is not None:
                    if or_suffix in key:
                        column_name   = key.split(or_suffix)[0]
                        _conditions   = [f"c eq \'{_value}\'" for _value in value]
                        _nested_conditions = ' or '.join(_conditions)
                        condition_str = f"{column_name}/any(c: {_nested_conditions})"
                        conditions.append(condition_str)
                        
                    elif and_suffix in key:
                        column_name = key.split(and_suffix)[0]
                        _conditions   = [f"c eq \'{_value}\'" for _value in value]
                        _nested_conditions = ' and '.join(_conditions)
                        condition_str = f"{column_name}/aall(c: {_nested_conditions})"
                        conditions.append(condition_str)

                    else:
                        response = Response(status_code=404, detail=self.response_format.error(f"Attribute Error : Unrecognized Attribute <{column_name}>. The Attribute Suffix for ListFilter must be Either _or or _and"))
                        logger.error(response.detail)


        # Dictionary Filter
        if data_filter.dictionary_filter is not None:
            or_suffix  = '_or'
            and_suffix = '_and'
            for key, value in data_filter.dictionary_filter.__dict__.items():
                if value is not None:
                    if or_suffix in key:
                        column_name = key.split(or_suffix)[0]
                        try:
                            # _conditions = [Q(**{f"attribute.{_key}__exists": True}) for _key in value]
                            # if _conditions:
                            #     nested_conditions = _conditions[0]
                            #     for _condition in _conditions:
                            #         nested_conditions |= _condition
                            pass
                        except AttributeError as e:
                            response = Response(status_code=404, detail=self.response_format.error(f"Attribute Error : Unrecognized Attribute <{column_name}>. The Attribute for DictionaryFilter must be <key_or> or <key_and>", str(e)))
                            logger.error(response.detail)
                        except Exception:
                            response = Response(status_code=500, detail=self.response_format.error(f"Unexpected Error : Unexpected Error during Parsing Attribute <{column_name}> in DictionaryFilter"))
                            logger.error(response.detail)     

                    elif and_suffix in key:
                        column_name = key.split(and_suffix)[0]
                        try:
                            # _conditions = [Q(**{f"attribute.{_key}__exists": True}) for _key in value]
                            # if _conditions:
                            #     nested_conditions = _conditions[0]
                            #     for _condition in _conditions:
                            #         nested_conditions &= _condition
                            pass
                        except AttributeError as e:
                            response = Response(status_code=404, detail=self.response_format.error(f"Attribute Error : Unrecognized Attribute <{column_name}>. The Attribute for DictionaryFilter must be <key_or> or <key_and>", str(e)))
                            logger.error(response.detail)
                        except Exception:
                            response = Response(status_code=500, detail=self.response_format.error(f"Unexpected Error : Unexpected Error during Parsing Attribute <{column_name}> in DictionaryFilter"))
                            logger.error(response.detail)

                    else:
                        response = Response(status_code=404, detail=self.response_format.error(f"Attribute Error : Unrecognized Attribute <{column_name}>. The Attribute Suffix for ListFilter must be Either _or or _and"))
                        logger.error(response.detail)


        # Boolean Filter
        if data_filter.boolean_filter is not None:
            boolean_suffix = '_filter'
            
            for key, value in data_filter.boolean_filter.__dict__.items():
                if value is not None:
                    column_name = key.split(boolean_suffix)[0]
                    try:
                        conditions.append(f"{column_name} eq true")
                    except AttributeError as e:
                        response = Response(status_code=404, detail=self.response_format.error(f"Attribute Error : Unrecognized Attribute <{column_name}>. The Attribute for BooleanFilter must be <key_filter>", str(e)))
                        logger.error(response.detail)
                    except Exception:
                        response = Response(status_code=500, detail=self.response_format.error(f"Unexpected Error : Unexpected Error during Parsing Attribute <{column_name}> in BooleanFilter"))
                        logger.error(response.detail)    


        # Datetime Filter
        if data_filter.datetime_filter is not None:
            start_suffix = '_start'
            end_suffix   = '_end'
            
            for key, value in data_filter.datetime_filter.__dict__.items():
                if value is not None:
                    if start_suffix in key:
                        column_name = key.split(start_suffix)[0]
                        try:
                            conditions.append(f"{column_name} ge value")
                        except AttributeError as e:
                            response = Response(status_code=404, detail=self.response_format.error(f"Attribute Error : Unrecognized Attribute <{column_name}>. The Attribute for DatetimeFilter must be <key_start> or <key_end>", str(e)))
                            logger.error(response.detail)
                        except Exception:
                            response = Response(status_code=500, detail=self.response_format.error(f"Unexpected Error : Unexpected Error during Parsing Attribute <{column_name}> in DatetimeFilter"))
                            logger.error(response.detail)      

                    if end_suffix in key:
                        column_name = key.split(end_suffix)[0]
                        try:
                            conditions.append(f"{column_name} le value")
                        except AttributeError as e:
                            response = Response(status_code=404, detail=self.response_format.error(f"Attribute Error : Unrecognized Attribute <{column_name}>. The Attribute for DatetimeFilter must be <key_start> or <key_end>", str(e)))
                            logger.error(response.detail)
                        except Exception:
                            response = Response(status_code=500, detail=self.response_format.error(f"Unexpected Error : Unexpected Error during Parsing Attribute <{column_name}> in DatetimeFilter"))
                            logger.error(response.detail)    

                    else:
                        response = Response(status_code=404, detail=self.response_format.error(f"Attribute Error : Unrecognized Attribute <{column_name}>. The Attribute Suffix for DatetimeFilter must be Either _start or _end"))
                        logger.error(response.detail)

        # Byte Filter
        if data_filter.byte_filter is not None:
            byte_suffix = '_filter'
            
            for key, value in data_filter.byte_filter.__dict__.items():
                if value is not None:
                    column_name = key.split(byte_suffix)[0]
                    pass
                    # _conditions = filter_by_byte(db_model=KnowledgeDB, column_name=column_name, entity_filter=value)
                    # if _conditions:
                    #     conditions += _conditions

                    # else:
                    #     response = Response(status_code=404, detail=self.response_format.error(f"Attribute Error : Unrecognized Attribute <{column_name}>. The Attribute Suffix for ByteFilter must be _filter"))
                    #     logger.error(response.detail)

        # Define sorting order
        sorting = []
        for col, direction in data_filter.sorting.items():
            if direction.lower() == "desc":
                sorting.append(f"{col} desc")
            else:
                sorting.append(f"{col} asc")

        filter_no = data_filter.filter_no

        return conditions, sorting, filter_no

    def export_data(self, config: IOConfig | None, data: list[dict], include_datetime: bool = True) -> Response:
          
            # Use default export configuration if none provided
            if config is None:
                config = self.default_export_config.copy()
                logger.info("Using default export configuration")

            # Handle file-based exports (JSON, CSV)
            if config.format.upper() in SETTINGS.EXPT.FILE_FORM:
                try:
                    file_name = config.file_name
                    if include_datetime:
                        file_name += '_' + datetime.now(timezone.utc).strftime("%Y-%m-%d-%H-%M-%S")
                    file_name += '.' + config.format.lower()
                    file_root = os.path.join(config.file_rdir, config.file_sdir) if config.file_sdir else config.file_rdir
                    file_path = os.path.join(file_root, file_name)
                    os.makedirs(file_root, exist_ok=True)
                except Exception as e:
                    response = Response(
                        status_code=500,
                        detail=self.response_format.error("Error preparing file path", str(e))
                    )
                    logger.error(response.detail)
                    return response


            # 3. Export Data
            # 3.1. Export to JSON
            if config.format.upper() == 'JSON':
                try:
                    with open(file_path, 'w', encoding="utf-8") as json_file:
                        json.dump(data, json_file, cls=ComplexEncoder, ensure_ascii=False, indent=4)
                
                    response = Response(status_code=200, detail=self.response_format.ok(f"Success : Exported Data to <{file_path}>"))
                    logger.info(response.detail)

                except:
                    response = Response(status_code=500, detail=self.response_format.error(f"Data Export Error : Failed to Export Data to <{file_path}>"))
                    logger.error(response.detail)
                
            # 3.2. Export to CSV
            elif config.format.upper() == 'CSV':
                try:
                    with open(file_path, 'w',  newline='', encoding="utf-8-sig") as csv_file:
                        fieldnames = data[0].keys()
                        writer = csv.DictWriter(csv_file, fieldnames=fieldnames)
                        writer.writeheader()    # Write the header
                        writer.writerows(data)  # Write the data
                    response = Response(status_code=200, detail=self.response_format.ok(f"Success : Exported Data to <{file_path}>"))
                    logger.info(response.detail)

                except:
                    response = Response(status_code=500, detail=self.response_format.error(f"Data Export Error : Failed to Export Data to <{file_path}>"))
                    logger.error(response.detail)

            # Handle unsupported formats
            else:
                response = Response(
                    status_code=500,
                    detail=self.response_format.error(f"Unsupported export format: {config.format}")
                )
                logger.error(response.detail)
                return response

            return response


    def preprocess_text(self, request: PrepMediaPipelineRequest) -> tuple[PrepMediaPipelineResponse, Response]:
        response_data = PrepMediaPipelineResponse(**request.__dict__)

        try:
            # API Call
            if self.api_call == True:
                api_url = f"http://{SETTINGS.PREP.HOST}:{SETTINGS.PREP.PORT}/{SETTINGS.PREP.REQUEST_PREPMEDIA_API}"
                payload = request.json()
                response_data, response = self.api_call_static(data=payload, service="KnowledgeHub", api_url=api_url, method="post", timeout=SETTINGS.BASE.APP_TIMEOUT)
                response_data = PrepMediaPipelineResponse(**response_data)
                if response.status_code < SETTINGS.STAT.SUCC_CODE_END:
                    response_data = response_data.json()
                    response = Response(status_code=200, detail=self.response_format.ok(f"Success : <{SETTINGS.BASE.APP_NAME}> Completed Text Preprocessing via API"))
                    logger.info(response.detail)

            # Function Call
            else:   
                try:
                    response_data = request_exec_prepmedia(request=request, api_call=False)
                    response_data = PrepMediaPipelineResponse(**response_data.__dict__)
                    response = Response(status_code=200, detail=self.response_format.ok(f"Success : <{SETTINGS.BASE.APP_NAME}> Completed Text Preprocessing via Function Call"))
                    logger.info(response.detail)
                except Exception as e:
                    response = Response(status_code=500, detail=self.response_format.error(f"Function Retrieval Error : <{SETTINGS.BASE.APP_NAME}> Failed to Complete Knolwdge Preprocessing via Function Call"))
                    logger.error(response.detail)
            
        # Handle common exceptions that might occur
        except (BaseException, Exception) as e:
            response = Response(status_code=500, detail=self.response_format.error(f"Common Error : <{SETTINGS.BASE.APP_NAME}> Preprocessing Text", str(e)))
            logger.error(response.detail)

        # Handle any other exceptions that might occur
        except:
            response = Response(status_code=500, detail=self.response_format.error(f"Unexpected Error : <{SETTINGS.BASE.APP_NAME}> Preprocessing Text"))
            logger.error(response.detail)

        return response_data, response

    def api_call_static(self, data, service: str, api_url: str, method: str, timeout: float | None) -> tuple[httpx.Response | None, Response]:
        response_data = None

        try:
            if method.lower() == "post":

                if isinstance(data, str):
                    if timeout:
                        resp = httpx.post(api_url, data=data, timeout=timeout)
                    else:
                        resp = httpx.post(api_url, data=data)

                else:
                    if timeout:
                        resp = httpx.post(api_url, json=data, timeout=timeout)
                    else:
                        resp = httpx.post(api_url, json=data)

            else:
                response = Response(status_code=500, detail=self.response_format.error(f"API Method Error : Unknown API Method <{method}>"))
                logger.error(response.detail)
                return response_data, response

            if not resp.status_code == httpx.codes.ok:
                response = Response(status_code=resp.status_code, detail=self.response_format.error(f"Response Error : Retrieving Data from <{service}> API Server", resp["detail"]))
                logger.error(response.detail)
            
            else:
                response = Response(status_code=resp.status_code, detail=self.response_format.ok(f"Success : Retrieved Data from <{service}> API Server"))
                response_data = resp

        except httpx.TimeoutException as e:
            response = Response(status_code=502, detail=self.response_format.error(f"Timeout Error : Retrieving Data <{service}> API Server", str(e)))
            logger.error(response.detail)

        except httpx.HTTPError as e:
            response = Response(status_code=502, detail=self.response_format.error(f"Connection Error : Retrieving Data <{service}> API Server", str(e)))
            logger.error(response.detail)

        # Handle common exceptions that might occur
        except (BaseException, Exception) as e:
            response = Response(status_code=500, detail=self.response_format.error(f"Common Error : Connecting to <{service}> API Server", str(e)))
            logger.error(response.detail)

        # Handle any other exceptions that might occur
        except:
            response = Response(status_code=500, detail=self.response_format.error(f"Unexpected Error : Connecting to <{service}> API Server"))
            logger.error(response.detail)

        return response_data, response