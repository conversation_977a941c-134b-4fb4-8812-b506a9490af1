name: Deploy Production

on:   
  workflow_dispatch:
    
  push:
    tags:
      - '[0-9]+.[0-9]+.[0-9]+'  # Matches semantic versioning tags

jobs:
  validate:
    runs-on: ubuntu-latest
    outputs:
      build_number: ${{ steps.validate-tag.outputs.build_number }}
      version: ${{ steps.validate-tag.outputs.version }}
    steps:
      - name: Checkout Code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Validate Tag
        id: validate-tag
        uses: pru-pss/pss-eta-reusable_workflows/actions/validate-tag-on-same-commit@main
        with:
          target_env: prd
          tag_name: ${{ github.ref_name }}
          base_branch: "develop"
  

  deploy:
    needs: validate
    runs-on: pru-phkl-all-prod-linux-runner-01
    environment: production
    steps: 
      - name: Checkout Code
        uses: actions/checkout@v4

      - name: Deploy to prod
        id: deploy-to-prod
        uses: pru-pss/pss-eta-reusable_workflows/actions/cd-kubectl-set-image@main
        with:
          image_name: ${{ vars.IMAGE_NAME }}
          env: prd
          build_number: ${{ needs.validate.outputs.build_number }}
          version: ${{ needs.validate.outputs.version }}
          deployment_name: ${{ vars.DEPLOYMENT_NAME }}
          container_name: ${{ vars.DEPLOYMENT_NAME }}
          registry_url: ${{ vars.JFROG_DOCKER_REGISTRY_URL }}
          registry_internal_url: ${{ vars.JFROG_DOCKER_REGISTRY_INTERNAL_URL }}
          jfrog_username: ${{ vars.JFROG_USERNAME }}
          jfrog_password: ${{ secrets.JFROG_PASSWORD }}
          role_id: ${{ secrets.VAULT_AKS_ROLE_ID }}
          secret_id: ${{ secrets.VAULT_AKS_SECRET_ID }}
          kv_path: ${{ vars.VAULT_AKS_KV_PATH }}
          kv_endpoint: ${{ vars.VAULT_AKS_KV_ENDPOINT }}