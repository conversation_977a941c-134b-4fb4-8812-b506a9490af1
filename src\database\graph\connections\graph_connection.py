import sys
import asyncio
import urllib.parse
from contextlib import contextmanager

from ....settings import SETTINGS

from ....logger.log_handler import get_logger

logger = get_logger(__name__)

if SETTINGS.GPDB.FORM.upper() == "COSMO":
    from azure.cosmos import CosmosClient
    import azure.cosmos.cosmos_client as cosmos_client
    import azure.cosmos.exceptions as exceptions
    from gremlin_python.driver import client, serializer

    if sys.platform == "win32":
        asyncio.set_event_loop_policy(asyncio.WindowsSelectorEventLoopPolicy())

    DB_URL = '{host}:{port}/'.format(
        host=urllib.parse.quote_plus(SETTINGS.GPDB.HOST),
        port=urllib.parse.quote_plus(SETTINGS.GPDB.PORT)
    )

    DATABASE_URL = f"wss://{DB_URL}"

    try:
        client_conn = CosmosClient(SETTINGS.GPDB.ENDPOINT, SETTINGS.GPDB.PSWD)
        gb_client   = client_conn.create_database_if_not_exists(id = SETTINGS.GPDB.NAME)
        logger.info(f"DB Connected : <{SETTINGS.BASE.APP_NAME}> <{SETTINGS.GPDB.NAME}> Connected")

    except exceptions.CosmosResourceExistsError:
        logger.info(f"DB Found and Connected : <{SETTINGS.BASE.APP_NAME}> <{SETTINGS.GPDB.NAME}> Connected")

    except exceptions.CosmosHttpResponseError as e:
        err_msg = f"DB Connection Error : <{SETTINGS.BASE.APP_NAME}> <{SETTINGS.GPDB.NAME}> Database"
        logger.error(err_msg)
        raise Exception(err_msg)

    # Handle any other exceptions that might occur
    except:
        err_msg = f"Unexpected Error : <{SETTINGS.BASE.APP_NAME}> <{SETTINGS.GPDB.NAME}> Database"
        logger.error(err_msg)
        raise Exception(err_msg)

    # gremlin_client = client.Client(
    #     url=DATABASE_URL,
    #     traversal_source="g",
    #     username=f"/dbs/{SETTINGS.GPDB.NAME}",
    #     password=SETTINGS.GPDB.PSWD,
    #     message_serializer=serializer.GraphSONSerializersV2d0()
    # )

    # DB via API Call
    def create_gb_client():
        try:
            return gb_client
        except Exception as e:
            logger.error(str(e))
            raise e

    # DB via Function Call
    @contextmanager
    def get_gb_func(container_name: str):
        try:
            gremlin_client = client.Client(
                url=DATABASE_URL,
                traversal_source="g",
                username=f"/dbs/{SETTINGS.GPDB.NAME}/colls/{container_name}",
                password=SETTINGS.GPDB.PSWD,
                message_serializer=serializer.GraphSONSerializersV2d0()
            )
            yield gremlin_client
        except Exception as e:
            logger.error(str(e))
            raise e
        finally:
            gremlin_client.close()

    # DB via API Call
    @contextmanager
    def get_gb_api(container_name: str):
        try:
            gremlin_client = client.Client(
                url=DATABASE_URL,
                traversal_source="g",
                username=f"/dbs/{SETTINGS.GPDB.NAME}/colls/{container_name}",
                password=SETTINGS.GPDB.PSWD,
                message_serializer=serializer.GraphSONSerializersV2d0()
            )
            yield gremlin_client
        except Exception as e:
            logger.error(str(e))
            raise e
        finally:
            gremlin_client.close()

else:
    err_msg = f"Unknown DB Error : <{SETTINGS.GPDB.NAME}> Database"
    logger.error(err_msg)
    raise Exception(err_msg)
