from fastapi import APIRouter, status

from ...settings import SETTINGS
from ...utils import router_response_handler


from ...database.vector.services.vector_data import (
    VectorDataManager,
    SystemVectorRequest,
    SystemVectorResponse
)

from ...schemas.format import Response

router = APIRouter(tags=["Vector-System"])

# API DB Session
if SETTINGS.BASE.APP_API == True:
    default_api_call = True
else:
    default_api_call = False


@router.post("/system/vector/query", include_in_schema=not(SETTINGS.BASE.APP_PRODUCTION), status_code=status.HTTP_200_OK, response_model=SystemVectorResponse)
def system_query_vector(request: SystemVectorRequest, api_call: bool = default_api_call) -> SystemVectorResponse:
    request = SystemVectorRequest(**request.__dict__)
    response_data, response = VectorDataManager(api_call=api_call, vector_storage=SETTINGS.VTDB.FORM, vector_location=SETTINGS.VTDB.LOCA, vector_config=SETTINGS.VTDB.CONFIG).query_data_by_system(request=request)
    router_response_handler(response=response, api_call=api_call)
    return response_data