from fastapi import APIRouter, Depends, status
from typing import Any

from ...settings import SETTINGS
from ...schemas.format import Response
from ...utils import router_response_handler

from ...database.registry.services.qaflow_citation_log_data import (
        DataManager as QAFlowCitationLogDataManager,
        SystemDataRequest as SystemQAFlowCitationLogRequest, 
        SystemDataResponse as SystemQAFlowCitationLogResponse,
    )

router = APIRouter(tags=["Registry-Admin"])

# API DB Session
if SETTINGS.BASE.APP_API == True:
    default_api_call = True
else:
    default_api_call = False


@router.post("/system/qaflow_citation_log/query", include_in_schema=not(SETTINGS.BASE.APP_PRODUCTION), status_code=status.HTTP_200_OK, response_model=SystemQAFlowCitationLogResponse)
def system_query_qaflow_citation_log(request: SystemQAFlowCitationLogRequest, api_call: bool=default_api_call) -> SystemQAFlowCitationLogResponse:
    request = SystemQAFlowCitationLogRequest(**request.__dict__)
    response_data, response = QAFlowCitationLogDataManager(api_call=api_call).query_data_by_system(request=request)
    router_response_handler(response=response, api_call=api_call)
    return response_data

@router.delete("/system/qaflow_citation_log/drop/inactive", include_in_schema=not(SETTINGS.BASE.APP_PRODUCTION), status_code=status.HTTP_200_OK)
def system_drop_inactive_qaflow_citation_log(api_call: bool=default_api_call) -> Response:
    response = QAFlowCitationLogDataManager(api_call=api_call).drop_inactive_by_system()
    router_response_handler(response=response, api_call=api_call)
    return response
