from pydantic import BaseModel, <PERSON>
from typing import Literal
import uuid
from datetime import datetime

from ..settings import SETTINGS

"""
    Knowledge Retrieval Log
"""
class RawCitationLog(BaseModel):
    data_id:      str=''
    data_version: int=1
    knowledge_id: str=''

class ModelUsageLog(BaseModel):
    service_name:  str=''
    model_name:    str=''
    input_tokens:  int=0
    output_tokens: int=0
    process_sec:   float=0.0

class KnowledgeRetrievalLog(BaseModel):
    input_query:        str=''
    query_keywords:     list[str]=[]
    keyword_knowledges: list[RawCitationLog]=[]
    vector_citations:   list[RawCitationLog]=[]
    graph_citations:    list[RawCitationLog]=[]
    rerank_citations:   list[RawCitationLog]=[]

    success_flag:       bool=True
    error_message:      str=''
    process_sec:        float=0.0
    model_usage:        list[ModelUsageLog]=[]

""" 
    Knowledge Retrieval Request
"""
class CustomField(BaseModel):
    agent_id:       str = Field(..., description = "Agent ID to verify the access right", example = 'AD11223')
    pru_force_lang: Literal["en", "zh"] = Field(..., description = "Language variable supporting Traditional Chinese (zh) and English (en)", example = 'en')

class CitationSourceObject(BaseModel):
    content:          str=Field(default="", description="Content of the citation")
    source_type:      str=Field(default="UNKNOWN", description="Source type of the citation, e.g., text, image, table")
    image_table_link: str=Field(default="", description="Image or table URL of the citation")
    score:            float=Field(default=0.0, description="Retrieval Scoring")

class CitationObject(BaseModel):
    document_name:        str=Field(default="UNKNOWN", description="Name of the document")
    hyperlink:            str=Field(default="UNKNOWN", description="Hyperlink of the document")
    page_number:          str=Field(default="UNKNOWN", description="Page numbers of the document")
    last_update_date:     str=Field(default="UNKNOWN", description="Last update date of the document")
    pil_reference_number: str=Field(default="UNKNOWN", description="PIL reference number of the document")
    raw_sources:          list[CitationSourceObject]=Field(default=[], description="Source of the citaiton")
    translation:          str=Field(default='ORIGINAL', description="Translation notes of the citation")

class SearchConfiguration(BaseModel):
    keyword_search_enable: bool=Field(default=True, description="Enable keyword search")
    keyword_search_config: dict=Field(default=dict(), description="Keyword search configuration")
    vector_search_enable:  bool=Field(default=True, description="Enable vector search")
    vector_search_config:  dict=Field(default=dict(), description="Vector search configuration")
    graph_search_enable:   bool=Field(default=False, description="Enable graph search")
    graph_search_config:   dict=Field(default=dict(), description="Graph search configuration")
    retrieval_no:          int=Field(default=10, description="Number of retrievals")
    rerank_threshold:      float=Field(default=SETTINGS.RETR.DEFAULT_RERANK_THRESHOLD, description="Reranking threshold")
    
class KnowledgeRetrievalRequest(BaseModel):
    retrieval_requestid: str                 = Field(default_factory=lambda: str(uuid.uuid4()))
    knowledge_ids:       list[str]           = Field(default=[], description="Filter for evaluation")
    query:               str                 = Field(description="User query to retrieve knowledge.")
    search_config:       SearchConfiguration = Field(default=SearchConfiguration(), description="Search configuration")
    custom_field:        CustomField         = Field(description="custom_field to pass to AI-PIL")
    save_log:            bool                = Field(default=True, description="Flag to save log")
    agent_group_id:      str | None = None # = Field("", description="User query access level group id.")
    agent_group_id_list: list | None = None

class KnowledgeRetrievalResponse(BaseModel):
    retrieval_requestid: str
    citations:           list[CitationObject]  = Field(default=[], description="List of CitationObjects")
    citation_count:      int                   = Field(default=0, description="Number of citations retrieved")
    retrieval_log:       KnowledgeRetrievalLog = Field(default=KnowledgeRetrievalLog(), description="Retrieval Log")

class RawDocumentObject(BaseModel):
    knowledge_id:         str=Field(default="UNKNOWN", description="knowledge_id of the document")
    knowledge_languages:  list[str]=Field(default=[], description="Languages of the document")
    document_name:        str=Field(default="UNKNOWN", description="Name of the document")
    hyperlink:            str=Field(default="UNKNOWN", description="Hyperlink of the document")
    last_update_date:     str=Field(default="UNKNOWN", description="Last update date of the document")
    pil_reference_number: str=Field(default="UNKNOWN", description="PIL reference number of the document")
    translation:          str=Field(default='ORIGINAL', description="Translation notes of the citation")

class RawCitationObject(BaseModel):
    data_id:              str=Field(default="", description="data_id of the citation")
    knowledge_id:         str=Field(default="", description="knowledge_id of the document")
    content:              str=Field(default="", description="Content of the citation")
    source_type:          str=Field(default="UNKNOWN", description="Source type of the citation, e.g., text, image, table")
    image_table_link:     str=Field(default="", description="Image or table URL of the citation")
    page_start:           int=Field(default="UNKNOWN", description="Page start of the citation")
    page_end:             int=Field(default="UNKNOWN", description="Page end of the citation")
    seq_no:               int=Field(default=-1, description="Sequence number of the citation")
    parent_id:            str=Field(default="", description="Parent data_id of the citation")
    langauage:            str=Field(default="en", description="Langauge of the citaiton")
    score:                float=Field(default=0.0, description="Retrieval Scoring")

class RawKnowledgeRetrievalResponse(BaseModel):
    retrieval_requestid: str
    refined_queries:     list[str]=[]
    documents:           list[RawDocumentObject] = Field(default=[], description="List of RawDocumentObjects")
    citations:           list[RawCitationObject] = Field(default=[], description="List of RawCitationObjects")
    document_count:      int                     = Field(default=0, description="Number of documents retrieved")
    citation_count:      int                     = Field(default=0, description="Number of citations retrieved")

    retrieval_log:       KnowledgeRetrievalLog = Field(default=KnowledgeRetrievalLog(), description="Retrieval Log")

class CitationRetrievalRequest(BaseModel):
    retrieval_requestid: str                     = Field(default_factory=lambda: str(uuid.uuid4()))
    query:               str                     = Field(description="User query to retrieve knowledge.")
    citations:           list[RawCitationObject] = Field(description="User query to retrieve knowledge.")
    search_config:       SearchConfiguration     = Field(default=SearchConfiguration(), description="Search configuration")
    custom_field:        CustomField             = Field(description="custom_field to pass to AI-PIL")
    
class CitationRetrievalResponse(BaseModel):
    retrieval_requestid: str
    citations:           list[RawCitationObject] = Field(default=[], description="List of CitationObjects")
    citation_count:      int                     = Field(default=0, description="Number of citations retrieved")


class RawRetrievalObject(BaseModel):
    document:  RawDocumentObject=Field(default=RawDocumentObject(), description="List of RawDocumentObjects")
    citations: list[RawCitationObject]=Field(default=[], description="List of RawCitationObjects")

class RetrievalTestRequest(BaseModel):
    retrieval_requestid: str                 = Field(default_factory=lambda: str(uuid.uuid4()), description="Unqiue ID of the request, default UUID")
    knowledge_ids:       list[str]           = Field(default=[], description="[Optional] Filter for evaluation, empty list will search all documents")
    query:               str                 = Field(description="User query to retrieve knowledge.", example="What is the product?")
    search_config:       SearchConfiguration = Field(default=SearchConfiguration(), description="[Optional] Search configuration")
    custom_field:        CustomField         = Field(description="custom_field to pass to AI-PIL", example=CustomField(agent_id= 'abc123', pru_force_lang='en'))

    class Config:
        schema_extra = {
            "example": {
                "retrieval_requestid": "9e7cb831-a33e-4f4b-939d-5ad26c128541",
                "knowledge_ids": ["knowledge_id_1"],
                "query": "What is the product?",
                "custom_field": {
                    "agent_id": "abc123",
                    "pru_force_lang": "en"
                }
            }
        }

class RetrievalTestResponse(BaseModel):
    retrieval_requestid: str                      = Field(..., description="Unqiue ID of the request, default UUID")
    grouped_citations:   list[RawRetrievalObject] = Field(
                                                                default=[], 
                                                                description="List of TestDocumentObjects", 
                                                                example=[
                                                                    RawRetrievalObject(
                                                                        document=RawDocumentObject(
                                                                            knowledge_id="123", 
                                                                            knowledge_languages=["en"],
                                                                            document_name="Test Document",
                                                                            hyperlink="blob storage url",
                                                                            last_update_date="2023-10-01",
                                                                            pil_reference_number="PIL12345",
                                                                            translation="None"
                                                                        ),
                                                                        citations=[
                                                                            RawCitationObject(
                                                                                data_id="abc",
                                                                                knowledge_id="123",
                                                                                content="Test Content",
                                                                                source_type="text",
                                                                                image_table_link="blob storage url",
                                                                                page_start=1,
                                                                                page_end=2,
                                                                                langauage="en",
                                                                                score=0.9
                                                                            )
                                                                        ]
                                                                    )
                                                                ]
                                                            )
    document_count:      int                               = Field(default=0, description="Number of documents retrieved")
    citation_count:      int                               = Field(default=0, description="Number of citations retrieved")

    class Config:
        schema_extra = {
            "example": {
                "retrieval_requestid": "9e7cb831-a33e-4f4b-939d-5ad26c128541",
                "grouped_citations": [
                    {
                        "document": {
                            "knowledge_id": "123",
                            "knowledge_languages": ["en"],
                            "document_name": "Test Document",
                            "hyperlink": "blob_url",
                            "last_update_date": "2023-10-01",
                            "pil_reference_number": "PIL12345",
                            "translation": "None"
                        },
                        "citations": [
                            {
                                "data_id": "abc",
                                "knowledge_id": "123",
                                "content": "Test Content",
                                "source_type": "text",
                                "image_table_link": "blob_url",
                                "page_start": 1,
                                "page_end": 2,
                                "langauage": "en",
                                "score": 0.9
                            }
                        ]
                    }
                ],
                "document_count": 1,
                "citation_count": 1
            }
        }

"""
    Graph Search
"""
class KnowDataObject(BaseModel):
    # Trace Information
    data_id:        str=Field(default_factory=lambda: str(uuid.uuid4()))
    data_traceid:   str=Field(default_factory=lambda: str(uuid.uuid4()))
    data_version:   int=1

    # Category Information
    data_type:      str='' # TEXT, IMAGE, TABLE, DOCUMENT, etc.
    content_type:   str='default' # default, image-to-text, OCR
    data_url:       str='' # Image URL, Table URL

    # Control Information
    data_status:    int=1

    # Specification
    raw_data:       str='' # Raw Text, Image Description, Text on Image, etc.
    processed_data: list[float]=[] # vector

    coord_x1:       float=-1.0
    coord_x2:       float=-1.0
    coord_y1:       float=-1.0
    coord_y2:       float=-1.0

    page_start:     int=-1
    page_end:       int=-1
    line_start:     int=-1
    line_end:       int=-1

    # Dependent
    seq_no:         int=-1 # Sequence Number
    knowledge_id:   str='' # Map to Original Knowledge
    node_id:        str=''
    node_type:      str=''

    # Tags
    data_languages: list[str]=[]
    data_keywords:  list[str]=[]
    data_tags:      list[str]=[]

    # Time Information
    created_at:     datetime = Field(default_factory=datetime.now)
    updated_at:     datetime = Field(default_factory=datetime.now)

class RelationshipObj(BaseModel):
    origin_nodeid:          str=""
    destination_nodeid:     str=""
    relationship:           str=''

class GraphSearchRequest(BaseModel):
    retrieval_requestid:    str=Field(default_factory=lambda: str(uuid.uuid4()))
    knowdata_objs:          list[KnowDataObject] | None = []
    graph_search_layers:    int=1
    graph_search_method:    str='herichacy'
    relationship:           str | None = ""

class GraphSearchResponse(BaseModel):
    retrieval_requestid:    str
    graph_search_layers:    int | None = ""
    graph_search_method:    str='herichacy'
    relationship:           str | None = ""
    knowdata_objs:          list[KnowDataObject]=[]
    retrieved_data:         list[RelationshipObj]=[]
    node_retrieval_time:    float=0.0


class KeywordMappingModel(BaseModel):
    mapping_id: str
    keywords: list[str]
    ranking: int | None = None
    mapping_status: int
    mapping_version: int
    created_at: datetime
    updated_at: datetime
class SystemKeywordMappingFuncRequest(BaseModel):
    search_str: str

class SystemKeywordMappingFuncReponse(BaseModel):
    filtered_data: list[KeywordMappingModel]