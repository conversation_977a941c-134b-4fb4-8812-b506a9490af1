# Git Branching Guidelines

## Branch Types and Naming

| Branch Pattern       | Purpose                         |
| -------------------- | ------------------------------- |
| `feature/*`          | New feature development         |
| `bugfix/*`           | Bug fix implementation          |
| `feature/deploy-dev` | Early stage integration         |
| `develop`            | Development and UAT integration |
| `main`               | Production stable branch        |

## Env/Promotion Tag Format

| Environment | Format            | Example      |
| ----------- | ----------------- | ------------ |
| DEV         | `{build}-dev`     | `1234-dev`   |
| UAT         | `{build}-uat`     | `1234-uat`   |
| PROD        | `{build}-{x.x.x}` | `1234-1.0.0` |

## Branching Workflow

| Branch Type          | Purpose                 | Merge Target                                         | Deployment Triggers                                  | Tag Format                     |
| -------------------- | ----------------------- | ---------------------------------------------------- | ---------------------------------------------------- | ------------------------------ |
| `feature/*`          | Feature development     | `feature/deploy-dev` (early)<br>`develop` (standard) | None                                                 | None                           |
| `bugfix/*`           | Bug fixes               | `develop`                                            | None                                                 | None                           |
| `feature/deploy-dev` | Early stage integration | `develop`                                            | DEV deploy with `{build}-dev` tag                    | `{build}-dev`                  |
| `develop`            | DEV/UAT integration     | `main`                                               | • DEV: `{build}-dev` tag<br>• UAT: `{build}-uat` tag | `{build}-dev`<br>`{build}-uat` |
| `main`               | Production branch       | N/A                                                  | PROD: `{build}-{x.x.x}` tag                          | `{build}-{x.x.x}`              |

## Development Workflow

### Initial Development

1. Create feature branch:

   ```
   feature/add-login
   ```

2. **Early Stage Projects**:

   - Merge to `feature/deploy-dev` (no PR required)
   - Auto-deploys to DEV with `{build}-dev` tag
   - Create PR: `feature/deploy-dev` → `develop`

3. **Standard Projects**:
   - Create PR directly to `develop`
   - Auto-deploys to DEV with `{build}-dev` tag

### Promotion Process

After merging to `develop`, both early and standard stage projects follow the same promotion workflow:

#### UAT Promotion

1. Identify successful DEV build tag (`{build}-dev`)
2. Create PR from `develop` using the DEV build commit
3. After approval and merge, tag same commit with `{build}-uat`
4. UAT deployment triggers automatically

#### Production Promotion

1. Identify successful UAT build tag (`{build}-uat`)
2. Create PR to `main` using the UAT build commit
3. After approval and merge, tag same commit with `{build}-{x.x.x}`
4. Production deployment triggers automatically

## Workflow Diagram

```mermaid
sequenceDiagram
    participant F as feature/*
    participant FD as feature/deploy-dev
    participant D as develop
    participant M as main
    participant ENV as Environments

    Note over F,ENV: Early Stage Path
    F->>FD: Push changes
    FD->>FD: Auto tag {build}-dev
    FD-->>ENV: Deploy to DEV
    FD->>D: PR & merge

    Note over F,ENV: Standard Path
    F->>D: PR & merge
    D->>D: Auto tag {build}-dev
    D-->>ENV: Deploy to DEV

    Note over F,ENV: Common Promotion Path
    D->>D: Tag {build}-uat
    D-->>ENV: Deploy to UAT
    D->>M: PR & merge
    M->>M: Tag {build}-{x.x.x}
    M-->>ENV: Deploy to PROD
```
