from fastapi import APIRouter, Depends, status
from typing import Any

from ...settings import SETTINGS
from ...schemas.format import Response
from ...utils import router_response_handler

from ...database.registry.services.qaflow_citation_log_data import (
        DataManager as QAFlowCitationLogDataManager,
        DataBackupRequest as QAFlowCitationLogBackupRequest, 
        DataBackupListRequest as QAFlowCitationLogBackupListRequest,
        DataBackupListResponse as QAFlowCitationLogBackupListResponse,
        DBRestoreRequest as QAFlowCitationLogRestoreRequest,
        DataImportRequest as QAFlowCitationLogImportRequest,
        DataExportRequest as QAFlowCitationLogExportRequest,
    )

router = APIRouter(tags=["Registry-IO"])

# API DB Session
if SETTINGS.BASE.APP_API == True:
    default_api_call = True
else:
    default_api_call = False

@router.post("/io/qaflow_citation_log/export", include_in_schema=not(SETTINGS.BASE.APP_PRODUCTION), status_code=status.HTTP_200_OK)
def system_export_qaflow_citation_log(request: QAFlowCitationLogExportRequest, api_call: bool=default_api_call) -> Response:
    request  = QAFlowCitationLogExportRequest(**request.__dict__)
    response = QAFlowCitationLogDataManager(api_call=api_call).export_data_by_system(request=request)
    router_response_handler(response=response, api_call=api_call)
    return response

@router.post("/io/qaflow_citation_log/import", include_in_schema=not(SETTINGS.BASE.APP_PRODUCTION), status_code=status.HTTP_200_OK)
def system_import_qaflow_citation_log(request: QAFlowCitationLogImportRequest, api_call: bool=default_api_call) -> Response:
    request  = QAFlowCitationLogImportRequest(**request.__dict__)
    response = QAFlowCitationLogDataManager(api_call=api_call).import_data_by_system(request=request)
    router_response_handler(response=response, api_call=api_call)
    return response

@router.post("/io/qaflow_citation_log/backup", include_in_schema=not(SETTINGS.BASE.APP_PRODUCTION), status_code=status.HTTP_200_OK)
def system_backup_qaflow_citation_log(request: QAFlowCitationLogBackupRequest, api_call: bool=default_api_call) -> Response:
    request  = QAFlowCitationLogBackupRequest(**request.__dict__)
    response = QAFlowCitationLogDataManager(api_call=api_call).backup_data_by_system(request=request)
    router_response_handler(response=response, api_call=api_call)
    return response

@router.post("/io/qaflow_citation_log/backup/list", include_in_schema=not(SETTINGS.BASE.APP_PRODUCTION), status_code=status.HTTP_200_OK, response_model=QAFlowCitationLogBackupListResponse)
def system_backup_list_qaflow_citation_log(request: QAFlowCitationLogBackupListRequest, api_call: bool=default_api_call) -> QAFlowCitationLogBackupListResponse:
    request = QAFlowCitationLogBackupListRequest(**request.__dict__)
    response_table, response = QAFlowCitationLogDataManager(api_call=api_call).list_backup_by_system(request=request)
    router_response_handler(response=response, api_call=api_call)
    return response_table

@router.post("/io/qaflow_citation_log/restore/backup", include_in_schema=not(SETTINGS.BASE.APP_PRODUCTION), status_code=status.HTTP_200_OK)
def system_restore_backup_qaflow_citation_log(request: QAFlowCitationLogRestoreRequest, api_call: bool=default_api_call) -> Response:
    request  = QAFlowCitationLogRestoreRequest(**request.__dict__)
    response = QAFlowCitationLogDataManager(api_call=api_call).restore_backup_by_system(request=request)
    router_response_handler(response=response, api_call=api_call)
    return response
