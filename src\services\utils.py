from datetime import datetime, timedelta, timezone
import time
import inspect
import os

from azure.storage.blob import BlobServiceClient, generate_blob_sas, BlobSasPermissions

from ..settings import SETTINGS

from ..schemas.format import (
    ResponseFormatter,
    Response,
    ComplexEncoder
)

from ..schemas.utils import(
    BlobShareServiceRequest,
    BlobShareServiceResponse
)

from ..logger.log_handler import get_logger

logger = get_logger(__name__)

class AzureStorageServiceManager:
    response_format = ResponseFormatter(suffix = f"{inspect.stack()[0][3]}")
    
    def generate_share_blob(self, request: BlobShareServiceRequest)-> tuple[BlobShareServiceResponse, Response]:
        logger.info("Processing : Generating Share Blob Object ...")
        response_data = BlobShareServiceResponse()

        BLOB_CONN_STR = 'DefaultEndpointsProtocol=https;AccountName={account_name};AccountKey={account_key};EndpointSuffix=core.windows.net'.format(
            account_name = SETTINGS.AZST.ACCOUNT_NAME,
            account_key  = SETTINGS.AZST.ACCOUNT_KEY
        )

        try:
            blob_service_client = BlobServiceClient.from_connection_string(BLOB_CONN_STR)
        except:
            response = Response(status_code=500, detail=self.response_format.error(f"Failed to Connect to Azure Blob Storage"))
            logger.error(response.detail)
            return response_data, response
        try:
            source_blob_name = request.source_blob_path

            source_blob_client = blob_service_client.get_blob_client(
                container = SETTINGS.AZST.SOURCE_CONTAINER,
                blob      = source_blob_name
            )
            source_blob_url = request.source_blob_path
        except:
            response = Response(status_code=500, detail=self.response_format.error(f"Failed to Connect to Source Blob"))
            logger.error(response.detail)
            return response_data, response

        # Share Blob
        shared_container = SETTINGS.AZST.SHARED_CONTAINER
        share_blob_name = request.source_blob_path.split(f"{SETTINGS.AZST.SOURCE_CONTAINER}/")[-1]
        share_blob_base = os.path.basename(request.source_blob_path)
        share_blob_root = share_blob_name.split(share_blob_base)[0]

        share_blob_full_name = "{share_blob_root}{share_blob_base}".format(
            share_blob_root = share_blob_root,
            share_blob_base = share_blob_base
        )

        try:
            share_blob_client = blob_service_client.get_blob_client(
                container = SETTINGS.AZST.SHARED_CONTAINER,
                blob      = share_blob_full_name
            )
        except:
            response = Response(status_code=500, detail=self.response_format.error(f"Failed to Connect to Share Blob"))
            logger.error(response.detail)
            return response_data, response

        # Check if Share Blob Exists
        if share_blob_client.exists():
            logger.info(f"Share blob {share_blob_full_name} already exists in container {SETTINGS.AZST.SHARED_CONTAINER}, skipping copy.")
        else:
            try:
                copy_operation = share_blob_client.start_copy_from_url(source_blob_url)
            except:
                response = Response(status_code=500, detail=self.response_format.error(f"Failed to Copy Blob From Source Blob to Share Blob"))
                logger.error(response.detail)
                return response_data, response

            while copy_operation['copy_status'] == 'pending':
                time.sleep(1)
                copy_operation = share_blob_client.get_blob_properties()['copy']

            if copy_operation['copy_status'].lower() != 'success':
                e = copy_operation['copy_status_description']
                response = Response(status_code = 500, detail = self.response_format.error(f"Blob Copy Failed : Failed to Copy Blob From Source Blob to Share Blob", str(e)))
                logger.error(response.detail)
                return response_data, response

        # Generate SAS URL
        start_time  = datetime.now(timezone.utc)
        expiry_time = start_time + timedelta(days=3)
        try:
            sas_token = generate_blob_sas(
                account_name   = SETTINGS.AZST.ACCOUNT_NAME,
                container_name = SETTINGS.AZST.SHARED_CONTAINER,
                blob_name      = share_blob_full_name,
                account_key    = SETTINGS.AZST.ACCOUNT_KEY,
                permission     = BlobSasPermissions(read=True),
                expiry         = expiry_time,
                start          = start_time,
                protocol       = 'https'
            )
        except:
            response = Response(status_code=500, detail=self.response_format.error(f"Failed to Generate SAS Token"))
            logger.error(response.detail)
            return response_data, response

        # Construct SAS URL with the correct blob path
        sas_url = f"https://{share_blob_client.primary_hostname}/{shared_container}/{share_blob_full_name}?{sas_token}"
        
        response_data = BlobShareServiceResponse(blob_sas_url=sas_url)
        response = Response(status_code=200, detail=self.response_format.ok(f"SAS URL generated successfully for {share_blob_full_name}"))

        return response_data, response