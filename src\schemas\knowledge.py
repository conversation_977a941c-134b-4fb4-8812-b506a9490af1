from pydantic import BaseModel, Field
import uuid
from datetime import datetime, timedelta

from ..settings import SETTINGS

# System-level Access
class SecretKnowledge(BaseModel):
    # Trace Information
    knowledge_id:                str | None = None
    knowledge_traceid:           str | None = None
    knowledge_name:              str | None = None
    knowledge_version:           int | None = None
    batch_order:                 str | None = None

    # Creator Information
    creator_id:                  str | None = None
    creator_name:                str | None = None
    approver_id:                 str | None = None
    approver_name:               str | None = None

    # PIL Information
    library_name_en:             str | None = None
    library_name_tc:             str | None = None
    category_name_en:            str | None = None
    category_name_tc:            str | None = None
    title_name_en:               str | None = None  
    title_name_tc:               str | None = None
    item_type:                   str | None = None
    item_url:                    str | None = None
    item_status:                 str | None = None
    document_id:                 str | None = None
    file_name:                   str | None = None
    file_description:            str | None = None
    file_created_datetime:       datetime | None = None
    file_last_modified_datetime: datetime | None = None
    group_id:                    str | None = None
    file_sync_up_url:            str | None = None
    library_id:                  str | None = None
    category_id:                 str | None = None
    reference_start_date:        datetime | None = None
    reference_end_date:          datetime | None = None

    # Category Information
    knowledge_group:             str | None = None
    knowledge_type:              str | None = None
    knowledge_location:          str | None = None
    storage_type:                str | None = None
    storage_type_origin:         str | None = None
    storage_provider:            str | None = None
    storage_provider_origin:     str | None = None
    storage_directory:           str | None = None
    storage_directory_origin:    str | None = None
    storage_secrets:             dict | None = None
    storage_secrets_origin:      dict | None = None

    # Control Information
    knowledge_status:            int | None = None
    knowledge_permission:        int | None = None
    knowledge_management:        int | None = None

    # Configuration
    knowledge_vectorstorage:     str  | None = None
    knowledge_vectorlocation:    str  | None = None
    knowledge_vectorinfo:        dict | None = None
    knowledge_graphstorage:      str  | None = None
    knowledge_graphlocation:     str  | None = None
    knowledge_graphinfo:         dict | None = None
    knowledge_searchstorage:     dict | None = None
    knowledge_searchinfo:        dict | None = None
    knowledge_secrets:           dict | None = None
    knowledge_record:            bool | None = None
    knowledge_key:               str  | None = None

    # Specification
    knowledge_confidence:        float | None = None
    knowledge_filename:          str   | None = None
    knowledge_fileextension:     str   | None = None
    knowledge_filesize:          float | None = None
    knowledge_description:       str   | None = None

    # Statistics
    processing_time:             float | None = None
    total_input_tokens:          int   | None = None
    total_output_tokens:         int   | None = None
    total_tool_tokens:           int   | None = None

    # Dependices
    knowledgeinput_id:           str  | None = None
    prepknow_id:                 str  | None = None
    graph_id:                    str  | None = None
    graph_config:                dict | None = None

    # Tags
    knowledge_searchstorages:    list[str] | None = None
    knowledge_sources:           list[str] | None = None
    knowledge_sourcetypes:       list[str] | None = None
    knowledge_contenttypes:      list[str] | None = None
    knowledge_cats:              list[str] | None = None
    knowledge_languages:         list[str] | None = None
    knowledge_tags:              list[str] | None = None
    user_groups:                 list[str] | None = None
    agent_groups:                list[str] | None = None

    # Time Information
    knowledge_issue_date:        datetime | None = None
    knowledge_effective_from:    datetime | None = None
    knowledge_effective_to:      datetime | None = None

    created_at:                  datetime | None = None
    updated_at:                  datetime | None = None


"""
    Knowledge Filter
"""   
class KnowledgeStringFilter(BaseModel):
    knowledge_id_filter:             list[str] | None = None
    knowledge_traceid_filter:        list[str] | None = None
    knowledge_name_filter:           list[str] | None = None
    batch_order_filter:              list[str] | None = None

    library_name_en_filter:          list[str] | None = None
    library_name_tc_filter:          list[str] | None = None
    category_name_en_filter:         list[str] | None = None
    category_name_tc_filter:         list[str] | None = None
    title_name_en_filter:            list[str] | None = None
    title_name_tc_filter:            list[str] | None = None
    item_type_filter:                list[str] | None = None
    item_url_filter:                 list[str] | None = None
    item_status_filter:              list[str] | None = None
    document_id_filter:              list[str] | None = None
    file_name_filter:                list[str] | None = None
    file_sync_up_url_filter:         list[str] | None = None
    library_id_filter:               list[str] | None = None
    category_id_filter:              list[str] | None = None


    creator_id_filter:               list[str] | None = None
    creator_name_filter:             list[str] | None = None
    approver_id_filter:              list[str] | None = None
    approver_name_filter:            list[str] | None = None

    knowledge_group_filter:          list[str] | None = None
    knowledge_type_filter:           list[str] | None = None
    knowledge_location_filter:       list[str] | None = None
    storage_type_filter:             list[str] | None = None
    storage_type_origin_filter:      list[str] | None = None
    storage_provider_filter:         list[str] | None = None
    storage_provider_origin_filter:  list[str] | None = None
    storage_directory_filter:        list[str] | None = None
    storage_directory_origin_filter: list[str] | None = None

    knowledge_vectorstorage_filter:  list[str] | None = None
    knowledge_vectorlocation_filter: list[str] | None = None
    knowledge_graphstorage_filter:   list[str] | None = None
    knowledge_graphlocation_filter:  list[str] | None = None
    knowledge_key_filter:            list[str] | None = None

    knowledge_filename_filter:       list[str] | None = None
    knowledge_fileextension_filter:  list[str] | None = None

    knowledgeinput_id_filter:        list[str] | None = None
    prepknow_id_filter:              list[str] | None = None
    graph_id_filter:                 list[str] | None = None

class KnowledgeNumericFilter(BaseModel):
    knowledge_version_min:    int | None = None
    knowledge_version_max:    int | None = None

    knowledge_status_min:     int | None = None
    knowledge_status_max:     int | None = None 
    knowledge_permission_min: int | None = None
    knowledge_permission_max: int | None = None
    knowledge_management_min: int | None = None
    knowledge_management_max: int | None = None

    knowledge_confidence_min: float | None = None
    knowledge_confidence_max: float | None = None
    knowledge_filesize_min:   float | None = None
    knowledge_filesize_max:   float | None = None

    processing_time_min:      float | None = None
    processing_time_max:      float | None = None
    total_input_tokens_min:   int   | None = None
    total_input_tokens_max:   int   | None = None
    total_output_tokens_min:  int   | None = None
    total_output_tokens_max:  int   | None = None
    total_tool_tokens_min:    int   | None = None
    total_tool_tokens_max:    int   | None = None


class KnowledgeListFilter(BaseModel):
    group_id_or:              list[str] | None = None
    group_id_and:              list[str] | None = None
    knowledge_searchstorages_or:  list[str] | None = None
    knowledge_searchstorages_and: list[str] | None = None
    knowledge_sources_or:         list[str] | None = None
    knowledge_sources_and:        list[str] | None = None
    knowledge_sourcetypes_or:     list[str] | None = None
    knowledge_sourcetypes_and:    list[str] | None = None
    knowledge_contenttypes_or:    list[str] | None = None
    knowledge_contenttypes_and:   list[str] | None = None
    knowledge_cats_or:            list[str] | None = None
    knowledge_cats_and:           list[str] | None = None
    knowledge_languages_or:       list[str] | None = None
    knowledge_languages_and:      list[str] | None = None
    knowledge_tags_or:            list[str] | None = None
    knowledge_tags_and:           list[str] | None = None
    user_groups_or:               list[str] | None = None
    user_groups_and:              list[str] | None = None
    agent_groups_or:              list[str] | None = None
    agent_groups_and:             list[str] | None = None


class KnowledgeDictionaryFilter(BaseModel):
    storage_secrets_or:          list[str] | None = None
    storage_secrets_and:         list[str] | None = None
    storage_secrets_origin_or:   list[str] | None = None
    storage_secrets_origin_and:  list[str] | None = None

    knowledge_vectorinfo_or:     list[str] | None = None
    knowledge_vectorinfo_and:    list[str] | None = None
    knowledge_graphinfo_or:      list[str] | None = None
    knowledge_graphinfo_and:     list[str] | None = None
    knowledge_searchstorage_or:  list[str] | None = None
    knowledge_searchstorage_and: list[str] | None = None
    knowledge_searchinfo_or:     list[str] | None = None
    knowledge_searchinfo_and:    list[str] | None = None
    knowledge_secrets_or:        list[str] | None = None
    knowledge_secrets_and:       list[str] | None = None

    graph_config_or:             list[str] | None = None
    graph_config_and:            list[str] | None = None

class KnowledgeBooleanFilter(BaseModel):
    knowledge_record_filter: bool | None = None

class KnowledgeDatetimeFilter(BaseModel):
    file_created_datetime_start:       datetime | None = None
    file_created_datetime_end:         datetime | None = None
    file_last_modified_datetime_start: datetime | None = None
    file_last_modified_datetime_end:   datetime | None = None
    reference_start_date_start:        datetime | None = None
    reference_start_date_end:          datetime | None = None
    reference_end_date_start:          datetime | None = None
    reference_end_date_end:            datetime | None = None

    knowledge_issue_date_start:        datetime  | None = None
    knowledge_issue_date_end:          datetime  | None = None
    knowledge_effective_from_start:    datetime  | None = None
    knowledge_effective_from_end:      datetime  | None = None
    knowledge_effective_to_start:      datetime  | None = None
    knowledge_effective_to_end:        datetime  | None = None

    created_at_start:                  datetime  | None = None
    created_at_end:                    datetime  | None = None
    updated_at_start:                  datetime  | None = None
    updated_at_end:                    datetime  | None = None

class KnowledgeByteFilter(BaseModel):
    not_used_filter: list[bytes] | None = None

class KnowledgeFilter(BaseModel):
    string_filter:     KnowledgeStringFilter     | None = None
    numeric_filter:    KnowledgeNumericFilter    | None = None
    list_filter:       KnowledgeListFilter       | None = None
    dictionary_filter: KnowledgeDictionaryFilter | None = None
    boolean_filter:    KnowledgeBooleanFilter    | None = None
    datetime_filter:   KnowledgeDatetimeFilter   | None = None
    byte_filter:       KnowledgeByteFilter       | None = None
    sorting:           dict={"knowledge_id": "asc"}
    filter_no:         int=-1


""" 
    Request and Resposne for System Access Knowledges
"""
class SystemKnowledgeRequest(BaseModel):
    knowledge_requestid: str=Field(default_factory=lambda: str(uuid.uuid4()))
    data_filter:         KnowledgeFilter | None = None

class SystemKnowledgeResponse(BaseModel):
    knowledge_requestid: str
    filtered_data:       list[SecretKnowledge]=[]
    data_count:          int=0