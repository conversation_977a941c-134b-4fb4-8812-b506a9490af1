from ....settings import SETTINGS

from ....logger.log_handler import get_logger

logger = get_logger(__name__)    

# # Import Functions if APP_FUNC == True
# if SETTINGS.BASE.APP_FUNC == True:
#     # Import Preprocessing Function
#     try:
#         module = __import__(SETTINGS.PREP.REQUEST_PREPKNOW_MODULE, fromlist=[SETTINGS.PREP.REQUEST_PREPKNOW_FUNC])
#         request_exec_prepknow  = getattr(module, SETTINGS.PREP.REQUEST_PREPKNOW_FUNC)
#         request_exec_prepmedia = getattr(module, SETTINGS.PREP.REQUEST_PREPMEDIA_FUNC)
#     except:
#         err_msg = f"Import Error : <{SETTINGS.BASE.APP_NAME}> Failed to Import <PreprocessingHub> Module"
#         logger.error(err_msg)
#         raise Exception(err_msg)