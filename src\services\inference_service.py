import inspect
from datetime import datetime, timezone
import time
import httpx
from openai import AzureOpenAI

from ..settings import SETTINGS

from ..schemas.format import (
    ResponseFormatter,
    Response
)

from ..schemas.inference import (
    InferenceInput,
    InferenceModel,
    InferenceResult,
    InferenceOutput,
    InferenceMetrics,
    InferenceRequest,
    InferenceResponse
)

from ..logger.log_handler import get_logger

logger = get_logger(__name__)

class InferenceServiceManager:
    response_format = ResponseFormatter(suffix = f"{inspect.stack()[0][3]}")

    default_inference_model = InferenceModel(
        model_id=SETTINGS.GEAI.MODEL_ID,
        model_version=SETTINGS.GEAI.MODEL_VERSION,
        model_name=SETTINGS.GEAI.MODEL_NAME,
        model_type=SETTINGS.GEAI.MODEL_TYPE,
        model_cluster=SETTINGS.GEAI.MODEL_CLUSTER,
        model_location=SETTINGS.GEAI.MODEL_LOCATION,
        model_host=SETTINGS.GEAI.MODEL_HOST,
        model_port=SETTINGS.GEAI.MODEL_PORT,
        model_api=SETTINGS.GEAI.MODEL_API,
        model_engine=SETTINGS.GEAI.MODEL_ENGINE,
        model_base=SETTINGS.GEAI.MODEL_BASE,
        model_parameters=SETTINGS.GEAI.MODEL_PARAMETERS,
        model_secrets=SETTINGS.GEAI.MODEL_SECRETS,
        model_file_path=SETTINGS.GEAI.MODEL_FILE_PATH,
        model_key=SETTINGS.GEAI.MODEL_KEY
    )

    default_system_prompt = ""

    def __init__(self, api_call: bool):
        self.api_call = api_call


    def inference_engine(self, request: InferenceRequest) -> tuple[InferenceResponse | None, Response]:
        if not request.model:
            request.model = self.default_inference_model

        if not request.system_prompt:
            request.system_prompt = self.default_system_prompt
        
        response_inference = InferenceResponse(**request.__dict__)

        if request.model.model_location.lower() == 'azure':
            inference_result, response = self.azure_server(request=request)  

        else:
            response = Response(status_code=404, detail=self.response_format.error(f"Input Error : <{SETTINGS.BASE.APP_NAME}> Unrecognized model location <{request.model.model_location}>"))
            response_inference.inference_metrics.inference_reason = response.detail
            response_inference.response_at = datetime.now(timezone.utc)
            return response_inference, response

        if inference_result.inference_metrics.inference_code == SETTINGS.INFR.STATUS_CODE.get("SUCCESS"):
            response_inference.inference_output  = inference_result.inference_output
            response_inference.inference_metrics = inference_result.inference_metrics
            logger.info(f'GenAI Response :\n{response_inference.inference_output.text}')
        else:
            response = Response(status_code=500, detail=response.detail)

        response_inference.response_at = datetime.now(timezone.utc)
        return response_inference, response

    def azure_server(self, request: InferenceRequest) -> tuple[InferenceResult, Response]:
        inference_result  = InferenceResult(inference_metrics=InferenceMetrics(inference_function=inspect.stack()[0][3]))
        inference_start = time.time()

        payload = dict()

        # Model Options
        options = dict()
        if request.model.model_parameters:
            options.update(request.model.model_parameters)
        if options:
            payload = options

        payload["model"] = request.model.model_base
        
        messages = []
        # System Prompt Formation
        if request.system_prompt:
            messages.append({"role": "system", "content": request.system_prompt})

        if request.chat_history:
            messages += [{"role": _chat.role, "content": _chat.content} for _chat in request.chat_history]

        # User Prompt Formation
        messages.append({"role": "user", "content": [{"type": "text", "text": request.input.text}]})
        payload["messages"] = messages

        api_url = f"{request.model.model_host}/{request.model.model_port}"
        genai_client = AzureOpenAI(
            api_key         = request.model.model_key,
            api_version     = request.model.model_secrets.get("api_version", ""),
            azure_endpoint  = api_url,
            default_headers = request.model.model_secrets.get("header", {})
        )

        # Post Request to Inference Server
        try:
            if "temperature" in payload:
                resp = genai_client.chat.completions.create(
                    model       =  payload["model"],
                    max_tokens  = payload.get("max_tokens", 3000),
                    temperature = payload.get("temperature", 0.3),
                    messages    =  payload["messages"]
                )
            else: 
                resp = genai_client.chat.completions.create(
                    model       =  payload["model"],
                    max_tokens  = payload.get("max_tokens", 3000),
                    messages    =  payload["messages"]
                )

            # Error during Calling Inference Server
            # if not resp or not resp.choices:
            #     response = Response(status_code=500, detail=self.response_format.error(f"Azure GenAI Error : <{SETTINGS.BASE.APP_NAME}> Failed to Retrieve GenAI Data from Azure Server", str(e)))
            #     inference_result.inference_metrics.inference_reason = response.detail
            #     logger.error(response.detail)
            #     return inference_result, response

            if not resp or not resp.choices:
                response = Response(
                    status_code=500,
                    detail=self.response_format.error(
                        f"Azure GenAI Error : <{SETTINGS.BASE.APP_NAME}> Failed to Retrieve GenAI Data from Azure Server"
                    )
                )
                inference_result.inference_metrics.inference_reason = response.detail
                logger.error(response.detail)
                return inference_result, response

            # Update Inference Ouptut
            inference_result.inference_output.__dict__.update(
                text = resp.choices[0].message.content
            )

            # Update Inference Metrics
            inference_result.inference_metrics.__dict__.update(
                inference_code    = SETTINGS.INFR.STATUS_CODE.get("SUCCESS"),
                inference_reason  = 'SUCCEED',
                input_tokens      = resp.usage.prompt_tokens,
                output_tokens     = resp.usage.completion_tokens,
                input_per_second  = resp.usage.prompt_tokens / time.time() - inference_start,
                output_per_second = resp.usage.completion_tokens / time.time() - inference_start,
                inference_time    = time.time() - inference_start,
                first_token_time  = time.time() - inference_start
            )
            logger.info(f"Input Tokens: {resp.usage.prompt_tokens}; Output Tokens: {resp.usage.completion_tokens}")
            
            response = Response(status_code=200, detail=self.response_format.ok(f"Inference Success : <{SETTINGS.BASE.APP_NAME}> Completed Inference"))
            logger.info(response.detail)

        except httpx.TimeoutException as e:
            response = Response(status_code=502, detail=self.response_format.error(f"Timeout Error : <{SETTINGS.BASE.APP_NAME}> Encountered Timeout Error when Connecting to Azure Server", str(e)))
            logger.error(response.detail)

        except httpx.HTTPError as e:
            response = Response(status_code=502, detail=self.response_format.error(f"Connection Error : <{SETTINGS.BASE.APP_NAME}> Encountered Connection Error when Connecting to Azure Server", str(e)))
            logger.error(response.detail)

        # Handle common exceptions that might occur
        except (BaseException, Exception) as e:
            response = Response(status_code=500, detail=self.response_format.error(f"Common Error : <{SETTINGS.BASE.APP_NAME}> Encountered Common Error when Calling Azure Server", str(e)))
            logger.error(response.detail)

        # Handle any other exceptions that might occur
        except:
            response = Response(status_code=500, detail=self.response_format.error(f"Unexpected Error : <{SETTINGS.BASE.APP_NAME}> Encountered Unexpected Error when Calling Azure Server", str(e)))
            logger.error(response.detail)

        return inference_result, response