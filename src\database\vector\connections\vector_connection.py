import os
import urllib.parse
from contextlib import contextmanager

from ....database.vector.models.vector_models import vb_schema, vb_schema_index_params

from ....settings import SETTINGS
from ....logger.log_handler import get_logger

logger = get_logger(__name__)

if SETTINGS.VTDB.FORM.upper() == "AISEARCH":
    from azure.core.credentials import AzureKeyCredential
    from azure.search.documents import SearchClient
    from azure.search.documents.indexes import SearchIndexClient
    from azure.search.documents.indexes.models import <PERSON>Index, SimpleField, SearchableField
    import azure.search.documents.indexes.models._edm as edm
    from azure.search.documents.indexes.models import SearchResourceEncryptionKey

    api_key          = SETTINGS.VTDB.PSWD
    index_name       = SETTINGS.VTDB.TABLE
    encrytion_config = SETTINGS.VTDB.CONFIG.get("encryption_config", None)
    
    # Create Search Index
    if encrytion_config:
        #encrytion_config = {"key_name": "ai-search-key", "key_version": "5da730552ec4405f9dbce6cfb2f363ae", "vault_uri": "https://kvahklifedevaz2z5oxk5001.vault.azure.net"}
        encryption_key = SearchResourceEncryptionKey(**encrytion_config)
        index = SearchIndex(
            name           = index_name, 
            fields         = vb_schema,
            vector_search  = vb_schema_index_params, 
            encryption_key = encryption_key
        )
    else:
        index = SearchIndex(
            name   = index_name, 
            fields = vb_schema,
            vector_search  = vb_schema_index_params
        )
    
    # Test DB Connection
    try:
        # Init Schema
        index_client = SearchIndexClient(    
            endpoint=SETTINGS.VTDB.HOST,
            credential=AzureKeyCredential(api_key)
        )

        try:
            index_client.get_index(index_name)
        except:
            index_client.create_or_update_index(index)

        # Init VB Client
        vb_client = SearchClient(
            endpoint   = SETTINGS.VTDB.HOST,
            index_name = index_name,
            credential = AzureKeyCredential(api_key)
        )

        logger.info(f"Connected : <{SETTINGS.BASE.APP_NAME}> <{SETTINGS.VTDB.NAME}> Vector Database")

    # Handle any operational errors that might occur
    except Exception as e:  
        err_msg = f"Connection Error : <{SETTINGS.BASE.APP_NAME}> <{SETTINGS.VTDB.NAME}> Vector Database"
        logger.error(err_msg)
        raise Exception(err_msg)
    
    # Handle any other exceptions that might occur
    except:
        err_msg = f"Unexpected Error : <{SETTINGS.BASE.APP_NAME}> <{SETTINGS.VTDB.NAME}> Vector Database"
        logger.error(err_msg)
        raise Exception(err_msg)

    # DB via Function Call
    @contextmanager
    def get_vb_func():
        try:
            yield vb_client
        except Exception as e:
            logger.error(f"Failed in Initializing Connection to Vector Database via Function Call --- {e}")
            raise e
            
    # DB via API Call
    def get_vb_api():
        try:
            return vb_client
        except Exception as e:
            logger.error(f"Failed in Initializing Connection to Vector Database via Function Call --- {e}")
            raise e

else:
    err_msg = f"Unknown DB Error : <{SETTINGS.VTDB.NAME}> Database"
    logger.error(err_msg)
    raise Exception(err_msg)