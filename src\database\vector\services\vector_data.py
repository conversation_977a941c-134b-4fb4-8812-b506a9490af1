import inspect

from typing import Any, Generator

from ....settings import SETTINGS

from ..schemas.format import (
    ResponseFormatter,
    Response,
    ComplexEncoder
)

from ..schemas.vector import(
    VectorCreateRequest, 
    VectorBatchCreateRequest,
    VectorUpdateRequest, 
    VectorRequest,
    VectorBatchRequest,
    UserVectorRequest,
    UserVectorResponse,
    SystemVectorRequest,
    SystemVectorResponse,
    VectorExportRequest
)

# from ..services.crypto_service import CryptoServiceManager

from ....database.vector.connections.vector_connection import get_vb_func, get_vb_api

# API DB Session
if SETTINGS.BASE.APP_API == True:
    vb_api = get_vb_api()
    default_api_call = True
else:
    vb_api = None
    default_api_call = False

# Function DB Session
if SETTINGS.BASE.APP_FUNC == True:
    vb_func = get_vb_func
else:
    vb_func = None


from ....logger.log_handler import get_logger

logger = get_logger(__name__)

# Init Manager Mapping
manager_map = {}
if SETTINGS.VTDB.FORM.upper() == "AISEARCH":
    from ..services.vb_aisearch_data import AISearchDataManager
    manager_map["AISEARCH"] = AISearchDataManager


class VectorDataManager:
    response_format = ResponseFormatter(suffix = f"{inspect.stack()[0][3]}")

    # default_backup_config = BackupConfig(
    #     format=SETTINGS.BKUP.FORM,
    #     location=SETTINGS.BKUP.LOCA,
    #     name=SETTINGS.BKUP.NAME,
    #     host=SETTINGS.BKUP.HOST,
    #     port=SETTINGS.BKUP.PORT,
    #     user=SETTINGS.BKUP.USER,
    #     pswd=SETTINGS.BKUP.PSWD,
    #     table=SETTINGS.BKUP.TABLE,
    #     rdir=SETTINGS.BKUP.RDIR,
    #     sdir=SETTINGS.BKUP.SDIR,
    #     limit=SETTINGS.BKUP.LIMIT
    # )

    # default_export_config = IOConfig(
    #     format=SETTINGS.EXPT.FORM,
    #     location=SETTINGS.EXPT.LOCA,
    #     name=SETTINGS.EXPT.NAME,
    #     host=SETTINGS.EXPT.HOST,
    #     port=SETTINGS.EXPT.PORT,
    #     user=SETTINGS.EXPT.USER,
    #     pswd=SETTINGS.EXPT.PSWD,
    #     table=SETTINGS.EXPT.TABLE,
    #     rdir=SETTINGS.EXPT.RDIR,
    #     sdir=SETTINGS.EXPT.SDIR,
    #     file_rdir=SETTINGS.EXPT.FILE_RDIR,
    #     file_sdir=SETTINGS.EXPT.FILE_SDIR,
    #     file_name=SETTINGS.EXPT.FILE_NAME
    # )

    def __init__(
            self, 
            api_call:        bool | None = default_api_call,
            vb_api:          Any  | None = vb_api, 
            vb_func:         Generator | None = vb_func, 
            vector_storage:  str=SETTINGS.VTDB.FORM, 
            vector_location: str=SETTINGS.VTDB.LOCA, 
            vector_config:   dict=SETTINGS.VTDB.CONFIG,
        ):
        self.api_call        = api_call
        self.vb_api          = vb_api
        self.vb_func         = vb_func
        self.vector_storage  = vector_storage
        self.vector_location = vector_location
        self.vector_config   = vector_config

        self.manager = manager_map.get(self.vector_storage.upper(), None)

    """
        General Operation
    """
    # Create
    def create(
            self, 
            request: VectorCreateRequest
        ) -> Response:
        
        try:
            response = self.manager(
                api_call        = self.api_call,
                vb_api          = self.vb_api, 
                vb_func         = self.vb_func, 
                vector_storage  = self.vector_storage,
                vector_location = self.vector_location,
                vector_config   = self.vector_config
            ).create(request=request)

        except Exception as e:
            response = Response(status_code=500, detail=self.response_format.error(f"Common Error : <{SETTINGS.BASE.APP_NAME}> Encountered Error in Vector Data Managment", str(e)))
            logger.error(response.detail)
        except:
            response = Response(status_code=500, detail=self.response_format.error(f"Unexpected Error : <{SETTINGS.BASE.APP_NAME}> Encounter Unexpected Error in Vector Data Managment"))
            logger.error(response.detail)
                
        return response

    # Batch Create
    def batch_create(self, request: VectorBatchCreateRequest) -> Response:

        try:
            response = self.manager(
                api_call        = self.api_call,
                vb_api          = self.vb_api, 
                vb_func         = self.vb_func, 
                vector_storage  = self.vector_storage,
                vector_location = self.vector_location,
                vector_config   = self.vector_config
            ).batch_create(request=request)

        except:
            response = Response(status_code=500, detail=self.response_format.error(f"Unexpected Error : <{SETTINGS.BASE.APP_NAME}> Encounter Unexpected Error in Vector Data Managment"))

        return response

    # Update
    def update(self, request: VectorUpdateRequest) -> Response:

        try:
            response = self.manager(
                api_call        = self.api_call,
                vb_api          = self.vb_api, 
                vb_func         = self.vb_func, 
                vector_storage  = self.vector_storage,
                vector_location = self.vector_location,
                vector_config   = self.vector_config
            ).update(request=request)

        except:
            response = Response(status_code=500, detail=self.response_format.error(f"Unexpected Error : <{SETTINGS.BASE.APP_NAME}> Encounter Unexpected Error in Vector Data Managment"))

        return response

    # Activate
    def activate(self, request: VectorRequest) -> Response:

        try:
            response = self.manager(
                api_call        = self.api_call,
                vb_api          = self.vb_api, 
                vb_func         = self.vb_func, 
                vector_storage  = self.vector_storage,
                vector_location = self.vector_location,
                vector_config   = self.vector_config
            ).activate(request=request)

        except:
            response = Response(status_code=500, detail=self.response_format.error(f"Unexpected Error : <{SETTINGS.BASE.APP_NAME}> Encounter Unexpected Error in Vector Data Managment"))

        return response

    # Batch Activate
    def batch_activate(self, request: VectorBatchRequest) -> Response:

        try:
            response = self.manager(
                api_call        = self.api_call,
                vb_api          = self.vb_api, 
                vb_func         = self.vb_func, 
                vector_storage  = self.vector_storage,
                vector_location = self.vector_location,
                vector_config   = self.vector_config
            ).batch_activate(request=request)

        except:
            response = Response(status_code=500, detail=self.response_format.error(f"Unexpected Error : <{SETTINGS.BASE.APP_NAME}> Encounter Unexpected Error in Vector Data Managment"))

        return response

    # Deactivate
    def deactivate(self, request: VectorRequest) -> Response:

        try:
            response = self.manager(
                api_call        = self.api_call,
                vb_api          = self.vb_api, 
                vb_func         = self.vb_func, 
                vector_storage  = self.vector_storage,
                vector_location = self.vector_location,
                vector_config   = self.vector_config
            ).deactivate(request=request)

        except:
            response = Response(status_code=500, detail=self.response_format.error(f"Unexpected Error : <{SETTINGS.BASE.APP_NAME}> Encounter Unexpected Error in Vector Data Managment"))

        return response

    # Batch Deactivate
    def batch_deactivate(self, request: VectorBatchRequest) -> Response:

        try:
            response = self.manager(
                api_call        = self.api_call,
                vb_api          = self.vb_api, 
                vb_func         = self.vb_func, 
                vector_storage  = self.vector_storage,
                vector_location = self.vector_location,
                vector_config   = self.vector_config
            ).batch_deactivate(request=request)

        except:
            response = Response(status_code=500, detail=self.response_format.error(f"Unexpected Error : <{SETTINGS.BASE.APP_NAME}> Encounter Unexpected Error in Vector Data Managment"))

        return response
    
    # Delete
    def delete(self, request: VectorRequest) -> Response:

        try:
            response = self.manager(
                api_call        = self.api_call,
                vb_api          = self.vb_api, 
                vb_func         = self.vb_func, 
                vector_storage  = self.vector_storage,
                vector_location = self.vector_location,
                vector_config   = self.vector_config
            ).delete(request=request)

        except:
            response = Response(status_code=500, detail=self.response_format.error(f"Unexpected Error : <{SETTINGS.BASE.APP_NAME}> Encounter Unexpected Error in Vector Data Managment"))

        return response

    # Batch Delete
    def batch_delete(self, request: VectorBatchRequest) -> Response:

        try:
            response = self.manager(
                api_call        = self.api_call,
                vb_api          = self.vb_api, 
                vb_func         = self.vb_func, 
                vector_storage  = self.vector_storage,
                vector_location = self.vector_location,
                vector_config   = self.vector_config
            ).batch_delete(request=request)

        except:
            response = Response(status_code=500, detail=self.response_format.error(f"Unexpected Error : <{SETTINGS.BASE.APP_NAME}> Encounter Unexpected Error in Vector Data Managment"))

        return response

    # Drop
    def drop(self, request: VectorRequest) -> Response:

        try:
            response = self.manager(
                api_call        = self.api_call,
                vb_api          = self.vb_api, 
                vb_func         = self.vb_func, 
                vector_storage  = self.vector_storage,
                vector_location = self.vector_location,
                vector_config   = self.vector_config
            ).drop(request=request)

        except:
            response = Response(status_code=500, detail=self.response_format.error(f"Unexpected Error : <{SETTINGS.BASE.APP_NAME}> Encounter Unexpected Error in Vector Data Managment"))

        return response

    # Batch Drop
    def batch_drop(self, request: VectorBatchRequest) -> Response:
        try:
            response = self.manager(
                api_call        = self.api_call,
                vb_api          = self.vb_api, 
                vb_func         = self.vb_func, 
                vector_storage  = self.vector_storage,
                vector_location = self.vector_location,
                vector_config   = self.vector_config
            ).batch_drop(request=request)

        except:
            response = Response(status_code=500, detail=self.response_format.error(f"Unexpected Error : <{SETTINGS.BASE.APP_NAME}> Encounter Unexpected Error in Vector Data Managment"))

        return response


    """
        Systme Operation
    """
    def drop_inactive_by_system(self) -> Response:
        try:
            response = self.manager(
                api_call        = self.api_call,
                vb_api          = self.vb_api, 
                vb_func         = self.vb_func, 
                vector_storage  = self.vector_storage,
                vector_location = self.vector_location,
                vector_config   = self.vector_config
            ).drop_inactive_by_system()

        except:
            response = Response(status_code=500, detail=self.response_format.error(f"Unexpected Error : <{SETTINGS.BASE.APP_NAME}> Encounter Unexpected Error in Vector Data Managment"))

        return response

    def query_data_by_system(self, request: SystemVectorRequest) -> tuple[SystemVectorResponse, Response]:
        response_data = SystemVectorResponse(**request.__dict__)
        try:
            response_data, response = self.manager(
                api_call        = self.api_call,
                vb_api          = self.vb_api, 
                vb_func         = self.vb_func, 
                vector_storage  = self.vector_storage,
                vector_location = self.vector_location,
                vector_config   = self.vector_config
            ).query_data_by_system(request=request)

        except:
            response = Response(status_code=500, detail=self.response_format.error(f"Unexpected Error : <{SETTINGS.BASE.APP_NAME}> Encounter Unexpected Error in Vector Data Managment"))

        return response_data, response

    def export_data_by_system(self, request: VectorExportRequest) -> Response:
        try:
            response = self.manager(
                api_call        = self.api_call,
                vb_api          = self.vb_api, 
                vb_func         = self.vb_func, 
                vector_storage  = self.vector_storage,
                vector_location = self.vector_location,
                vector_config   = self.vector_config
            ).export_data_by_system(request=request)

        except:
            response = Response(status_code=500, detail=self.response_format.error(f"Unexpected Error : <{SETTINGS.BASE.APP_NAME}> Encounter Unexpected Error in Vector Data Managment"))

        return response


    """
        User Operation
    """
    def query_data_by_user(self, request: UserVectorRequest) -> tuple[UserVectorResponse, Response]:
        response_data = UserVectorResponse(**request.__dict__)
        try:
            response_data, response = self.manager(
                api_call        = self.api_call,
                vb_api          = self.vb_api, 
                vb_func         = self.vb_func, 
                vector_storage  = self.vector_storage,
                vector_location = self.vector_location,
                vector_config   = self.vector_config
            ).query_data_by_system(request=request)

        except:
            response = Response(status_code=500, detail=self.response_format.error(f"Unexpected Error : <{SETTINGS.BASE.APP_NAME}> Encounter Unexpected Error in Vector Data Managment"))

        return response_data, response



    # def export_data_by_system(self, request: KnowledgeExportRequest) -> Response:
    #     manager = manager_map.get(self.vector_storage.upper(), None)
    #     if manager is None:
    #         response = Response(status_code=404, detail=self.response_format.error(f"Vector Manager Not Found : {SETTINGS.BASE.APP_NAME} Cannot Find Vector Manager for Processing"))

    #     else:
    #         try:
    #             response = manager(
    #                 vb_api          = self.vb_api, 
    #                 vb_func         = self.vb_func, 
    #                 api_call        = self.api_call,
    #                 vector_storage  = self.vector_storage,
    #                 vector_location = self.vector_location,
    #                 vector_config   = self.vector_config
    #             ).export_data_by_system(request=request)

    #         except:
    #             response = Response(status_code=500, detail=self.response_format.error(f"Unexpected Error : <{SETTINGS.BASE.APP_NAME}> Encounter Unexpected Error in Vector Data Managment"))

    #     return response
    
    # def list_by_knowledge_id(self, request: ListDataByKnowledgeReRequest) -> tuple[VectorRetrieveOutput, Response]:
    #     response_data = VectorRetrieveOutput()
    #     manager = manager_map.get(self.vector_storage.upper(), None)
    #     if manager is None:
    #         response = Response(
    #             status_code=404,
    #             detail=self.response_format.error(
    #                 f"Vector Manager Not Found : {SETTINGS.BASE.APP_NAME} Cannot Find Vector Manager for Processing"
    #             )
    #         )
    #     else:
    #         try:
    #             response_data, response = manager(
    #                 vb_api=self.vb_api,
    #                 vb_func=self.vb_func,
    #                 api_call=self.api_call,
    #                 vector_storage=self.vector_storage,
    #                 vector_location=self.vector_location,
    #                 vector_config=self.vector_config
    #             ).list_by_knowledge_id(request=request)
    #         except Exception as e:
    #             response = Response(
    #                 status_code=500,
    #                 detail=self.response_format.error(
    #                     f"Unexpected Error : <{SETTINGS.BASE.APP_NAME}> Encounter Unexpected Error in Vector Data Management",
    #                     e
    #                 )
    #             ) 
    #     return response_data, response
    


    # def retrieve(self,request: VectorRetrieveInput) -> tuple[VectorRetrieveOutput, Response]:
    #     manager = manager_map.get(self.vector_storage.upper(), None)
    #     if manager is None:
    #         raise ValueError(f"Vector Manager Not Found")
    #     return manager(
    #         vb_api=self.vb_api,
    #         vb_func=self.vb_func,
    #         api_call=self.api_call,
    #         vector_storage=self.vector_storage,
    #         vector_location=self.vector_location,
    #         vector_config=self.vector_config
    #     ).retrieve(request=request)

    # # Update
    # def update(self, request: VectorUpdateRequest) -> Response:
        
    #     # Retrieve data from database
    #     vb_data = self.get_data_by_id(
    #         id      = request.data_id, 
    #         active  = False
    #     )

    #     # Check if data exists in database
    #     if not vb_data:
    #         response = Response(status_code=404, detail=self.response_format.error(f"Unfound Error : Updating Vector <data_id: {request.data_id}>"))
    #         logger.error(response.detail)
    #         return response

    #     else:
    #         update_data = {key: value for key, value in request.update_data.__dict__.items() if value is not None}

    #         if not update_data:
    #             response = Response(status_code=404, detail=self.response_format.error(f"Unfound Error : Invalid Update Vector"))
    #             logger.error(response.detail)
    #             return response

    #         new_data = VectorCreate(**vb_data.to_dict())
    #         new_data.__dict__.update(update_data)
    #         if "vector_version" not in update_data.keys() or request.overwrite == False:
    #             new_data.vector_version += 1

    #         # Validate parameters with creator role
    #         response = self.verify_vector_content(is_admin=request.is_admin, data=new_data)
    #         if response:
    #             return response
            
    #         # Reset Parameters
    #         new_data.updated_at = None
            
    #         new_vb_data = VectorDB(**new_data.__dict__)
            
    #         try:
    #             old_id = str(uuid.uuid4())
    #             if self.api_call == True:
    #                 vb = self.vb_api
    #                 # Deactivate previous version
    #                 vb.query(VectorDB).filter(VectorDB.data_id==vb_data.data_id).update({VectorDB.data_id: old_id, VectorDB.vector_status: 0})
    #                 vb.commit()

    #                 # Add new version
    #                 vb.add(new_vb_data)
    #                 vb.commit()
    #                 vb.refresh(new_vb_data)

    #                 if request.overwrite == True:
    #                     vb.query(VectorDB).filter(VectorDB.data_id==old_id).delete()
    #                     vb.commit()

    #             else:
    #                 old_id = str(uuid.uuid4())
    #                 with self.vb_func() as vb:
    #                     # Deactivate previous version
    #                     vb.query(VectorDB).filter(VectorDB.data_id==vb_data.data_id).update({VectorDB.data_id: old_id, VectorDB.vector_status: 0})
    #                     vb.commit()

    #                     # Add new version
    #                     vb.add(new_vb_data)
    #                     vb.commit()
    #                     vb.refresh(new_vb_data)

    #                     if request.overwrite == True:
    #                         vb.query(VectorDB).filter(VectorDB.data_id==old_id).delete()
    #                         vb.commit()

    #             response = Response(status_code=200, detail=self.response_format.ok(f"Success : Updated Vector <data_id: {new_data.data_id}>"))
    #             logger.info(response.detail)

    #         # Handle duplicate errors
    #         except IntegrityError as e:
    #             response = Response(status_code=400, detail=self.response_format.error(f"Duplicate Error : Vector Already Registered <data_id: {new_data.data_id}>", str(e)))
    #             logger.error(response.detail)

    #         # Handle any operational errors that might occur
    #         except OperationalError as e:
    #             response = Response(status_code=502, detail=self.response_format.error(f"Database Connection Error : Updating Vector <data_id: {new_data.data_id}>", str(e)))
    #             logger.error(response.detail)

    #         # Handle common exceptions that might occur
    #         except (BaseException, Exception) as e:
    #             response = Response(status_code=500, detail=self.response_format.error(f"Common Error : Updating Vector <data_id: {new_data.data_id}>", str(e)))
    #             logger.error(response.detail)

    #         # Handle any other exceptions that might occur
    #         except:
    #             response = Response(status_code=500, detail=self.response_format.error(f"Unexpected Error : Updating Vector <data_id: {new_data.data_id}>"))
    #             logger.error(response.detail)

    #     return response

    # # Activate
    # def activate(self, request: VectorRequest) -> Response:
    #     try:
    #         vb_data = self.get_data_by_id(
    #             id      = request.data_id, 
    #             active  = False
    #         )

    #         if vb_data:
    #             if self.api_call == True:
    #                 vb = self.vb_api
    #                 vb.query(VectorDB).filter(VectorDB.data_id==vb_data.data_id).update({VectorDB.vector_status: 1})
    #                 vb.commit()
    #             else:
    #                 with self.vb_func() as vb:
    #                     vb.query(VectorDB).filter(VectorDB.data_id==vb_data.data_id).update({VectorDB.vector_status: 1})
    #                     vb.commit()

    #             response = Response(status_code=200, detail=self.response_format.ok(f"Success : Activated Vector <data_id: {request.data_id}>"))
    #             logger.info(response.detail)
            
    #         else:
    #             response = Response(status_code=404, detail=self.response_format.error(f"Unfound Error : Activating Vector <data_id: {request.data_id}>"))
    #             logger.error(response.detail)

    #     # Handle any operational errors that might occur
    #     except OperationalError as e:
    #         response = Response(status_code=502, detail=self.response_format.error(f"Database Connection Error : Activating Vector <data_id: {request.data_id}>", str(e)))
    #         logger.error(response.detail)

    #     # Handle common exceptions that might occur
    #     except (BaseException, Exception) as e:
    #         response = Response(status_code=500, detail=self.response_format.error(f"Common Error : Activating Vector <data_id: <data_id: {request.data_id}>", str(e)))
    #         logger.error(response.detail)

    #     # Handle any other exceptions that might occur
    #     except:
    #         response = Response(status_code=500, detail=self.response_format.error(f"Unexpected Error : Activating Vector <data_id: {request.data_id}>"))
    #         logger.error(response.detail)

    #     return response

    # # Batch Activate
    # def batch_activate(self, request: VectorBatchRequest) -> Response:
    #     conditions = []
    #     try:
    #         for data in request.vector_requests:
    #             vb_data = self.get_data_by_id(
    #                 id      = data.data_id, 
    #                 active  = False
    #             )

    #             # Check if data exists
    #             if vb_data:
    #                 conditions.append(VectorDB.data_id==vb_data.data_id)
    #             else:
    #                 response = Response(status_code=404, detail=self.response_format.error(f"Unfound Error : Activating Vector <data_id: {data.data_id}> <data_id: {data.data_id}>"))
    #                 logger.error(response.detail)
    #                 return response

    #         # Update status
    #         if self.api_call == True:
    #             vb = self.vb_api
    #             vb.query(VectorDB).filter(*conditions).update({VectorDB.vector_status: 1})
    #             vb.commit()
    #         else:
    #             with self.vb_func() as vb:
    #                 vb.query(VectorDB).filter(*conditions).update({VectorDB.vector_status: 1})
    #                 vb.commit()

    #         response = Response(status_code=200, detail=self.response_format.ok(f"Success : Batch Activated <{len(request.vector_requests)}> Vectors"))
    #         logger.info(response.detail)
        
    #     # Handle any operational errors that might occur
    #     except OperationalError as e:
    #         response = Response(status_code=502, detail=self.response_format.error(f"Database Connection Error : Batch Activating Vector", str(e)))
    #         logger.error(response.detail)

    #     # Handle common exceptions that might occur
    #     except (BaseException, Exception) as e:
    #         response = Response(status_code=500, detail=self.response_format.error(f"Common Error : Batch Activating Vector", str(e)))
    #         logger.error(response.detail)

    #     # Handle any other exceptions that might occur
    #     except:
    #         response = Response(status_code=500, detail=self.response_format.error(f"Unexpected Error : Batch Activating Vector"))
    #         logger.error(response.detail)

    #     return response

    # # Deactivate
    # def deactivate(self, request: VectorRequest) -> Response:
    #     try:
    #         vb_data = self.get_data_by_id(
    #             id      = request.data_id, 
    #             active  = True
    #         )

    #         if vb_data:
    #             if self.api_call == True:
    #                 vb = self.vb_api
    #                 vb.query(VectorDB).filter(VectorDB.data_id==vb_data.data_id).update({VectorDB.vector_status: 0})
    #                 vb.commit()
    #             else:
    #                 with self.vb_func() as vb:
    #                     vb.query(VectorDB).filter(VectorDB.data_id==vb_data.data_id).update({VectorDB.vector_status: 0})
    #                     vb.commit()

    #             response = Response(status_code=200, detail=self.response_format.ok(f"Success : Deactivated Vector <data_id: {vb_data.data_id}>"))
    #             logger.info(response.detail)
            
    #         else:
    #             response = Response(status_code=404, detail=self.response_format.error(f"Unfound Error : Deactivating Vector <data_id: {request.data_id}>"))
    #             logger.error(response.detail)

    #     # Handle any operational errors that might occur
    #     except OperationalError as e:
    #         response = Response(status_code=502, detail=self.response_format.error(f"Database Connection Error : Deactivating Vector <data_id: {request.data_id}>", str(e)))
    #         logger.error(response.detail)

    #     # Handle common exceptions that might occur
    #     except (BaseException, Exception) as e:
    #         response = Response(status_code=500, detail=self.response_format.error(f"Common Error : Deactivating Vector <data_id: {request.data_id}>", str(e)))
    #         logger.error(response.detail)

    #     # Handle any other exceptions that might occur
    #     except:
    #         response = Response(status_code=500, detail=self.response_format.error(f"Unexpected Error : Deactivating Vector <data_id: {request.data_id}>"))
    #         logger.error(response.detail)

    #     return response

    # # Batch Deactivate
    # def batch_deactivate(self, request: VectorBatchRequest) -> Response:
    #     conditions = []
    #     try:
    #         for data in request.vector_requests:
    #             vb_data = self.get_data_by_id(
    #                 id      = data.data_id, 
    #                 active  = True
    #             )

    #             # Check if data exists
    #             if vb_data:
    #                 conditions.append(VectorDB.data_id==vb_data.data_id)
    #             else:
    #                 response = Response(status_code=404, detail=self.response_format.error(f"Unfound Error : Deactivating Vector <data_id: {data.data_id}> <data_id: {data.data_id}>"))
    #                 logger.error(response.detail)
    #                 return response

    #         # Update status
    #         if self.api_call == True:
    #             vb = self.vb_api
    #             vb.query(VectorDB).filter(*conditions).update({VectorDB.vector_status: 0})
    #             vb.commit()
    #         else:
    #             with self.vb_func() as vb:
    #                 vb.query(VectorDB).filter(*conditions).update({VectorDB.vector_status: 0})
    #                 vb.commit()

    #         response = Response(status_code=200, detail=self.response_format.ok(f"Success : Batch Deactivated <{len(request.vector_requests)}> Vectors"))
    #         logger.info(response.detail)
        
    #     # Handle any operational errors that might occur
    #     except OperationalError as e:
    #         response = Response(status_code=502, detail=self.response_format.error(f"Database Connection Error : Batch Deactivating Vector", str(e)))
    #         logger.error(response.detail)

    #     # Handle common exceptions that might occur
    #     except (BaseException, Exception) as e:
    #         response = Response(status_code=500, detail=self.response_format.error(f"Common Error : Batch Deactivating Vector", str(e)))
    #         logger.error(response.detail)

    #     # Handle any other exceptions that might occur
    #     except:
    #         response = Response(status_code=500, detail=self.response_format.error(f"Unexpected Error : Batch Deactivating Vector"))
    #         logger.error(response.detail)

    #     return response

    # # Delete
    # def delete(self, request: VectorRequest) -> Response:
    #     response = self.deactivate(request=request)
    #     return response

    # # Batch Delete
    # def batch_delete(self, request: VectorBatchRequest) -> Response:
    #     response = self.batch_deactivate(request=request)
    #     return response

    # # Drop
    # def drop(self, request: VectorRequest) -> Response:
    #     try:
    #         vb_data = self.get_data_by_id(
    #             id      = request.data_id, 
    #             active  = False
    #         )
            
    #         if vb_data:
    #             if self.api_call == True:
    #                 vb = self.vb_api
    #                 vb.query(VectorDB).filter(VectorDB.data_id==vb_data.data_id).delete()   
    #                 vb.commit()
    #             else:
    #                 with self.vb_func() as vb:
    #                     vb.query(VectorDB).filter(VectorDB.data_id==vb_data.data_id).delete()   
    #                     vb.commit()

    #             response = Response(status_code=200, detail=self.response_format.ok(f"Success : Dropped Vector <data_id: {vb_data.data_id}>"))
    #             logger.info(response.detail)
            
    #         else:
    #             response = Response(status_code=404, detail=self.response_format.error(f"Unfound Error : Dropping Vector <data_id: {request.data_id}>"))
    #             logger.error(response.detail)

    #     # Handle any operational errors that might occur
    #     except OperationalError as e:
    #         response = Response(status_code=502, detail=self.response_format.error(f"Database Connection Error : Dropping Vector <data_id: {request.data_id}>", str(e)))
    #         logger.error(response.detail)

    #     # Handle common exceptions that might occur
    #     except (BaseException, Exception) as e:
    #         response = Response(status_code=500, detail=self.response_format.error(f"Common Error : Dropping Vector <data_id: {request.data_id}>", str(e)))
    #         logger.error(response.detail)

    #     # Handle any other exceptions that might occur
    #     except:
    #         response = Response(status_code=500, detail=self.response_format.error(f"Unexpected Error : Dropping Vector <data_id: {request.data_id}>"))
    #         logger.error(response.detail)

    #     return response

    # # Batch Drop
    # def batch_drop(self, request: VectorBatchRequest) -> Response:
    #     conditions = []
    #     try:
    #         for data in request.vector_requests:
    #             vb_data = self.get_data_by_id(
    #                 id      = data.data_id, 
    #                 active  = False
    #             )

    #             # Check if data exists
    #             if vb_data:
    #                 conditions.append(VectorDB.data_id==vb_data.data_id)
    #             else:
    #                 response = Response(status_code=404, detail=self.response_format.error(f"Unfound Error : Dropping Vector <data_id: {data.data_id}> <data_id: {data.data_id}>"))
    #                 logger.error(response.detail)
    #                 return response

    #         # Delete data
    #         if self.api_call == True:
    #             vb = self.vb_api
    #             vb.query(VectorDB).filter(VectorDB.data_id==vb_data.data_id).delete()   
    #             vb.commit()
    #         else:
    #             with self.vb_func() as vb:
    #                 vb.query(VectorDB).filter(VectorDB.data_id==vb_data.data_id).delete()   
    #                 vb.commit()

    #         response = Response(status_code=200, detail=self.response_format.ok(f"Success : Batch Dropped <{len(request.vector_requests)}> Vectors"))
    #         logger.info(response.detail)
        
    #     # Handle any operational errors that might occur
    #     except OperationalError as e:
    #         response = Response(status_code=502, detail=self.response_format.error(f"Database Connection Error : Batch Dropping Vector", str(e)))
    #         logger.error(response.detail)

    #     # Handle common exceptions that might occur
    #     except (BaseException, Exception) as e:
    #         response = Response(status_code=500, detail=self.response_format.error(f"Common Error : Batch Dropping Vector", str(e)))
    #         logger.error(response.detail)

    #     # Handle any other exceptions that might occur
    #     except:
    #         response = Response(status_code=500, detail=self.response_format.error(f"Unexpected Error : Batch Dropping Vector"))
    #         logger.error(response.detail)

    #     return response


    # """
    #     System Operation
    # """
    # # System Drop All Inactive
    # def drop_inactive_by_system(self) -> Response:
    #     try:
    #         if self.api_call == True:
    #             vb = self.vb_api
    #             vb.query(VectorDB).filter(VectorDB.vector_status==0).delete() 
    #             vb.commit()
    #         else:
    #             with self.vb_func() as vb:
    #                 vb.query(VectorDB).filter(VectorDB.vector_status==0).delete() 
    #                 vb.commit()

    #         response = Response(status_code=200, detail=self.response_format.ok("Success : Dropped All Inactive Vectors"))
    #         logger.info(response.detail)

    #     # Handle any operational errors that might occur
    #     except OperationalError as e:
    #         response = Response(status_code=502, detail=self.response_format.error(f"Database Connection Error : Dropping Inactive Vectors", str(e)))
    #         logger.error(response.detail)

    #     # Handle common exceptions that might occur
    #     except (BaseException, Exception) as e:
    #         response = Response(status_code=500, detail=self.response_format.error(f"Common Error : Dropping Inactive Vectors", str(e)))
    #         logger.error(response.detail)

    #     # Handle any other exceptions that might occur
    #     except:
    #         response = Response(status_code=500, detail=self.response_format.error(f"Unexpected Error : Dropping Inactive Vectors"))
    #         logger.error(response.detail)

    #     return response

    # # System Query Data
    # def query_data_by_system(self, request: SystemVectorRequest) -> tuple[SystemVectorResponse, Response]:
    #     response_data = SystemVectorResponse(**request.__dict__)

    #     if request.vector_filter:
    #         data_filter = VectorFilter(**request.vector_filter.__dict__)
    #     else:
    #         data_filter = VectorFilter()

    #     conditions, sorting, filter_no = self.filter_formatter(data_filter=data_filter)
    #     vb_data, response              = self.query_with_conditions(conditions=conditions, sorting=sorting, filter_no=filter_no)

    #     if vb_data:
    #         response_data.__dict__.update(filtered_vectors=[SecretVector(**data.to_dict()) for data in vb_data], vector_no=len(vb_data))
    #         response = Response(status_code=200, detail=self.response_format.ok("Success : Get Filtered Data for System"))
       
    #     return response_data, response

    # def import_data_by_system(self, request: VectorImportRequest) -> Response:
    #     config = request.io_config
    #     if request.backup == True:
    #         response = self.backup_data_by_system(request=VectorBackupRequest())

    #         if response.status_code >= SETTINGS.STAT.SUCC_CODE_END:
    #             response = Response(status_code=500, detail=self.response_format.error(f"Import Error : Failed to Backup Current DB before Import"))
    #             logger.error(response.detail)
    #             return response 
            
    #     response = self.import_data(config=config)

    #     return response

    # # System Export Data
    # def export_data_by_system(self, request: VectorExportRequest) -> Response:

    #     # Get Data
    #     if request.vector_filter:
    #         data_filter = VectorFilter(**request.vector_filter.__dict__)
    #     else:
    #         data_filter = VectorFilter()

    #     conditions, sorting, filter_no = self.filter_formatter(data_filter=data_filter)
    #     vb_data, response              = self.query_with_conditions(conditions=conditions, sorting=sorting, filter_no=filter_no)

    #     if vb_data:
    #         filtered_data = [SecretVector(**data.to_dict()).__dict__ for data in vb_data]
    #         response = Response(status_code=200, detail=self.response_format.ok("Success : Get Filtered Data for System"))
    #     else:
    #         return response # Return No Data

    #     response = self.export_data(config=request.io_config, data=filtered_data, include_datetime=request.include_datetime)

    #     return response    

    # # System Backup Data
    # def backup_data_by_system(self, request: VectorBackupRequest) -> Response:
    #     # Get Data
    #     if request.vector_filter:
    #         data_filter = VectorFilter(**request.vector_filter.__dict__)
    #     else:
    #         data_filter = VectorFilter()

    #     conditions, sorting, filter_no = self.filter_formatter(data_filter=data_filter)
    #     vb_data, response              = self.query_with_conditions(conditions=conditions, sorting=sorting, filter_no=filter_no)

    #     if vb_data:
    #         filtered_data = [SecretVector(**data.to_dict()).__dict__ for data in vb_data]
    #         response = Response(status_code=200, detail=self.response_format.ok("Success : Get Filtered Data for System"))
    #     else:
    #         return response # Return No Data

    #     response = self.backup_data(config=request.backup_config, data=filtered_data)
    
    #     return response  

    # # System List Backup Table Name
    # def list_backup_by_system(self, request: VectorBackupListRequest) -> tuple[VectorBackupListResponse, Response]:
    #     response_table = VectorBackupListResponse(**request.__dict__)
    #     config=request.backup_config

    #     # Detect Backup Configuration
    #     if config is None:
    #         config = self.default_backup_config.copy()
    #         response = Response(status_code=200, detail=self.response_format.ok("Info : Empty Backup Configuration >>> Use System Default Backup Configuration"))
    #         logger.info(response.detail)

    #     # Check DB Format
    #     if not config.format.upper() in SETTINGS.BKUP.DB_FORM:
    #         response = Response(status_code=500, detail=self.response_format.error("Data Backup Error : Invalid Data Backup Format"))
    #         logger.error(response.detail)       
    #         return response_table, response 
         
    #     # Load Backup DB Config
    #     try:
    #         table_prefix = config.table
    #         vb_config = BackupDatabaseConfiguration(**config.__dict__)
    #         engine = init_bkup_vb_engine(vb_config) # init engine
    #         _table_list = sqlalchemy.inspect(engine).get_table_names()
    #         table_list  = [table for table in _table_list if table_prefix in table]
            
    #         if table_list:
    #             table_list.sort(reverse=True)
    #             response_table.table_list = table_list
    #         response = Response(status_code=200, detail=self.response_format.ok("Success : Retrieved Table List from Backup DB"))
    #         return response_table, response
        
    #     except:
    #         response = Response(status_code=500, detail=self.response_format.error(f"Unexpected Error : Failed to Init Backup DB <{vb_config.name}>"))
    #         logger.error(response.detail)
    #         return response_table, response

    # # System Restore Data
    # def restore_backup_by_system(self, request: VectorRestoreRequest) -> Response:
    #     config = request.restore_config
    #     response = self.restore_data(config=config)
    #     return response


    # """
    #     User Operation
    # """
    # def query_data_by_user(self, request: UserVectorRequest) -> tuple[UserVectorResponse, Response]:
    #     response_data = UserVectorResponse(**request.__dict__)

    #     data_filter = VectorFilter(**request.vector_filter.__dict__)
    #     conditions, sorting, filter_no = self.filter_formatter(data_filter=data_filter)

    #     if not conditions:
    #         response = Response(status_code=500, detail=self.response_format.error(f"Missing Filter Error : Filter cannot be All Empty"))
    #         logger.error(response.detail)
    #         return response_data, response

    #     else:
    #         vb_data, response = self.query_with_conditions(conditions=conditions, sorting=sorting, filter_no=filter_no)

    #     if vb_data:
    #         response_data.__dict__.update(filtered_vectors=[Vector(**data.to_dict()) for data in vb_data])
    #         response = Response(status_code=200, detail=self.response_format.ok("Success : Get Permitted Data for User"))
       
    #     return response_data, response

    # def list_data_by_user(self, request: UserVectorListRequest) -> tuple[UserVectorListResponse, Response]:
    #     response_data = UserVectorResponse(**request.__dict__)

    #     # Get default vectors
    #     data_filter = VectorFilter(vector_group_filter=["default"], vector_permission_min=request.user_permission)
    #     conditions, sorting, filter_no = self.filter_formatter(data_filter=data_filter)
    #     vb_data, response              = self.query_with_conditions(conditions=conditions, sorting=sorting, filter_no=filter_no)
    #     if vb_data:
    #         response_data.__dict__.update(default_vectors=[Vector(**data.to_dict()) for data in vb_data])

    #     # Get user vectors
    #     data_filter = VectorFilter(vector_group_filter=["user"], creator_ids=[request.user_id])
    #     conditions, sorting, filter_no = self.filter_formatter(data_filter=data_filter)
    #     vb_data, response              = self.query_with_conditions(conditions=conditions, sorting=sorting, filter_no=filter_no)
    #     if vb_data:
    #         response_data.__dict__.update(user_vectors=[Vector(**data.to_dict()) for data in vb_data])

    #     # Get group vectors
    #     group_vectors = []
    #     group_management_vectors = []
    #     for group in request.user_groups:
    #         # Get general group vectors
    #         data_filter = VectorFilter(vector_group_filter=["group"], user_group_and=[group], vector_permission_max=group.group_permission)
    #         conditions, sorting, filter_no = self.filter_formatter(data_filter=data_filter)
    #         vb_data, response              = self.query_with_conditions(conditions=conditions, sorting=sorting, filter_no=filter_no)
    #         if vb_data:
    #             group_vectors += [Vector(**data.to_dict()) for data in vb_data if group.group_permission < data.vector_management]
            
    #         # Get management-levl group vectors
    #         data_filter = VectorFilter(vector_group_filter=["group"], user_group_and=[group], vector_management_max=group.group_permission)
    #         conditions, sorting, filter_no = self.filter_formatter(data_filter=data_filter)
    #         vb_data, response              = self.query_with_conditions(conditions=conditions, sorting=sorting, filter_no=filter_no)
    #         if vb_data:
    #             group_management_vectors += [Vector(**data.to_dict()) for data in vb_data]

    #     response_data.__dict__.update(group_vectors=group_vectors, group_management_vectors=group_management_vectors)
    
    #     return response_data, response
 

    # """
    #     Class Operation
    # """
    # # Import Data
    # def import_data(self, config: IOConfig | None) -> Response:         
        
    #     # 1. Detect IO Configuration
    #     if config is None:
    #         response = Response(status_code=500, detail=self.response_format.ok("Import Error : Empty Import Configuration"))
    #         logger.error(response.detail)
    #         return response

    #     # 2.1. Init Import File
    #     if config.format.upper() in SETTINGS.IMPT.FILE_FORM:
    #         try:
    #             file_extn = '.' + config.format.lower()
    #             if file_extn in config.file_name.lower() and len(config.file_name.lower().split(file_extn)) == 2:
    #                 file_name = config.file_name
    #             else:
    #                 file_name = config.file_name + file_extn

    #             if not config.file_sdir:
    #                 file_root = config.file_rdir
    #             else:
    #                 file_root = os.path.join(config.file_rdir, config.file_sdir)

    #             file_path = os.path.join(file_root, file_name)

    #         # Handle missing parameters
    #         except TypeError as e:
    #             response = Response(status_code=500, detail=self.response_format.error(f"Missing Parameter Error : Importing Data", str(e)))
    #             logger.error(response.detail)
    #             return response

    #         # Handle any other exceptions that might occur
    #         except:
    #             response = Response(status_code=500, detail=self.response_format.error(f"Unexpected Error : Importing Data"))
    #             logger.error(response.detail)
    #             return response

    #         if not os.path.isfile(file_path):
    #             response = Response(status_code=500, detail=self.response_format.error(f"Import Error : Cannot Find Import File {file_path}"))
    #             logger.error(response.detail)
    #             return response
        
    #     # 2.2. Init Import DB
    #     elif config.format.upper() in SETTINGS.IMPT.DB_FORM:
    #         try:
    #             vb_config = IODatabaseConfiguration(**config.__dict__)
    #             engine = init_io_vb_engine(vb_config) # init engine

    #         except:
    #             response = Response(status_code=500, detail=self.response_format.error(f"Import Error : Failed to Init Import DB <{vb_config.name}>"))
    #             logger.error(response.detail)
    #             return response

    #     # 3. Import Data
    #     # 3.1. Import to JSON
    #     if config.format.upper() == 'JSON':
    #         try:
    #             with open(file_path, 'r') as json_file:
    #                 io_data = json.load(json_file)
            
    #             response = Response(status_code=200, detail=self.response_format.ok(f"Import in Progress : Retrieved Data from <{file_path}>"))
    #             logger.info(response.detail)

    #         except:
    #             response = Response(status_code=500, detail=self.response_format.error(f"Data Import Error : Failed to Retrieve Data from <{file_path}>"))
    #             logger.error(response.detail)
    #             return response
            
    #     # 3.2. Import to CSV
    #     elif config.format.upper() == 'CSV':
    #         try:
    #             with open(file_path, mode='r', newline='') as csv_file:
    #                 reader = csv.DictReader(csv_file)  # Use DictReader to create dictionaries
    #                 io_data = [row for row in reader] 

    #             response = Response(status_code=200, detail=self.response_format.ok(f"Import in Progress : Retrieved Data from <{file_path}>"))
    #             logger.info(response.detail)

    #         except:
    #             response = Response(status_code=500, detail=self.response_format.error(f"Data Import Error : Failed to Retrieve Data from <{file_path}>"))
    #             logger.error(response.detail)    
    #             return response
    
    #     # 3.3. Import to DB
    #     elif config.format.upper() in SETTINGS.IMPT.DB_FORM: # Valid DB
    #         _table_list = sqlalchemy.inspect(engine).get_table_names()
    #         _table_name = [table for table in _table_list if table == config.table]
            
    #         # Check if Table Exists
    #         if not _table_name:
    #             response = Response(status_code=500, detail=self.response_format.error("Data Import Error : Cannot Find the Table in Import DB"))
    #             logger.error(response.detail)
    #             return response
        
    #         # Retrieve Import Data
    #         try:
    #             table_name = _table_name[0]
    #             IOVectorDB = set_io_vb_table(table_name=table_name, engine=engine)
    #             with get_io_vb_func(engine=engine) as vb:
    #                 _io_data = vb.query(IOVectorDB).all()
    #             io_data = [VectorCreate(**_data.__dict__) for _data in _io_data]
    #             response = Response(status_code=200, detail=self.response_format.ok("Import in Progress : Retrieved Data from Import DB"))
    #             logger.info(response.detail)

    #         except:
    #             response = Response(status_code=500, detail=self.response_format.error(f"Unexpected Error : Failed to Get Data from Import DB <{vb_config.name}>"))
    #             logger.error(response.detail)
    #             return response

    #     # 3.X. Unknown Import Format
    #     else:
    #         response = Response(status_code=500, detail=self.response_format.error("Data Import Error : Invalid Data Import Format"))
    #         logger.error(response.detail)       
    #         return response 

    #     # 4. Remove Current Data from DB
    #     try:
    #         if self.api_call == True:
    #             vb = self.vb_api
    #             vb.query(VectorDB).delete()
    #             vb.commit()
    #         else:
    #             with self.vb_func() as vb:
    #                 vb.query(VectorDB).delete()
    #                 vb.commit()

    #         response = Response(status_code=200, detail=self.response_format.ok("Import in Progress : Removed Current Data from DB"))
    #         logger.info(response.detail)
            
    #     # 4.X. Handle any operational errors that might occur
    #     except OperationalError as e:
    #         response = Response(status_code=502, detail=self.response_format.error(f"Database Connection Error : Removing Current Data from DB during Importing Data", str(e)))
    #         logger.error(response.detail)
    #         return response

    #     # 4.X. Handle common exceptions that might occur
    #     except (BaseException, Exception) as e:
    #         response = Response(status_code=500, detail=self.response_format.error(f"Common Error : Removing Current Data from DB during Importing Data", str(e)))
    #         logger.error(response.detail)
    #         return response

    #     # 4.X. Handle any other exceptions that might occur
    #     except:
    #         response = Response(status_code=500, detail=self.response_format.error(f"Unexpected Error : Removing Current Data from DB during Importing Data"))
    #         logger.error(response.detail)
    #         return response

    #     # 5. Add Data to Current DB
    #     response = self.batch_create(data_list=io_data)
    #     if response.status_code < SETTINGS.STAT.SUCC_CODE_END:
    #         response = Response(status_code=200, detail=self.response_format.ok(f"Success : Imported Data from Import DB <{config.table}>"))
    #         logger.info(response.detail)
    #     else:
    #         response = Response(status_code=500, detail=self.response_format.error(f"Unexpected Error : Importing Data to Current DB"))
    #         logger.error(response.detail)

    #     return response

    # # Export Data
    # def export_data(self, config: IOConfig | None, data: list, include_datetime: bool=True) -> Response:         
        
    #     # 1. Detect Export Configuration
    #     if config is None:
    #         config = self.default_export_config.copy()
    #         response = Response(status_code=200, detail=self.response_format.ok("Info : Empty Export Configuration >>> Use System Default Export Configuration"))
    #         logger.info(response.detail)

    #     # 2.1. Init Export File
    #     if config.format.upper() in SETTINGS.EXPT.FILE_FORM:
    #         try:
    #             if include_datetime == True:
    #                 file_name = config.file_name + '_' + datetime.now(timezone.utc).strftime("%Y-%m-%d-%H-%M-%S")
    #             file_name = '.'.join([file_name, config.format.lower()])

    #             if not config.file_sdir:
    #                 file_root = config.file_rdir
    #             else:
    #                 file_root = os.path.join(config.file_rdir, config.file_sdir)

    #             file_path = os.path.join(file_root, file_name)

    #         # Handle missing parameters
    #         except TypeError as e:
    #             response = Response(status_code=500, detail=self.response_format.error(f"Missing Parameter Error : Exporting Data", str(e)))
    #             logger.error(response.detail)
    #             return response

    #         # Handle any other exceptions that might occur
    #         except:
    #             response = Response(status_code=500, detail=self.response_format.error(f"Unexpected Error : Exporting Data"))
    #             logger.error(response.detail)
    #             return response

    #         try:
    #             os.makedirs(file_root, exist_ok=True)
    #         except:
    #             response = Response(status_code=500, detail=self.response_format.error(f"Unexpected Error : Failed to Create Directory when Exporting Data"))
    #             logger.error(response.detail)
    #             return response
        
    #     # 2.2. Init Export DB
    #     elif config.format.upper() in SETTINGS.EXPT.DB_FORM:
    #         try:
    #             if include_datetime == True:
    #                 config.table = config.table + '_' + datetime.now(timezone.utc).strftime("%Y-%m-%d-%H-%M-%S")
            
    #         # Handle missing parameters
    #         except TypeError as e:
    #             response = Response(status_code=500, detail=self.response_format.error(f"Missing Parameter Error : Exporting Data", str(e)))
    #             logger.error(response.detail)
    #             return response

    #         # Handle any other exceptions that might occur
    #         except:
    #             response = Response(status_code=500, detail=self.response_format.error(f"Unexpected Error : Exporting Data"))
    #             logger.error(response.detail)
    #             return response
 
    #     # 2.3. Handle Unknown Format
    #     else:
    #         response = Response(status_code=500, detail=self.response_format.error("Data Export Error : Invalid Data Export Format"))
    #         logger.error(response.detail)       
    #         return response 

    #     # 3. Export Data
    #     # 3.1. Export to JSON
    #     if config.format.upper() == 'JSON':
    #         try:
    #             with open(file_path, 'w') as json_file:
    #                 json.dump(data, json_file, cls=ComplexEncoder, indent=4)
            
    #             response = Response(status_code=200, detail=self.response_format.ok(f"Success : Exported Data to <{file_path}>"))
    #             logger.info(response.detail)

    #         except:
    #             response = Response(status_code=500, detail=self.response_format.error(f"Data Export Error : Failed to Export Data to <{file_path}>"))
    #             logger.error(response.detail)
            
    #     # 3.2. Export to CSV
    #     elif config.format.upper() == 'CSV':
    #         try:
    #             with open(file_path, 'w',  newline='') as csv_file:
    #                 fieldnames = data[0].keys()
    #                 writer = csv.DictWriter(csv_file, fieldnames=fieldnames)
    #                 writer.writeheader()  # Write the header
    #                 writer.writerows(data)  # Write the data
    #             response = Response(status_code=200, detail=self.response_format.ok(f"Success : Exported Data to <{file_path}>"))
    #             logger.info(response.detail)

    #         except:
    #             response = Response(status_code=500, detail=self.response_format.error(f"Data Export Error : Failed to Export Data to <{file_path}>"))
    #             logger.error(response.detail)      
            
    #     # 3.3. Export to DB
    #     elif config.format.upper() in SETTINGS.EXPT.DB_FORM: # Valid DB
    #         vb_config = IODatabaseConfiguration(**config.__dict__)

    #         try:
    #             engine = init_io_vb_engine(vb_config) # init engine
    #             IOVectorDB = set_io_vb_table(table_name=vb_config.table, engine=engine) # set table name
    #             vb_data = [IOVectorDB(**VectorCreate(**_data).__dict__) for _data in data]
            
    #         except:
    #             response = Response(status_code=500, detail=self.response_format.error(f"Unexpected Error : Failed to Init Export DB <{vb_config.name}>"))
    #             logger.error(response.detail)
    #             return response

    #         try:
    #             with get_io_vb_func(engine=engine) as vb:
    #                 vb.add_all(vb_data)
    #                 vb.commit()
    #             response = Response(status_code=201, detail=self.response_format.ok(f"Success : Exported Data to DB <{vb_config.table}>"))
    #             logger.info(response.detail)
            
    #         except:
    #             response = Response(status_code=500, detail=self.response_format.error(f"Unexpected Error : Failed to Export Data to DB <{vb_config.table}>"))
    #             logger.error(response.detail)

    #     # 3.X. Unknown Export Format
    #     else:
    #         response = Response(status_code=500, detail=self.response_format.error("Data Export Error : Invalid Data Export Format"))
    #         logger.error(response.detail)       

    #     return response

    # # Backup Data
    # def backup_data(self, config: BackupConfig | None, data: list) -> Response:         
        
    #     # 1. Detect Backup Configuration
    #     if config is None:
    #         config = self.default_backup_config.copy()
    #         response = Response(status_code=200, detail=self.response_format.ok("Info : Empty Backup Configuration >>> Use System Default Backup Configuration"))
    #         logger.info(response.detail)

    #     # 2. Check DB Format
    #     if not config.format.upper() in SETTINGS.BKUP.DB_FORM:
    #         response = Response(status_code=500, detail=self.response_format.error("Data Backup Error : Invalid Data Backup Format"))
    #         logger.error(response.detail)       
    #         return response 
         
    #     # Load Backup DB Config
    #     try:
    #         table_prefix = config.table
    #         config.table = config.table + '_' + datetime.now(timezone.utc).strftime("%Y-%m-%d-%H-%M-%S")
    #         vb_config = BackupDatabaseConfiguration(**config.__dict__)
    #         engine = init_bkup_vb_engine(vb_config) # init engine
    #         BackupVectorDB = set_bkup_vb_table(table_name=vb_config.table, engine=engine) # set table name
    #         vb_data = [BackupVectorDB(**VectorCreate(**_data).__dict__) for _data in data]
        
    #     except:
    #         response = Response(status_code=500, detail=self.response_format.error(f"Unexpected Error : Failed to Init Backup DB <{vb_config.name}>"))
    #         logger.error(response.detail)
    #         return response

    #     # Add Data to Backup DB
    #     try:
    #         with get_bkup_vb_func(engine=engine) as vb:
    #             vb.add_all(vb_data)
    #             vb.commit()
    #         response = Response(status_code=201, detail=self.response_format.ok(f"Success : Backuped Data to DB <{vb_config.table}>"))
    #         logger.info(response.detail)

    #     except:
    #         response = Response(status_code=500, detail=self.response_format.error(f"Unexpected Error : Failed to Backup Data to DB <{vb_config.table}>"))
    #         logger.error(response.detail)

    #         return response

    #     # Check if Backup Limit is Reached
    #     if config.limit and config.limit > 0:
    #         logger.info(f"Backup Progress : Backup Limit <{config.limit}> is Set. Start Dropping Previous Backup Tables")
    #         _table_list = sqlalchemy.inspect(engine).get_table_names()
    #         table_list  = [table for table in _table_list if table_prefix in table]
    #         table_list.sort(reverse=True)

    #         if len(table_list) > config.limit:
    #             table_to_drop = table_list[config.limit:]
    #             for table_name in table_to_drop:
    #                 try:
    #                     with get_bkup_vb_func(engine=engine) as vb:
    #                         table = get_bkup_vb_table(table_name=table_name, engine=engine)
    #                         table.drop(engine)
    #                         vb.commit()
    #                     response = Response(status_code=200, detail=self.response_format.ok(f"Success : Dropped Backup DB Table <{table_name}>"))
    #                     logger.info(response.detail)
                    
    #                 except:
    #                     response = Response(status_code=500, detail=self.response_format.error(f"Unexpected Error : Failed to Drop Backup DB Table <{table_name}>"))
    #                     logger.error(response.detail)
    #                     return response
                    
    #             response = Response(status_code=200, detail=self.response_format.ok(f"Completed Backup : Dropped Previous <{len(table_list)-config.limit}> Tables during Data Backup. <{len(sqlalchemy.inspect(engine).get_table_names())}> Backup Tables Remained"))
    #             logger.info(response.detail)    

    #         else:
    #             response = Response(status_code=200, detail=self.response_format.ok(f"Completed Backup : Number of Backup Table is Less than Backup Limit. No Actions are Required"))
    #             logger.info(response.detail)

    #     return response

    # # Restore Data
    # def restore_data(self, config: RestoreConfig | None) -> Response:         

    #     # 1. Detect Restore Configuration
    #     # Check if table name is given (table_name denotes backup version)
    #     if config.table is None:
    #         response = Response(status_code=404, detail=self.response_format.error("Data Restore Error : Unfound Table Name"))
    #         logger.error(response.detail)
    #         return response
    #     else:
    #         _table = config.table

    #     # Check if Use Default Restore DB Config
    #     vb_config = {key: value for key, value in config.__dict__.items() if key != "table" and value is not None}
    #     if not vb_config:
    #         config = self.default_backup_config.copy()
    #         config.table = _table

    #         response = Response(status_code=200, detail=self.response_format.ok("Info : Empty Restore Configuration >>> Use System Default Restore Configuration"))
    #         logger.info(response.detail)

    #     # Check DB Format
    #     if not config.format.upper() in SETTINGS.BKUP.DB_FORM:
    #         response = Response(status_code=500, detail=self.response_format.error("Data Restore Error : Invalid Data Restore Format"))
    #         logger.error(response.detail)
    #         return response

    #     # 2. Init Restore DB 
    #     try:
    #         vb_config = BackupDatabaseConfiguration(**config.__dict__)
    #         engine = init_bkup_vb_engine(vb_config) # init engine
    #         _table_list = sqlalchemy.inspect(engine).get_table_names()
    #         _table_name = [table for table in _table_list if table == config.table]
        
    #     except:
    #         response = Response(status_code=500, detail=self.response_format.error(f"Unexpected Error : Failed to Init Backup DB <{vb_config.name}>"))
    #         logger.error(response.detail)
    #         return response

    #     # 3. Check if Table Exists
    #     if not _table_name:
    #         response = Response(status_code=500, detail=self.response_format.error("Data Restore Error : Cannot Find the Table in Backup DB"))
    #         logger.error(response.detail)
    #         return response

    #     # 4. Retrieve Data from BackupDB
    #     try:
    #         table_name = _table_name[0]
    #         BackupVectorDB = set_bkup_vb_table(table_name=table_name, engine=engine)
    #         with get_bkup_vb_func(engine=engine) as vb:
    #             backup_data = vb.query(BackupVectorDB).all()
    #         restore_data = [VectorCreate(**_data.__dict__) for _data in backup_data]
    #         response = Response(status_code=200, detail=self.response_format.ok("Restore in Progress : Retrieved Data from Backup DB"))
    #         logger.info(response.detail)

    #     except:
    #         response = Response(status_code=500, detail=self.response_format.error(f"Unexpected Error : Failed to Get Data from Backup DB <{vb_config.name}>"))
    #         logger.error(response.detail)
    #         return response

    #     # 6. Remove Current Data from DB
    #     try:
    #         if self.api_call == True:
    #             vb = self.vb_api
    #             vb.query(VectorDB).delete()
    #             vb.commit()
    #         else:
    #             with self.vb_func() as vb:
    #                 vb.query(VectorDB).delete()
    #                 vb.commit()

    #         response = Response(status_code=200, detail=self.response_format.ok("Restore in Progress : Removed Current Data from DB"))
    #         logger.info(response.detail)
            
    #     # Handle any operational errors that might occur
    #     except OperationalError as e:
    #         response = Response(status_code=502, detail=self.response_format.error(f"Database Connection Error : Removing Current Data from DB during Restoring Backup", str(e)))
    #         logger.error(response.detail)
    #         return response

    #     # Handle common exceptions that might occur
    #     except (BaseException, Exception) as e:
    #         response = Response(status_code=500, detail=self.response_format.error(f"Common Error : Removing Current Data from DB during Restoring Backup", str(e)))
    #         logger.error(response.detail)
    #         return response

    #     # Handle any other exceptions that might occur
    #     except:
    #         response = Response(status_code=500, detail=self.response_format.error(f"Unexpected Error : Removing Current Data from DB during Restoring Backup"))
    #         logger.error(response.detail)
    #         return response

    #     # 7. Add Restore Data to Current DB
    #     response = self.batch_create(data_list=restore_data)
    #     if response.status_code < SETTINGS.STAT.SUCC_CODE_END:
    #         response = Response(status_code=200, detail=self.response_format.ok(f"Success : Restored Data from Backup DB <{config.table}>"))
    #         logger.info(response.detail)
    #     else:
    #         response = Response(status_code=500, detail=self.response_format.error(f"Unexpected Error : Restoring Data to Current DB"))
    #         logger.error(response.detail)

    #     return response

    # # Verify Content
    # def verify_vector_content(self, is_admin: bool, data: VectorCreate) -> Response | None:
    #     # try:
    #     #     for item in [data.vector_parameters, data.vector_secrets]:
    #     #         if item: 
    #     #             if isinstance(item, dict):
    #     #                 for para, para_config in item.items():
    #     #                     for key, value in para_config.items():
    #     #                         if is_admin == False and key in SETTINGS.CSTR.ADMIN_PARAM_KEY:
    #     #                             response = Response(status_code=500, detail=self.response_format.error(f"Input Error : Invalid Permission for using Key <{key}>"))
    #     #                             logger.error(response.detail)
    #     #                             return response
                                
    #     #                         # if key in SETTINGS.CSTR.VALID_PARAM_KEY:
    #     #                         #     if value not in SETTINGS.CSTR.VALID_PARAM_VAL:
    #     #                         #         response = Response(status_code=500, detail=self.response_format.error(f"Input Error : Invalid Parameter Value <{value}>"))
    #     #                         #         logger.error(response.detail)      
    #     #                         #         return response                              

    #     #                         elif key not in SETTINGS.CSTR.ADMIN_PARAM_KEY:
    #     #                             response = Response(status_code=500, detail=self.response_format.error(f"Input Error : Invalid Parameter Key <{key}>"))
    #     #                             logger.error(response.detail)
    #     #                             return response

    #     #             else:
    #     #                 response = Response(status_code=500, detail=self.response_format.error(f"Input Error : Parameter Must be Either None or Dict"))
    #     #                 logger.error(response.detail)
    #     #                 return response

    #     # except:
    #     #     response = Response(status_code=500, detail=self.response_format.error(f"Unexpected Error : Validating Parameters for Vector"))
    #     #     logger.error(response.detail)
    #     #     return response

    #     return None

    # # Get Vector by ID or Name
    # def get_data_by_id(self, id: str=None, active: bool=True) -> VectorDB | None:
    #     conditions = []

    #     if id:
    #         conditions.append(VectorDB.data_id == id)
    #     else:
    #         return None

    #     if active:
    #         conditions.append(VectorDB.vector_status == 1)
        
    #     if self.api_call == True:
    #         vb = self.vb_api
    #         vb_data = vb.query(VectorDB).filter(*conditions).order_by(VectorDB.vector_version.desc()).first()

    #     else:
    #         with self.vb_func() as vb:
    #             vb_data = vb.query(VectorDB).filter(*conditions).order_by(VectorDB.vector_version.desc()).first()

    #     return vb_data

    # # Perform Query
    # def query_with_conditions(self, conditions: list, sorting: list, filter_no: int) -> tuple[list[VectorDB] | None, Response]:
    #     vb_data = None

    #     if not conditions:
    #         response = Response(status_code=500, detail=self.response_format.error("Condition Filter Error : Condition Filter is Empty"))
    #         logger.error(response.detail)
    #         return vb_data, response

    #     try:
    #         if self.api_call == True:
    #             vb = self.vb_api
    #             if not filter_no or filter_no < 0:
    #                 vb_data = vb.query(VectorDB).filter(and_(*conditions)).order_by(*sorting).all()
    #             else:
    #                 vb_data = vb.query(VectorDB).filter(and_(*conditions)).order_by(*sorting).limit(filter_no).all()
    #         else:
    #             with self.vb_func() as vb:
    #                 if not filter_no or filter_no < 0:
    #                     vb_data = vb.query(VectorDB).filter(and_(*conditions)).order_by(*sorting).all()
    #                 else:
    #                     vb_data = vb.query(VectorDB).filter(and_(*conditions)).order_by(*sorting).limit(filter_no).all()
            
    #         if vb_data:
    #             response = Response(status_code=200, detail=self.response_format.ok("Success : Retrieved Filtered Vectors from DB"))
    #         else:
    #             response = Response(status_code=200, detail=self.response_format.ok("Success : No Matched Vectors from DB"))

    #     # Handle any operational errors that might occur
    #     except OperationalError as e:
    #         response = Response(status_code=502, detail=self.response_format.error(f"Database Connection Error : Getting Vectors", str(e)))
    #         logger.error(response.detail)

    #     # Handle common exceptions that might occur
    #     except (BaseException, Exception) as e:
    #         response = Response(status_code=500, detail=self.response_format.error(f"Common Error : Getting Vectors", str(e)))
    #         logger.error(response.detail)

    #     # Handle any other exceptions that might occur
    #     except:
    #         response = Response(status_code=500, detail=self.response_format.error(f"Unexpected Error : Getting Vectors"))
    #         logger.error(response.detail)

    #     return vb_data, response

    # # Filter Formatter
    # def filter_formatter(self, data_filter: VectorFilter) -> tuple[list, list, int]:

    #     def filter_by_entity(vb_model: VectorDB, column_name: str, entity_filter: list[str]) -> list:
    #         conditions = []

    #         try:
    #             column = getattr(vb_model, '_' + column_name)
    #         except AttributeError as e:
    #             response = Response(status_code=404, detail=self.response_format.error(f"Attribute Error : Unrecognized Attribute <{column_name}>. The Attribute for ListFilter must be <key_or> or <key_and>", str(e)))
    #             logger.error(response.detail)
    #             return conditions
    #         except Exception:
    #             response = Response(status_code=500, detail=self.response_format.error(f"Unexpected Error : Unexpected Error during Parsing Attribute <{column_name}> in ListFilter"))
    #             logger.error(response.detail)       
    #             return conditions
            
    #         for entity in entity_filter:
    #             # Create conditions to match the tag surrounded by delimiters
    #             conditions += [
    #                 column.like(f'%{SETTINGS.DATB.SEP}{entity}{SETTINGS.DATB.SEP}%'),  # Middle
    #                 column.like(f'{entity}{SETTINGS.DATB.SEP}%'),  # Start
    #                 column.like(f'%{entity}'),  # End
    #                 column == entity               # Exact match (if no delimiters)
    #             ]
            
    #         return conditions

    #     def filter_by_byte(vb_model: VectorDB, column_name: str, entity_filter: list[str]) -> list:
    #         conditions = []

    #         try:
    #             column = getattr(vb_model, '_' + column_name)
    #         except AttributeError as e:
    #             response = Response(status_code=404, detail=self.response_format.error(f"Attribute Error : Unrecognized Attribute <{column_name}>. The Attribute for ByteFilter must be <key_filter>", str(e)))
    #             logger.error(response.detail)
    #             return conditions
    #         except Exception:
    #             response = Response(status_code=500, detail=self.response_format.error(f"Unexpected Error : Unexpected Error during Parsing Attribute <{column_name}> in ByteFilter"))
    #             logger.error(response.detail)       
    #             return conditions
            
    #         _encoded_values = []
    #         for value in entity_filter:
    #             encoded_value, response = CryptoServiceManager(api_call=False).encode_content(content=value)
    #             if response.status_code >= SETTINGS.STAT.SUCC_CODE_END:
    #                 logger.error(response.detail)
    #                 return conditions
    #             _encoded_values.append(encoded_value)
            
    #         conditions = [column.in_(_encoded_values)]
            
    #         return conditions

    #     conditions = []
    #     sorting    = []

    #     # Parsing conditions

    #     # String Filter
    #     if data_filter.string_filter is not None:
    #         string_suffix = '_filter'
    #         for key, value in data_filter.string_filter.__dict__.items():
    #             if value is not None:
    #                 column_name = key.split(string_suffix)[0]
    #                 try:
    #                     conditions.append(getattr(VectorDB, column_name).in_(value))
    #                 except AttributeError as e:
    #                     response = Response(status_code=404, detail=self.response_format.error(f"Attribute Error : Unrecognized Attribute <{column_name}>. The Attribute for StringFilter must be <key_filter>", str(e)))
    #                     logger.error(response.detail)
    #                 except Exception:
    #                     response = Response(status_code=500, detail=self.response_format.error(f"Unexpected Error : Unexpected Error during Parsing Attribute <{column_name}> in StringFilter"))
    #                     logger.error(response.detail)                    


    #     # Numeric Filter
    #     if data_filter.numeric_filter is not None:
    #         min_suffix = '_min'
    #         max_suffix = '_max'
    #         for key, value in data_filter.numeric_filter.__dict__.items():
    #             if value is not None:
    #                 if min_suffix in key:
    #                     column_name = key.split(min_suffix)[0]
    #                     try:
    #                         conditions.append(getattr(VectorDB, column_name) >= value)
    #                     except AttributeError as e:
    #                         response = Response(status_code=404, detail=self.response_format.error(f"Attribute Error : Unrecognized Attribute <{column_name}>. The Attribute for NumericFilter must be <key_min> or <key_max>", str(e)))
    #                         logger.error(response.detail)
    #                     except Exception:
    #                         response = Response(status_code=500, detail=self.response_format.error(f"Unexpected Error : Unexpected Error during Parsing Attribute <{column_name}> in NumericFilter"))
    #                         logger.error(response.detail)              

    #                 elif max_suffix in key:
    #                     column_name = key.split(max_suffix)[0]
    #                     try:
    #                         conditions.append(getattr(VectorDB, column_name) <= value)
    #                     except AttributeError as e:
    #                         response = Response(status_code=404, detail=self.response_format.error(f"Attribute Error : Unrecognized Attribute <{column_name}>. The Attribute for NumericFilter must be <key_min> or <key_max>", str(e)))
    #                         logger.error(response.detail)
    #                     except Exception:
    #                         response = Response(status_code=500, detail=self.response_format.error(f"Unexpected Error : Unexpected Error during Parsing Attribute <{column_name}> in NumericFilter"))
    #                         logger.error(response.detail)    

    #                 else:
    #                     response = Response(status_code=404, detail=self.response_format.error(f"Attribute Error : Unrecognized Attribute <{column_name}>. The Attribute Suffix for NumericFilter must be Either _min or _max"))
    #                     logger.error(response.detail)

    #     # List Filter
    #     if data_filter.list_filter is not None:
    #         or_suffix  = '_or'
    #         and_suffix = '_and'
    #         for key, value in data_filter.list_filter.__dict__.items():
    #             if value is not None:
    #                 if or_suffix in key:
    #                     column_name = key.split(or_suffix)[0]
    #                     _conditions = filter_by_entity(vb_model=VectorDB, column_name=column_name, entity_filter=value)
    #                     if _conditions:
    #                         conditions.append(or_(*_conditions))

    #                 elif and_suffix in key:
    #                     column_name = key.split(and_suffix)[0]
    #                     _conditions = filter_by_entity(vb_model=VectorDB, column_name=column_name, entity_filter=value)
    #                     if _conditions:
    #                         conditions.append(and_(*_conditions))

    #                 else:
    #                     response = Response(status_code=404, detail=self.response_format.error(f"Attribute Error : Unrecognized Attribute <{column_name}>. The Attribute Suffix for ListFilter must be Either _or or _and"))
    #                     logger.error(response.detail)


    #     # Dictionary Filter
    #     if data_filter.dictionary_filter is not None:
    #         or_suffix  = '_or'
    #         and_suffix = '_and'
    #         for key, value in data_filter.dictionary_filter.__dict__.items():
    #             if value is not None:
    #                 if or_suffix in key:
    #                     column_name = key.split(or_suffix)[0]
    #                     try:
    #                         _conditions = [getattr(VectorDB, column_name)[_key] for _key in value]
    #                         if _conditions:
    #                             conditions.append(or_(*_conditions))
    #                     except AttributeError as e:
    #                         response = Response(status_code=404, detail=self.response_format.error(f"Attribute Error : Unrecognized Attribute <{column_name}>. The Attribute for DictionaryFilter must be <key_or> or <key_and>", str(e)))
    #                         logger.error(response.detail)
    #                     except Exception:
    #                         response = Response(status_code=500, detail=self.response_format.error(f"Unexpected Error : Unexpected Error during Parsing Attribute <{column_name}> in DictionaryFilter"))
    #                         logger.error(response.detail)     

    #                 elif and_suffix in key:
    #                     column_name = key.split(and_suffix)[0]
    #                     try:
    #                         _conditions = [getattr(VectorDB, column_name)[_key] for _key in value]
    #                         if _conditions:
    #                             conditions.append(and_(*_conditions))
    #                     except AttributeError as e:
    #                         response = Response(status_code=404, detail=self.response_format.error(f"Attribute Error : Unrecognized Attribute <{column_name}>. The Attribute for DictionaryFilter must be <key_or> or <key_and>", str(e)))
    #                         logger.error(response.detail)
    #                     except Exception:
    #                         response = Response(status_code=500, detail=self.response_format.error(f"Unexpected Error : Unexpected Error during Parsing Attribute <{column_name}> in DictionaryFilter"))
    #                         logger.error(response.detail)

    #                 else:
    #                     response = Response(status_code=404, detail=self.response_format.error(f"Attribute Error : Unrecognized Attribute <{column_name}>. The Attribute Suffix for ListFilter must be Either _or or _and"))
    #                     logger.error(response.detail)


    #     # Boolean Filter
    #     if data_filter.boolean_filter is not None:
    #         boolean_suffix = '_filter'
            
    #         for key, value in data_filter.boolean_filter.__dict__.items():
    #             if value is not None:
    #                 column_name = key.split(boolean_suffix)[0]
    #                 try:
    #                     conditions.append(getattr(VectorDB, column_name) == value)
    #                 except AttributeError as e:
    #                     response = Response(status_code=404, detail=self.response_format.error(f"Attribute Error : Unrecognized Attribute <{column_name}>. The Attribute for BooleanFilter must be <key_filter>", str(e)))
    #                     logger.error(response.detail)
    #                 except Exception:
    #                     response = Response(status_code=500, detail=self.response_format.error(f"Unexpected Error : Unexpected Error during Parsing Attribute <{column_name}> in BooleanFilter"))
    #                     logger.error(response.detail)    


    #     # Datetime Filter
    #     if data_filter.datetime_filter is not None:
    #         start_suffix = '_start'
    #         end_suffix   = '_end'
            
    #         for key, value in data_filter.datetime_filter.__dict__.items():
    #             if value is not None:
    #                 if start_suffix in key:
    #                     column_name = key.split(start_suffix)[0]
    #                     try:
    #                         conditions.append(getattr(VectorDB, column_name) >= value)
    #                     except AttributeError as e:
    #                         response = Response(status_code=404, detail=self.response_format.error(f"Attribute Error : Unrecognized Attribute <{column_name}>. The Attribute for DatetimeFilter must be <key_start> or <key_end>", str(e)))
    #                         logger.error(response.detail)
    #                     except Exception:
    #                         response = Response(status_code=500, detail=self.response_format.error(f"Unexpected Error : Unexpected Error during Parsing Attribute <{column_name}> in DatetimeFilter"))
    #                         logger.error(response.detail)              

    #                 if end_suffix in key:
    #                     column_name = key.split(end_suffix)[0]
    #                     try:
    #                         conditions.append(getattr(VectorDB, column_name) <= value)
    #                     except AttributeError as e:
    #                         response = Response(status_code=404, detail=self.response_format.error(f"Attribute Error : Unrecognized Attribute <{column_name}>. The Attribute for DatetimeFilter must be <key_start> or <key_end>", str(e)))
    #                         logger.error(response.detail)
    #                     except Exception:
    #                         response = Response(status_code=500, detail=self.response_format.error(f"Unexpected Error : Unexpected Error during Parsing Attribute <{column_name}> in DatetimeFilter"))
    #                         logger.error(response.detail)    

    #                 else:
    #                     response = Response(status_code=404, detail=self.response_format.error(f"Attribute Error : Unrecognized Attribute <{column_name}>. The Attribute Suffix for DatetimeFilter must be Either _start or _end"))
    #                     logger.error(response.detail)

    #     # Byte Filter
    #     if data_filter.byte_filter is not None:
    #         byte_suffix = '_filter'
            
    #         for key, value in data_filter.byte_filter.__dict__.items():
    #             if value is not None:
    #                 column_name = key.split(byte_suffix)[0]
    #                 _conditions = filter_by_byte(vb_model=VectorDB, column_name=column_name, entity_filter=value)
    #                 if _conditions:
    #                     conditions += _conditions

    #                 else:
    #                     response = Response(status_code=404, detail=self.response_format.error(f"Attribute Error : Unrecognized Attribute <{column_name}>. The Attribute Suffix for ByteFilter must be _filter"))
    #                     logger.error(response.detail)

    #     # Define sorting order
    #     sorting = []
    #     for col, direction in data_filter.sorting.items():
    #         column = getattr(VectorDB, col, None)  # Returns None if col is not found
    #         if column is not None:
    #             if direction.lower() == "desc":
    #                 sorting.append(column.desc())
    #             else:
    #                 sorting.append(column.asc())

    #     filter_no = data_filter.filter_no

    #     return conditions, sorting, filter_no




