# from pydantic import BaseModel, Field 
# import uuid
# from datetime import datetime, timezone
# from fastapi import UploadFile
# from typing import List, Any, Dict, Optional

# """"
#     Relationship General Operation
# """
# class KnowRelationshipObject(BaseModel):
#     # Trace Information
#     data_id:       str=Field(default_factory=lambda: str(uuid.uuid4()))
#     data_traceid:   str=Field(default_factory=lambda: str(uuid.uuid4()))
#     data_version:   int=1

#     # Category Information
#     data_type:      str='' # TEXT, IMAGE, TABLE, DOCUMENT, etc.

#     # Control Information
#     data_status:    int=1

#     # Dependent
#     knowledge_id:   str='' # Map to Original Knowledge
#     node_id:        str=''
#     node_type:      str=''
#     node_name:      str=''

#     # Relation
#     relationship:    list[dict]=[] # [{target: str, relationship: str}]

#     # Time Information
#     created_at:     datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
#     updated_at:     datetime = Field(default_factory=lambda: datetime.now(timezone.utc))

# class RelationshipCreateRequest(BaseModel):
#     user_requestid: str | None = None
#     user_id:        str | None = ""
#     user_name:      str | None = ""
#     is_admin:       bool | None = False
#     container:      str | None = ""
#     data:           list[KnowRelationshipObject]

# class RelationshipBatchCreateRequest(BaseModel):
#     create_requests: list[RelationshipCreateRequest]

# # Relationship CRUD
# class RelationshipUpdate(BaseModel):
#     # Trace Information
#     data_id:        str | None = None
#     data_traceid:   str | None = None
#     data_version:   int | None = None

#     # Category Information
#     data_type:      str | None = None
#     content_type:   str | None = None
#     data_url:       str | None = None

#     # Control Information
#     data_status:    int | None = None

#     # Specification
#     raw_data:       str | None = None
#     processed_data: list[float] | None = None
#     data_dimension: int | None = None

#     coord_x1:       float | None = None
#     coord_x2:       float | None = None
#     coord_y1:       float | None = None
#     coord_y2:       float | None = None

#     page_start:     int | None = None
#     page_end:       int | None = None
#     line_start:     int | None = None
#     line_end:       int | None = None

#     # Dependent
#     knowledge_id:   str | None = None
#     node_id:        str | None = None
#     node_type:      str | None = None

#     # Tags
#     data_languages: list[str] | None = None
#     data_keywords:  list[str] | None = None
#     data_tags:      list[str] | None = None

#     # Time Information
#     created_at:     datetime | None = None
#     updated_at:     datetime | None = None

# class RelationshipUpdateRequest(BaseModel):
#     user_requestid: str | None = None
#     user_id:        str=''
#     user_name:      str=''
#     is_admin:       bool=False
#     data_id:        str | None = None
#     update_data:    RelationshipUpdate=RelationshipUpdate()
#     overwrite:      bool = False
    
# class RelationshipRequest(BaseModel):
#     user_requestid: str | None = None
#     user_id:        str | None = None
#     user_name:      str | None = None
#     data_id:        str | None = None

# class RelationshipBatchRequest(BaseModel):
#     vector_requests: list[RelationshipRequest]

# class KnowledgeRelationshipCreateRequest(BaseModel):
#     create_requests: list[RelationshipCreateRequest]

# class KnowledgeRelationshipCreateResponse(BaseModel):
#     knowledge_vectorstorage:  str=''
#     knowledge_vectorlocation: str=''
#     knowledge_vectorinfo:     dict=dict()


# # System-level Access
# class SecretRelationship(BaseModel):
#     # Trace Information
#     data_id:        str | None = None
#     data_traceid:   str | None = None
#     data_version:   int | None = None

#     # Category Information
#     data_type:      str | None = None
#     content_type:   str | None = None
#     data_url:       str | None = None

#     # Control Information
#     data_status:    int | None = None

#     # Specification
#     raw_data:       str | None = None
#     processed_data: list[float] | None = None
#     data_dimension: int | None = None

#     coord_x1:       float | None = None
#     coord_x2:       float | None = None
#     coord_y1:       float | None = None
#     coord_y2:       float | None = None

#     page_start:     int | None = None
#     page_end:       int | None = None
#     line_start:     int | None = None
#     line_end:       int | None = None

#     # Dependent
#     knowledge_id:   str | None = None
#     node_id:        str | None = None
#     node_type:      str | None = None

#     # Tags
#     data_languages: list[str] | None = None
#     data_keywords:  list[str] | None = None
#     data_tags:      list[str] | None = None

#     # Time Information
#     created_at:     datetime | None = None
#     updated_at:     datetime | None = None



# """
#     Relationship Filter
# """   
# class RelationshipStringFilter(BaseModel):
#     data_id_filter:               list[str] | None = None
#     vector_traceid_filter:          list[str] | None = None
#     vector_name_filter:             list[str] | None = None

#     creator_id_filter:                      list[str] | None = None
#     creator_name_filter:                    list[str] | None = None
#     approver_id_filter:                     list[str] | None = None
#     approver_name_filter:                   list[str] | None = None

#     vector_group_filter:            list[str] | None = None
#     vector_type_filter:             list[str] | None = None
#     vector_location_filter:         list[str] | None = None

#     storage_type_filter:                    list[str] | None = None
#     storage_type_origin_filter:             list[str] | None = None
#     storage_provider_filter:                list[str] | None = None
#     storage_provider_origin_filter:         list[str] | None = None

#     vector_directory_filter:        list[str] | None = None
#     vector_directory_origin_filter: list[str] | None = None
    
#     vector_key_filter:              list[str] | None = None

#     vector_filename:                list[str] | None = None
#     vector_fileextension_filter:    list[str] | None = None

# class RelationshipNumericFilter(BaseModel):
#     vector_version_min:    int | None = None
#     vector_version_max:    int | None = None

#     vector_status_min:     int | None = None
#     vector_status_max:     int | None = None 
#     vector_permission_min: int | None = None
#     vector_permission_max: int | None = None
#     vector_management_min: int | None = None
#     vector_management_max: int | None = None

#     vector_filesize_min:   float | None = None
#     vector_filesize_max:   float | None = None

# class RelationshipListFilter(BaseModel):
#     vector_tags_or:  list[str] | None = None
#     vector_tags_and: list[str] | None = None

# class RelationshipDictionaryFilter(BaseModel):
#     vector_secrets_or:  list[str] | None = None
#     vector_secrets_and: list[str] | None = None

# class RelationshipBooleanFilter(BaseModel):
#     vector_record_filter: bool | None = None

# class RelationshipDatetimeFilter(BaseModel):
#     created_at_start: datetime  | None = None
#     created_at_end:   datetime  | None = None
#     updated_at_start: datetime  | None = None
#     updated_at_end:   datetime  | None = None

# class RelationshipByteFilter(BaseModel):
#     not_used_filter: list[bytes] | None = None

# class RelationshipFilter(BaseModel):
#     string_filter:     RelationshipStringFilter     | None = None
#     numeric_filter:    RelationshipNumericFilter    | None = None
#     list_filter:       RelationshipListFilter       | None = None
#     dictionary_filter: RelationshipDictionaryFilter | None = None
#     boolean_filter:    RelationshipBooleanFilter    | None = None
#     datetime_filter:   RelationshipDatetimeFilter   | None = None
#     byte_filter:       RelationshipByteFilter       | None = None
#     sorting:           dict={"vector_name": "asc", "updated_at": "desc"}
#     filter_no:         int=-1


# """ 
#     Request and Resposne for System Access Relationships
# """
# class SystemRelationshipRequest(BaseModel):
#     vector_requestid: str=Field(default_factory=lambda: str(uuid.uuid4()))
#     vector_filter:    RelationshipFilter | None = None

# class SystemRelationshipResponse(BaseModel):
#     vector_requestid: str
#     filtered_vectors: list[SecretRelationship]=[]
#     vector_no:        int=0


# """
#     Data Backup / Restore Configuration
# """
# class BackupConfig(BaseModel):
#     format:   str | None = None
#     location: str | None = None
#     name:     str | None = None
#     host:     str | None = None
#     port:     str | None = None
#     user:     str | None = None
#     pswd:     str | None = None
#     table:    str | None = None
#     rdir:     str | None = None
#     sdir:     str | None = None
#     limit:    int | None = None

# class RelationshipBackupRequest(BaseModel):
#     vector_requestid: str=Field(default_factory=lambda: str(uuid.uuid4()))
#     vector_filter:    RelationshipFilter | None = None
#     backup_config:            BackupConfig | None = None

# class RelationshipBackupListRequest(BaseModel):
#     vector_requestid: str=Field(default_factory=lambda: str(uuid.uuid4()))
#     backup_config:            BackupConfig | None = None

# class RelationshipBackupListResponse(BaseModel):
#     vector_requestid: str=Field(default_factory=lambda: str(uuid.uuid4()))
#     table_list:               list[str]=[]

# class RestoreConfig(BaseModel):
#     format:   str | None = None
#     location: str | None = None
#     name:     str | None = None
#     host:     str | None = None
#     port:     str | None = None
#     user:     str | None = None
#     pswd:     str | None = None
#     table:    str | None = None
#     rdir:     str | None = None
#     sdir:     str | None = None

# class RelationshipRestoreRequest(BaseModel):
#     vector_requestid: str=Field(default_factory=lambda: str(uuid.uuid4()))
#     restore_config:           RestoreConfig | None = None


# """
#     Data Import/Export Configuration
# """
# class IOConfig(BaseModel):
#     format:           str | None = None
#     location:         str | None = None
#     name:             str | None = None
#     host:             str | None = None
#     port:             str | None = None
#     user:             str | None = None
#     pswd:             str | None = None
#     table:            str | None = None
#     rdir:             str | None = None
#     sdir:             str | None = None
#     file_rdir:        str | None = None
#     file_sdir:        str | None = None
#     file_name:        str | None = None

# class RelationshipImportRequest(BaseModel):
#     vector_requestid: str=Field(default_factory=lambda: str(uuid.uuid4()))
#     io_config:                IOConfig | None = None
#     backup:                   bool=True

# class RelationshipExportRequest(BaseModel):
#     vector_requestid: str=Field(default_factory=lambda: str(uuid.uuid4()))
#     vector_filter:    RelationshipFilter | None = None
#     io_config:                IOConfig | None = None
#     include_datetime:         bool = True


# """" RelationshipServiceManager """
# """
#     Request and Response for User Access Permitted Relationships
# """
# # User-level Access
# class Relationship(BaseModel):
#     # Trace Information
#     data_id:        str | None = None
#     data_traceid:   str | None = None
#     data_version:   int | None = None

#     # Category Information
#     data_type:      str | None = None
#     content_type:   str | None = None
#     data_url:       str | None = None

#     # Control Information
#     data_status:    int | None = None

#     # Specification
#     raw_data:       str | None = None
#     processed_data: list[float] | None = None
#     data_dimension:  int | None = None

#     coord_x1:       float | None = None
#     coord_x2:       float | None = None
#     coord_y1:       float | None = None
#     coord_y2:       float | None = None

#     page_start:     int | None = None
#     page_end:       int | None = None
#     line_start:     int | None = None
#     line_end:       int | None = None

#     # Dependent
#     knowledge_id:   str | None = None
#     node_id:        str | None = None
#     node_type:      str | None = None

#     # Tags
#     data_languages: list[str] | None = None
#     data_keywords:  list[str] | None = None
#     data_tags:      list[str] | None = None

#     # Time Information
#     created_at:     datetime | None = None
#     updated_at:     datetime | None = None

    
# class UserRelationshipRequest(BaseModel):
#     vector_requestid: str=Field(default_factory=lambda: str(uuid.uuid4()))
#     vector_filter:    RelationshipFilter

# class UserRelationshipResponse(BaseModel):
#     vector_requestid: str
#     filtered_vectors: list[Relationship]=[]


# class RelationshipReadNodeRequest(BaseModel):
#     user_requestid: str         | None = None
#     user_id:        str         | None = None
#     user_name:      str         | None = None
#     container_name: str        
#     node_id:        str        


# class RelationshipModel(BaseModel):
#     relationship: str
#     target: str




# class ReadRelationshipModel(BaseModel):
#     direction: str # incoming or outgoing
#     relationship: str
#     target: str

# class NodeModel(BaseModel):
#     id: str
#     label: str
#     relationships: list[ReadRelationshipModel]  | None = None
#     extra_properties: dict[str, Any]            | None = None


# class RelationshipReadRepsonse(BaseModel):
#     data: list[NodeModel] | None = None




# class RelationshipUpdate(BaseModel):
#     node_id: str                            | None = None
#     node_name: str                          | None = None
#     node_type: str                          | None = None
#     data_id: str                            | None = None
#     data_traceid: str                       | None = None
#     data_version: int                       | None = None
#     data_type: str                          | None = None
#     data_status: int                        | None = None
#     knowledge_id: str                       | None = None
#     relationship:  list[RelationshipModel]  | None = []
#     created_at: datetime                    | None = None
#     updated_at: datetime                    | None = None




# class KGNodeUpdateRequest(BaseModel):
#     container_name: str  
#     node_id: str       
#     update_data: RelationshipUpdate  


# class KGKnowledgeUpdateRequest(BaseModel):
#     container_name: str
#     knowledge_id: str
#     update_data: list[RelationshipUpdate] 


# class RelationshipDeleteKnowledgeRequest(BaseModel):
#     user_requestid: str         | None = None
#     user_id:        str         | None = None
#     user_name:      str         | None = None
#     container_name: str         | None = None
#     knowledge_id:   str         

# class RelationshipDeleteNodeRequest(BaseModel):
#     user_requestid: str         | None = None
#     user_id:        str         | None = None
#     user_name:      str         | None = None
#     container_name: str         
#     node_id:        str         