import os
from typing import Callable, Any
from datetime import datetime, timezone
import uuid
import time
import inspect

import json
import csv
import httpx

import asyncio
import sys

if sys.platform == "win32":
    asyncio.set_event_loop_policy(asyncio.WindowsSelectorEventLoopPolicy())

from azure.cosmos import CosmosClient, DatabaseProxy, ContainerProxy, PartitionKey
import azure.cosmos.exceptions as exceptions
# from gremlin_python.structure.graph import Graph
# from gremlin_python.driver.driver_remote_connection import DriverRemoteConnection
from gremlin_python.driver import client, serializer
from gremlin_python.structure.graph import Graph
from gremlin_python.driver.driver_remote_connection import DriverRemoteConnection

from ..settings import SETTINGS

from ..schemas.format import (
    ResponseFormatter,
    Response,
    ComplexEncoder
)

from ..schemas.graph import (
    GraphSearchRequest,
    GraphSearchResponse,
    NodeFilter,
    EdgeFilter,
    <PERSON><PERSON>h<PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON>de<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    Edge<PERSON><PERSON><PERSON><PERSON><PERSON>,
    EdgeN<PERSON>ric<PERSON>ilter,
    SecretGraph,
    SystemGraphRequest
)

from ..schemas.vector import (
    VectorFilter,
    VectorStringFilter
)

from ..routers.vector.system import (
    SystemVectorRequest
)

from ..database.graph.connections.graph_connection import (
    get_gb_func,
    get_gb_api,
    create_gb_client
)

from ..routers.graph.system import system_query_graph

# API DB Session
if SETTINGS.BASE.APP_API == True:
    gb_api = get_gb_api
    default_api_call = True
else:
    gb_api = None
    default_api_call = False

# Function DB Session
if SETTINGS.BASE.APP_FUNC == True:
    gb_func = get_gb_func
else:
    gb_func = None


from ..logger.log_handler import get_logger

logger = get_logger(__name__)


def date_to_str(date: datetime):
    return date.isoformat()

class GraphServiceManager:
    response_format = ResponseFormatter(suffix = f"{inspect.stack()[0][3]}")

    def __init__(
            self, 
            api_call:    bool = default_api_call,
            gb_api:      Any | None = gb_api,
            gb_func:     Any | None = gb_func,
            gb_storage:  str='COSMOS', 
            gb_location: str='azure', 
            gb_config:   dict={},
        ):
        self.api_call    = api_call
        self.gb_api      = gb_api
        self.gb_func     = gb_func
        self.gb_storage  = gb_storage
        self.gb_location = gb_location
        self.gb_config   = gb_config

    """
        General Operation
    """
    def partition_formatter(self, name: str) -> str:
        formated_name = name.replace(' ', '_')
        formated_name = formated_name.replace('.', '_')
        return f'{SETTINGS.GPDB.CONTAINER_PREFIX}{formated_name}'
    
    def search_by_graph(self, request: GraphSearchRequest) -> tuple[GraphSearchResponse, Response]:
        logger.info(f"Processing : <{SETTINGS.BASE.APP_NAME}> Starts Graph Search")
        start_at = time.time()
        response_graph = GraphSearchResponse(**request.__dict__)

        if not request.search_config:
            search_config = SETTINGS.RETR.DEFAULT_GRAPH_CONFIG
        else:
            search_config = request.search_config

        for index, _info in enumerate(request.search_info, start=1):
            logger.info(f"Graph Searching for <{index} / {len(request.search_info)}> Container")

            retrieved_node_ids = []
            retrieved_edge_ids = []

            # Handle both data_ids and node_ids input
            if _info.data_ids:
                # If data_ids are provided, find corresponding chunks and their node_ids
                logger.info(f"Processing data_ids: {_info.data_ids}")
                try:
                    # Step 1: Find input chunks by data_ids
                    input_request = SystemVectorRequest(
                        vector_filter=VectorFilter(
                            string_filter = VectorStringFilter(
                                data_id_filter = _info.data_ids
                            )
                        )
                    )
                    from ..routers.vector.system import system_query_vector
                    response_input = system_query_vector(request=input_request, api_call=self.api_call)
                    input_chunks = [_vector for _vector in response_input.filtered_vectors]
                    
                    if not input_chunks:
                        logger.warning(f"No chunks found for data_ids: {_info.data_ids}")
                        continue
                        
                    # Get knowledge_id and input chunk node_ids
                    input_knowledge_ids = list(set([chunk.knowledge_id for chunk in input_chunks if chunk.knowledge_id]))
                    input_node_ids = [chunk.node_id for chunk in input_chunks if chunk.node_id]
                    logger.info(f"Found {len(input_chunks)} input chunks with node_ids: {input_node_ids}")
                    
                    # Use first knowledge_id for container_name
                    if _info.container_name:
                        container_name = _info.container_name
                    elif input_knowledge_ids:
                        container_name = self.partition_formatter(name=input_knowledge_ids[0])
                    else:
                        logger.error(f"No knowledge_id found from data_ids and no container_name provided")
                        continue
                    
                    # Step 2: Find edges where input chunks are targets (reverse search)
                    # This will give us the Document Summary source nodes
                    reverse_edge_request = SystemGraphRequest(
                        container_name = container_name,
                        data_filter = GraphFilter(
                            edge_filter = EdgeFilter(
                                string_filter = EdgeStringFilter(
                                    target_node_id_filter = input_node_ids
                                ),
                                numeric_filter = EdgeNumericFilter(
                                    edge_status_min = 1
                                )
                            )
                        )
                    )
                    
                    response_reverse = system_query_graph(request=reverse_edge_request, api_call=self.api_call)
                    if not response_reverse.filtered_data.edges:
                        logger.warning(f"No edges found with input chunks as targets")
                        continue
                    
                    # Extract Document Summary source node_ids
                    source_node_ids = list(set([edge.source_node_id for edge in response_reverse.filtered_data.edges]))
                    logger.info(f"Found Document Summary source nodes: {source_node_ids}")
                    
                    # Use these source node_ids for forward search
                    node_ids = source_node_ids
                        
                except Exception as e:
                    logger.error(f"Failed to process data_ids {_info.data_ids}: {str(e)}")
                    response = Response(status_code=500, detail=self.response_format.error(f"Failed to process data_ids", str(e)))
                    return response_graph, response
            else:
                # Use provided node_ids directly (legacy mode)
                node_ids = _info.node_ids
                
                # Use provided knowledge_id for container_name
                if _info.container_name:
                    container_name = _info.container_name
                elif _info.knowledge_id:
                    container_name = self.partition_formatter(name=_info.knowledge_id)
                else:
                    logger.error(f"No container_name or knowledge_id provided for node_ids mode")
                    continue

            if not node_ids:
                logger.warning(f"No valid node_ids found for search_info {index}")
                continue

            top_k        = search_config.get("top_k", None)
            search_depth = search_config.get("depth", 1)

            # Iterate over Depth
            for i, level in enumerate(range(search_depth), start=1):
                logger.info(f"Graph Searching <{i} / {search_depth}> Depth of <{index} / {len(request.search_info)}> Container")
                edge_request = SystemGraphRequest(
                    container_name  = container_name,
                    data_filter     = GraphFilter(
                        edge_filter = EdgeFilter(
                            string_filter = EdgeStringFilter(
                                source_node_id_filter = [node_id for node_id in node_ids],
                            ),
                            numeric_filter = EdgeNumericFilter(
                                edge_status_min = 1
                            )
                        )
                    )
                )

                if search_config.get("edge_type_filter", None):
                    edge_request.data_filter.edge_filter.string_filter.edge_type_filter = search_config.get("edge_type_filter", None)

                try:
                    response_data = system_query_graph(request=edge_request, api_call=self.api_call)
                except Exception as e:
                    response = Response(status_code=500, detail=self.response_format.error(f"Failed to Query Edge from Graph DB", str(e)))
                    return response_graph, response
                
                if response_data.filtered_data.edges:
                    node_ids = [edge.target_node_id for edge in response_data.filtered_data.edges]
                    if top_k:
                        node_ids = node_ids[:top_k]

                    retrieved_node_ids += node_ids
                    retrieved_edge_ids += response_data.filtered_data.edges

            # Query Nodes
            logger.info("Edge Search Completed. Proceeding to Retrieving Nodes ...")
            node_request = SystemGraphRequest(
                container_name  = container_name,
                data_filter     = GraphFilter(
                    node_filter = NodeFilter(
                        string_filter = NodeStringFilter(
                            node_id_filter = [node_id for node_id in node_ids],
                        ),
                        numeric_filter = NodeNumericFilter(
                            node_status_min = 1
                        )
                    )
                )
            )

            try:
                response_data = system_query_graph(request=node_request, api_call=self.api_call)
                retrieved_nodes = response_data.filtered_data.nodes
            except Exception as e:
                response = Response(status_code=500, detail=self.response_format.error(f"Failed to Query Node from Graph DB", str(e)))
                return response_graph, response
        
            response_graph.retrieved_data.append(
                SecretGraph(
                    nodes=retrieved_nodes
                )
            )
            logger.info(f"Completed Graph Search for <{index} / {len(request.search_info)}> Container")

        # Update Response
        response_graph.__dict__.update(
            graph_retrieval_count = len(response_graph.retrieved_data),
            graph_retrieval_time  = time.time() - start_at
        )

        response = Response(status_code=200, detail=self.response_format.ok(f"Graph Search Completed : <{SETTINGS.BASE.APP_NAME}> Completed Graph Search"))
        logger.info(response.detail)

        return response_graph, response