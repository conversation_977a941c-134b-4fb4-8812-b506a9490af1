import os
from typing import Callable, Any
from datetime import datetime, timezone
import uuid
import time
import inspect

import json
import csv
import httpx

import asyncio
import sys

if sys.platform == "win32":
    asyncio.set_event_loop_policy(asyncio.WindowsSelectorEventLoopPolicy())

from azure.cosmos import CosmosClient, DatabaseProxy, ContainerProxy, PartitionKey
import azure.cosmos.exceptions as exceptions
# from gremlin_python.structure.graph import Graph
# from gremlin_python.driver.driver_remote_connection import DriverRemoteConnection
from gremlin_python.driver import client, serializer
from gremlin_python.structure.graph import Graph
from gremlin_python.driver.driver_remote_connection import DriverRemoteConnection

from ..settings import SETTINGS

from ..schemas.format import (
    ResponseFormatter,
    Response,
    ComplexEncoder
)

from ..schemas.graph import (
    GraphSearchRequest,
    GraphSearchResponse,
    NodeFilter,
    EdgeFilter,
    <PERSON><PERSON>h<PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON>deN<PERSON><PERSON><PERSON><PERSON><PERSON>,
    Edge<PERSON><PERSON><PERSON><PERSON><PERSON>,
    EdgeNumeric<PERSON>ilter,
    SecretGraph,
    SystemGraphRequest,
    AdjacentNodeRequest,
    AdjacentNodeResponse,
    AdjacentNodeFilter
)

from ..database.graph.connections.graph_connection import (
    get_gb_func,
    get_gb_api,
    create_gb_client
)

from ..routers.graph.system import system_query_graph, system_query_adjancent_nodes

# API DB Session
if SETTINGS.BASE.APP_API == True:
    gb_api = get_gb_api
    default_api_call = True
else:
    gb_api = None
    default_api_call = False

# Function DB Session
if SETTINGS.BASE.APP_FUNC == True:
    gb_func = get_gb_func
else:
    gb_func = None


from ..logger.log_handler import get_logger

logger = get_logger(__name__)


def date_to_str(date: datetime):
    return date.isoformat()

class GraphServiceManager:
    response_format = ResponseFormatter(suffix = f"{inspect.stack()[0][3]}")

    def __init__(
            self, 
            api_call:    bool = default_api_call,
            gb_api:      Any | None = gb_api,
            gb_func:     Any | None = gb_func,
            gb_storage:  str='COSMOS', 
            gb_location: str='azure', 
            gb_config:   dict={},
        ):
        self.api_call    = api_call
        self.gb_api      = gb_api
        self.gb_func     = gb_func
        self.gb_storage  = gb_storage
        self.gb_location = gb_location
        self.gb_config   = gb_config

    """
        General Operation
    """
    def partition_formatter(self, name: str) -> str:
        formated_name = name.replace(' ', '_')
        formated_name = formated_name.replace('.', '_')
        return f'{SETTINGS.GPDB.CONTAINER_PREFIX}{formated_name}'
    
    def search_by_graph(self, request: GraphSearchRequest) -> tuple[GraphSearchResponse, Response]:
        logger.info(f"Processing : <{SETTINGS.BASE.APP_NAME}> Starts Graph Search")
        start_at = time.time()
        response_graph = GraphSearchResponse(**request.__dict__)

        if not request.search_config:
            search_config = SETTINGS.RETR.DEFAULT_GRAPH_CONFIG
        else:
            search_config = request.search_config

        for index, _info in enumerate(request.search_info, start=1):
            logger.info(f"Graph Searching for <{index} / {len(request.search_info)}> Container")

            if _info.container_name:
                container_name = _info.container_name
            else:
                container_name = self.partition_formatter(name=_info.knowledge_id)

            retrieved_node_ids = []
            retrieved_edge_ids = []

            node_ids     = _info.node_ids
            top_k        = search_config.get("top_k", None)
            search_depth = search_config.get("depth", 1)
                
            # Query Nodes
            logger.info("Edge Search Completed. Proceeding to Retrieving Nodes ...")
            node_request = AdjacentNodeRequest(
                container_name  = container_name,
                data_filter     = AdjacentNodeFilter(
                    partition_key = _info.knowledge_id,
                    node_ids = _info.node_ids
                )
            )

            try:
                response_data = system_query_adjancent_nodes(request=node_request, api_call=self.api_call)
                retrieved_nodes = response_data.filtered_data
            except Exception as e:
                response = Response(status_code=500, detail=self.response_format.error(f"Failed to Query Node from Graph DB", str(e)))
                return response_graph, response
        
            response_graph.retrieved_data.append(
                SecretGraph(
                    nodes=retrieved_nodes
                )
            )
            logger.info(f"Completed Graph Search for <{index} / {len(request.search_info)}> Container")

        # Update Response
        response_graph.__dict__.update(
            graph_retrieval_count = len(response_graph.retrieved_data),
            graph_retrieval_time  = time.time() - start_at
        )

        response = Response(status_code=200, detail=self.response_format.ok(f"Graph Search Completed : <{SETTINGS.BASE.APP_NAME}> Completed Graph Search"))
        logger.info(response.detail)

        return response_graph, response

    def deep_search_by_graph(self, request: GraphSearchRequest) -> tuple[GraphSearchResponse, Response]:
        logger.info(f"Processing : <{SETTINGS.BASE.APP_NAME}> Starts Graph Search")
        start_at = time.time()
        response_graph = GraphSearchResponse(**request.__dict__)

        if not request.search_config:
            search_config = SETTINGS.RETR.DEFAULT_GRAPH_CONFIG
        else:
            search_config = request.search_config

        for index, _info in enumerate(request.search_info, start=1):
            logger.info(f"Graph Searching for <{index} / {len(request.search_info)}> Container")

            if _info.container_name:
                container_name = _info.container_name
            else:
                container_name = self.partition_formatter(name=_info.knowledge_id)

            retrieved_node_ids = []
            retrieved_edge_ids = []

            node_ids     = _info.node_ids
            top_k        = search_config.get("top_k", None)
            search_depth = search_config.get("depth", 1)

            # Iterate over Depth
            for i, level in enumerate(range(search_depth), start=1):
                logger.info(f"Graph Searching <{i} / {search_depth}> Depth of <{index} / {len(request.search_info)}> Container")
                edge_request = SystemGraphRequest(
                    container_name  = container_name,
                    data_filter     = GraphFilter(
                        edge_filter = EdgeFilter(
                            string_filter = EdgeStringFilter(
                                source_node_id_filter = [node_id for node_id in node_ids],
                            )
                        )
                    )
                )

                if search_config.get("edge_type_filter", None):
                    edge_request.data_filter.edge_filter.string_filter.edge_type_filter = search_config.get("edge_type_filter", None)

                try:
                    response_data = system_query_graph(request=edge_request, api_call=self.api_call)
                except Exception as e:
                    response = Response(status_code=500, detail=self.response_format.error(f"Failed to Query Edge from Graph DB", str(e)))
                    return response_graph, response
                
                if response_data.filtered_data.edges:
                    node_ids = [edge.target_node_id for edge in response_data.filtered_data.edges]
                    if top_k:
                        node_ids = node_ids[:top_k]

                    retrieved_node_ids += node_ids
                    retrieved_edge_ids += response_data.filtered_data.edges

            # Query Nodes
            logger.info("Edge Search Completed. Proceeding to Retrieving Nodes ...")
            node_request = SystemGraphRequest(
                container_name  = container_name,
                data_filter     = GraphFilter(
                    node_filter = NodeFilter(
                        string_filter = NodeStringFilter(
                            node_id_filter = [node_id for node_id in node_ids],
                        ),
                        numeric_filter = NodeNumericFilter(
                            node_status_min = 1
                        )
                    )
                )
            )

            try:
                response_data = system_query_graph(request=node_request, api_call=self.api_call)
                retrieved_nodes = response_data.filtered_data.nodes
            except Exception as e:
                response = Response(status_code=500, detail=self.response_format.error(f"Failed to Query Node from Graph DB", str(e)))
                return response_graph, response
        
            response_graph.retrieved_data.append(
                SecretGraph(
                    nodes=retrieved_nodes
                )
            )
            logger.info(f"Completed Graph Search for <{index} / {len(request.search_info)}> Container")

        # Update Response
        response_graph.__dict__.update(
            graph_retrieval_count = len(response_graph.retrieved_data),
            graph_retrieval_time  = time.time() - start_at
        )

        response = Response(status_code=200, detail=self.response_format.ok(f"Graph Search Completed : <{SETTINGS.BASE.APP_NAME}> Completed Graph Search"))
        logger.info(response.detail)

        return response_graph, response