import os
from typing import Callable, Any
from datetime import datetime, timezone
import uuid
import time
import inspect

import json
import csv
import httpx

import asyncio
import sys

if sys.platform == "win32":
    asyncio.set_event_loop_policy(asyncio.WindowsSelectorEventLoopPolicy())

from azure.cosmos import CosmosClient, DatabaseProxy, ContainerProxy, PartitionKey
import azure.cosmos.exceptions as exceptions
# from gremlin_python.structure.graph import Graph
# from gremlin_python.driver.driver_remote_connection import DriverRemoteConnection
from gremlin_python.driver import client, serializer
from gremlin_python.structure.graph import Graph
from gremlin_python.driver.driver_remote_connection import DriverRemoteConnection

from ..settings import SETTINGS

from ..schemas.format import (
    ResponseFormatter,
    Response,
    ComplexEncoder
)

from ..schemas.graph import (
    GraphSearchRequest,
    GraphSearchResponse,
    NodeFilter,
    EdgeFilter,
    <PERSON><PERSON>h<PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON>de<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    Edge<PERSON><PERSON><PERSON><PERSON><PERSON>,
    EdgeNumeric<PERSON>ilter,
    SecretGraph,
    SystemGraphRequest,
    AdjacentNodeRequest,
    AdjacentNodeResponse,
    AdjacentNodeFilter
)

from ..schemas.vector import (
    VectorFilter,
    VectorStringFilter
)

from ..routers.vector.system import (
    SystemVectorRequest
)

from ..database.graph.connections.graph_connection import (
    get_gb_func,
    get_gb_api,
    create_gb_client
)

from ..routers.graph.system import system_query_graph, system_query_adjancent_nodes

# API DB Session
if SETTINGS.BASE.APP_API == True:
    gb_api = get_gb_api
    default_api_call = True
else:
    gb_api = None
    default_api_call = False

# Function DB Session
if SETTINGS.BASE.APP_FUNC == True:
    gb_func = get_gb_func
else:
    gb_func = None


from ..logger.log_handler import get_logger

logger = get_logger(__name__)


def date_to_str(date: datetime):
    return date.isoformat()

class GraphServiceManager:
    response_format = ResponseFormatter(suffix = f"{inspect.stack()[0][3]}")

    def __init__(
            self, 
            api_call:    bool = default_api_call,
            gb_api:      Any | None = gb_api,
            gb_func:     Any | None = gb_func,
            gb_storage:  str='COSMOS', 
            gb_location: str='azure', 
            gb_config:   dict={},
        ):
        self.api_call    = api_call
        self.gb_api      = gb_api
        self.gb_func     = gb_func
        self.gb_storage  = gb_storage
        self.gb_location = gb_location
        self.gb_config   = gb_config

    """
        General Operation
    """
    def partition_formatter(self, name: str) -> str:
        formated_name = name.replace(' ', '_')
        formated_name = formated_name.replace('.', '_')
        return f'{SETTINGS.GPDB.CONTAINER_PREFIX}{formated_name}'
    
    def search_by_graph(self, request: GraphSearchRequest) -> tuple[GraphSearchResponse, Response]:
        logger.info(f"Processing : <{SETTINGS.BASE.APP_NAME}> Starts Graph Search")
        start_at = time.time()
        response_graph = GraphSearchResponse(**request.__dict__)

        if not request.search_config:
            search_config = SETTINGS.RETR.DEFAULT_GRAPH_CONFIG
        else:
            search_config = request.search_config

        logger.info(f"🔍 DEBUG: Search config: {search_config}")
        logger.info(f"🔍 DEBUG: Processing {len(request.search_info)} search_info items")

        for index, _info in enumerate(request.search_info, start=1):
            logger.info(f"🔍 DEBUG: Processing search_info {index}: data_ids={_info.data_ids}, node_ids={_info.node_ids}, container_name={_info.container_name}, knowledge_id={_info.knowledge_id}")
            logger.info(f"Graph Searching for <{index} / {len(request.search_info)}> Container")

            retrieved_node_ids = []
            retrieved_edge_ids = []

            # Handle both data_ids and node_ids input
            if _info.data_ids:
                # If data_ids are provided, find corresponding chunks and their node_ids
                logger.info(f"🔍 DEBUG: Processing data_ids: {_info.data_ids}")
                try:
                    # Step 1: Find input chunks by data_ids
                    input_request = SystemVectorRequest(
                        vector_filter=VectorFilter(
                            string_filter = VectorStringFilter(
                                data_id_filter = _info.data_ids
                            )
                        )
                    )
                    logger.info(f"🔍 DEBUG: Vector search request: {input_request}")

                    from ..routers.vector.system import system_query_vector
                    response_input = system_query_vector(request=input_request, api_call=self.api_call)
                    input_chunks = [_vector for _vector in response_input.filtered_vectors]

                    logger.info(f"🔍 DEBUG: Vector search response - found {len(input_chunks)} chunks")
                    for i, chunk in enumerate(input_chunks):
                        logger.info(f"🔍 DEBUG: Chunk {i}: data_id={chunk.data_id}, node_id={chunk.node_id}, knowledge_id={chunk.knowledge_id}")

                    if not input_chunks:
                        logger.warning(f"❌ No chunks found for data_ids: {_info.data_ids}")
                        continue

                    # Get knowledge_id and input chunk node_ids
                    input_knowledge_ids = list(set([chunk.knowledge_id for chunk in input_chunks if chunk.knowledge_id]))
                    input_node_ids = [chunk.node_id for chunk in input_chunks if chunk.node_id]
                    logger.info(f"🔍 DEBUG: Extracted knowledge_ids: {input_knowledge_ids}")
                    logger.info(f"🔍 DEBUG: Extracted node_ids: {input_node_ids}")

                    # Use first knowledge_id for container_name
                    if _info.container_name:
                        container_name = _info.container_name
                        logger.info(f"🔍 DEBUG: Using provided container_name: {container_name}")
                    elif input_knowledge_ids:
                        # Try using the fixed container name first
                        if hasattr(SETTINGS.GPDB, 'CONTAINER') and SETTINGS.GPDB.CONTAINER:
                            container_name = SETTINGS.GPDB.CONTAINER
                            logger.info(f"🔍 DEBUG: Using fixed container_name from config: {container_name}")
                        else:
                            container_name = self.partition_formatter(name=input_knowledge_ids[0])
                            logger.info(f"🔍 DEBUG: Generated container_name from knowledge_id: {container_name}")
                    else:
                        logger.error(f"❌ No knowledge_id found from data_ids and no container_name provided")
                        continue
                    
                    # Step 2: Find edges where input chunks are targets (reverse search)
                    # This will give us the Document Summary source nodes
                    reverse_edge_request = SystemGraphRequest(
                        container_name = container_name,
                        partition_key = input_knowledge_ids[0],  # Use first knowledge_id as partition_key
                        data_filter = GraphFilter(
                            edge_filter = EdgeFilter(
                                string_filter = EdgeStringFilter(
                                    target_node_id_filter = input_node_ids
                                ),
                                numeric_filter = EdgeNumericFilter(
                                    edge_status_min = 1
                                )
                            )
                        )
                    )

                    logger.info(f"🔍 DEBUG: Reverse edge search request: container={container_name}, target_node_ids={input_node_ids}")
                    response_reverse = system_query_graph(request=reverse_edge_request, api_call=self.api_call)
                    logger.info(f"🔍 DEBUG: Reverse edge search found {len(response_reverse.filtered_data.edges)} edges")

                    for i, edge in enumerate(response_reverse.filtered_data.edges):
                        logger.info(f"🔍 DEBUG: Reverse Edge {i}: {edge.source_node_id} -> {edge.target_node_id} (type: {edge.edge_type})")

                    if not response_reverse.filtered_data.edges:
                        logger.warning(f"❌ No edges found with input chunks as targets")
                        continue

                    # Extract Document Summary source node_ids
                    source_node_ids = list(set([edge.source_node_id for edge in response_reverse.filtered_data.edges]))
                    logger.info(f"🔍 DEBUG: Found Document Summary source nodes: {source_node_ids}")

                    # Use these source node_ids for forward search
                    node_ids = source_node_ids
                        
                except Exception as e:
                    logger.error(f"Failed to process data_ids {_info.data_ids}: {str(e)}")
                    response = Response(status_code=500, detail=self.response_format.error(f"Failed to process data_ids", str(e)))
                    return response_graph, response
            else:
                # Use provided node_ids directly (legacy mode)
                node_ids = _info.node_ids
                
                # Use provided knowledge_id for container_name
                if _info.container_name:
                    container_name = _info.container_name
                elif _info.knowledge_id:
                    # Try using the fixed container name first
                    if hasattr(SETTINGS.GPDB, 'CONTAINER') and SETTINGS.GPDB.CONTAINER:
                        container_name = SETTINGS.GPDB.CONTAINER
                        logger.info(f"🔍 DEBUG: Using fixed container_name from config: {container_name}")
                    else:
                        container_name = self.partition_formatter(name=_info.knowledge_id)
                        logger.info(f"🔍 DEBUG: Generated container_name from knowledge_id: {container_name}")
                else:
                    logger.error(f"No container_name or knowledge_id provided for node_ids mode")
                    continue

            if not node_ids:
                logger.warning(f"❌ No valid node_ids found for search_info {index}")
                continue

            logger.info(f"🔍 DEBUG: Starting forward search with node_ids: {node_ids}")
            top_k        = search_config.get("top_k", None)
            search_depth = search_config.get("depth", 1)
            edge_type_filter = search_config.get("edge_type_filter", None)

            logger.info(f"🔍 DEBUG: Search config - top_k: {top_k}, depth: {search_depth}, edge_type_filter: {edge_type_filter}")

            # Query Nodes
            logger.info("Edge Search Completed. Proceeding to Retrieving Nodes ...")
            node_request = AdjacentNodeRequest(
                container_name  = container_name,
                data_filter     = AdjacentNodeFilter(
                    partition_key = _info.knowledge_id,
                    node_ids = _info.node_ids
                )
            )

            try:
                response_data = system_query_adjancent_nodes(request=node_request, api_call=self.api_call)
                retrieved_nodes = response_data.filtered_data
            except Exception as e:
                response = Response(status_code=500, detail=self.response_format.error(f"Failed to Query Node from Graph DB", str(e)))
                return response_graph, response

            response_graph.retrieved_data.append(
                SecretGraph(
                    nodes=retrieved_nodes
                )
            )
            logger.info(f"Completed Graph Search for <{index} / {len(request.search_info)}> Container")

        # Update Response
        response_graph.__dict__.update(
            graph_retrieval_count = len(response_graph.retrieved_data),
            graph_retrieval_time  = time.time() - start_at
        )

        response = Response(status_code=200, detail=self.response_format.ok(f"Graph Search Completed : <{SETTINGS.BASE.APP_NAME}> Completed Graph Search"))
        logger.info(response.detail)

        return response_graph, response

    def deep_search_by_graph(self, request: GraphSearchRequest) -> tuple[GraphSearchResponse, Response]:
        logger.info(f"Processing : <{SETTINGS.BASE.APP_NAME}> Starts Graph Search")
        start_at = time.time()
        response_graph = GraphSearchResponse(**request.__dict__)

        if not request.search_config:
            search_config = SETTINGS.RETR.DEFAULT_GRAPH_CONFIG
        else:
            search_config = request.search_config

        for index, _info in enumerate(request.search_info, start=1):
            logger.info(f"Graph Searching for <{index} / {len(request.search_info)}> Container")

            if _info.container_name:
                container_name = _info.container_name
            else:
                # Try using the fixed container name first
                if hasattr(SETTINGS.GPDB, 'CONTAINER') and SETTINGS.GPDB.CONTAINER:
                    container_name = SETTINGS.GPDB.CONTAINER
                    logger.info(f"🔍 DEBUG: Using fixed container_name from config: {container_name}")
                else:
                    container_name = self.partition_formatter(name=_info.knowledge_id)
                    logger.info(f"🔍 DEBUG: Generated container_name from knowledge_id: {container_name}")

            retrieved_node_ids = []
            retrieved_edge_ids = []

            node_ids     = _info.node_ids
            top_k        = search_config.get("top_k", None)
            search_depth = search_config.get("depth", 1)

            # Iterate over Depth
            for i, level in enumerate(range(search_depth), start=1):
                logger.info(f"🔍 DEBUG: Graph Searching depth {i}/{search_depth} for container {index}/{len(request.search_info)}")
                edge_request = SystemGraphRequest(
                    container_name  = container_name,
                    partition_key   = _info.knowledge_id,  # Use knowledge_id as partition_key
                    data_filter     = GraphFilter(
                        edge_filter = EdgeFilter(
                            string_filter = EdgeStringFilter(
                                source_node_id_filter = [node_id for node_id in node_ids],
                            )
                        )
                    )
                )

                if edge_type_filter:
                    edge_request.data_filter.edge_filter.string_filter.edge_type_filter = edge_type_filter
                    logger.info(f"🔍 DEBUG: Applied edge_type_filter: {edge_type_filter}")

                logger.info(f"🔍 DEBUG: Forward edge search request: container={container_name}, source_node_ids={node_ids}, edge_type_filter={edge_type_filter}")

                try:
                    response_data = system_query_graph(request=edge_request, api_call=self.api_call)
                    logger.info(f"🔍 DEBUG: Forward edge search found {len(response_data.filtered_data.edges)} edges")

                    for j, edge in enumerate(response_data.filtered_data.edges):
                        logger.info(f"🔍 DEBUG: Forward Edge {j}: {edge.source_node_id} -> {edge.target_node_id} (type: {edge.edge_type})")

                except Exception as e:
                    logger.error(f"❌ Failed to query edges from graph DB: {str(e)}")
                    response = Response(status_code=500, detail=self.response_format.error(f"Failed to Query Edge from Graph DB", str(e)))
                    return response_graph, response

                if response_data.filtered_data.edges:
                    new_node_ids = [edge.target_node_id for edge in response_data.filtered_data.edges]
                    logger.info(f"🔍 DEBUG: Found {len(new_node_ids)} target nodes: {new_node_ids}")

                    if top_k:
                        new_node_ids = new_node_ids[:top_k]
                        logger.info(f"🔍 DEBUG: Limited to top_k={top_k}: {new_node_ids}")

                    retrieved_node_ids += new_node_ids
                    retrieved_edge_ids += response_data.filtered_data.edges
                    node_ids = new_node_ids  # Update for next depth level
                else:
                    logger.warning(f"❌ No edges found at depth {i}")
                    break  # No more edges to follow

            # Query Nodes
            logger.info(f"🔍 DEBUG: Edge Search Completed. Retrieved {len(retrieved_node_ids)} unique node_ids: {list(set(retrieved_node_ids))}")
            logger.info(f"🔍 DEBUG: Retrieved {len(retrieved_edge_ids)} edges")

            if retrieved_node_ids:
                unique_node_ids = list(set(retrieved_node_ids))
                node_request = SystemGraphRequest(
                    container_name  = container_name,
                    partition_key   = _info.knowledge_id,  # Use knowledge_id as partition_key
                    data_filter     = GraphFilter(
                        node_filter = NodeFilter(
                            string_filter = NodeStringFilter(
                                node_id_filter = unique_node_ids,
                            ),
                            numeric_filter = NodeNumericFilter(
                                node_status_min = 1
                            )
                        )
                    )
                )

                logger.info(f"🔍 DEBUG: ===== NODE REQUEST DETAILS =====")
                logger.info(f"🔍 DEBUG: Node request container_name: {node_request.container_name}")
                logger.info(f"🔍 DEBUG: Node request graph_requestid: {node_request.graph_requestid}")
                logger.info(f"🔍 DEBUG: Node request data_filter: {node_request.data_filter}")
                if node_request.data_filter and node_request.data_filter.node_filter:
                    logger.info(f"🔍 DEBUG: Node filter string_filter: {node_request.data_filter.node_filter.string_filter}")
                    logger.info(f"🔍 DEBUG: Node filter numeric_filter: {node_request.data_filter.node_filter.numeric_filter}")
                    if node_request.data_filter.node_filter.string_filter:
                        logger.info(f"🔍 DEBUG: Node ID filter: {node_request.data_filter.node_filter.string_filter.node_id_filter}")
                    if node_request.data_filter.node_filter.numeric_filter:
                        logger.info(f"🔍 DEBUG: Node status min: {node_request.data_filter.node_filter.numeric_filter.node_status_min}")
                logger.info(f"🔍 DEBUG: Node IDs to search: {unique_node_ids}")
                logger.info(f"🔍 DEBUG: Total unique node IDs: {len(unique_node_ids)}")

                # Convert to JSON for complete visibility
                import json
                try:
                    node_request_dict = node_request.dict()
                    logger.info(f"🔍 DEBUG: Complete node request JSON: {json.dumps(node_request_dict, indent=2, default=str)}")
                except Exception as json_e:
                    logger.warning(f"🔍 DEBUG: Could not serialize node request to JSON: {json_e}")

                logger.info(f"🔍 DEBUG: ===== END NODE REQUEST DETAILS =====")

                try:
                    response_data = system_query_graph(request=node_request, api_call=self.api_call)
                    retrieved_nodes = response_data.filtered_data.nodes
                    logger.info(f"🔍 DEBUG: Node search found {len(retrieved_nodes)} nodes")

                    for k, node in enumerate(retrieved_nodes):
                        logger.info(f"🔍 DEBUG: Retrieved Node {k}: id={node.node_id}, type={getattr(node, 'node_type', 'N/A')}")

                except Exception as e:
                    logger.error(f"❌ Failed to query nodes from graph DB: {str(e)}")
                    response = Response(status_code=500, detail=self.response_format.error(f"Failed to Query Node from Graph DB", str(e)))
                    return response_graph, response
            else:
                logger.warning(f"❌ No node_ids to retrieve")
                retrieved_nodes = []
        
            response_graph.retrieved_data.append(
                SecretGraph(
                    nodes=retrieved_nodes
                )
            )
            logger.info(f"Completed Graph Search for <{index} / {len(request.search_info)}> Container")

        # Update Response
        response_graph.__dict__.update(
            graph_retrieval_count = len(response_graph.retrieved_data),
            graph_retrieval_time  = time.time() - start_at
        )

        response = Response(status_code=200, detail=self.response_format.ok(f"Graph Search Completed : <{SETTINGS.BASE.APP_NAME}> Completed Graph Search"))
        logger.info(response.detail)

        return response_graph, response