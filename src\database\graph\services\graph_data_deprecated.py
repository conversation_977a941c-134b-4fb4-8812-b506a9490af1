# import os
# from typing import Callable, Any
# from datetime import datetime, timezone
# import uuid
# import inspect

# import json
# import csv
# import httpx

# import asyncio
# import sys

# if sys.platform == "win32":
#     asyncio.set_event_loop_policy(asyncio.WindowsSelectorEventLoopPolicy())

# from azure.cosmos import CosmosClient, DatabaseProxy, ContainerProxy, PartitionKey
# import azure.cosmos.exceptions as exceptions
# # from gremlin_python.structure.graph import Graph
# # from gremlin_python.driver.driver_remote_connection import DriverRemoteConnection
# from gremlin_python.driver import client, serializer
# from gremlin_python.structure.graph import Graph
# from gremlin_python.driver.driver_remote_connection import DriverRemoteConnection

# from ....settings import SETTINGS

# from ....database.graph.connections.graph_connection import create_gb_client, get_gb

# from ..schemas.format import (
#     ResponseFormatter,
#     Response,
#     ComplexEncoder
# )

# from ..schemas.graph import(
#     RelationshipCreateRequest,
# )


# from ..schemas.graph import(
#     RelationshipCreateRequest,
#     RelationshipReadNodeRequest,
#     RelationshipReadRepsonse,
#     RelationshipModel,
#     NodeModel,
#     KGNodeUpdateRequest,
#     KGKnowledgeUpdateRequest,
#     RelationshipDeleteNodeRequest,
#     RelationshipDeleteKnowledgeRequest,
#     ReadRelationshipModel
# )

# if SETTINGS.BASE.APP_ENCRYPTION == True:
#     from ....security.services.crypto_service import CryptoServiceManager

# from ....logger.log_handler import get_logger

# logger = get_logger(__name__)

# def date_to_str(date: datetime):
#     return date.isoformat()

# class GraphDataManager:
#     response_format = ResponseFormatter(suffix = f"{inspect.stack()[0][3]}")

#     def __init__(
#             self, 
#             api_call:    bool,
#             gb_storage:  str='COSMOS', 
#             gb_location: str='azure', 
#             gb_config:   dict={},
#             gb_client:   Callable[[str], Any]=get_gb
#         ):
#         self.gb_client   = gb_client
#         self.gb_storage  = gb_storage
#         self.gb_location = gb_location
#         self.gb_config   = gb_config
#         self.api_call    = api_call

#     """
#         General Operation
#     """
#     def partition_formatter(self, name: str):
#         formated_name = name.replace(' ', '_')
#         formated_name = formated_name.replace('.', '_')
#         return formated_name

#     # Create
#     def create(self, request: RelationshipCreateRequest) -> Response:
#         if not request.data:
#             response = Response(status_code=404, detail=self.response_format.error(f"Data Unfound Error : <{SETTINGS.BASE.APP_NAME}> Found Empty Data during Registering Relationship"))
#             return response
        
#         if request.container:
#             container_name = request.container
#         else:
#             container_name = self.partition_formatter(str(request.data[0].knowledge_id))
        
#         try:
#             graph = create_gb_client().create_container_if_not_exists(
#                 id=container_name,
#                 partition_key=PartitionKey(path="/node_id"),
#                 offer_throughput=400
#             )
#         except:
#             response = Response(status_code=500, detail=self.response_format.error(f"Container Creation Failed : <{SETTINGS.BASE.APP_NAME}> Failed to Create Container for Graph DB"))
#             return response

#         node_data = []
#         edge_data = []
#         for _data in request.data:
#             bindings_obj = _data.__dict__
                
#             bindings_obj["created_at"] = date_to_str(_data.__dict__.get("created_at", datetime.now(timezone.utc)))
#             updated_at = date_to_str(datetime.now(timezone.utc))
#             bindings_obj["updated_at"] = updated_at

#             bindings={
#                     "prop_id": bindings_obj["node_id"],
#                     "prop_node_id": bindings_obj["node_id"],
#                     "prop_node_name": bindings_obj["node_name"],
#                     "prop_node_type": bindings_obj["node_type"],
#                     "prop_data_id": bindings_obj["data_id"],
#                     "prop_data_traceid": bindings_obj["data_traceid"],
#                     "prop_data_version": bindings_obj["data_version"],
#                     "prop_data_type": bindings_obj["data_type"],
#                     "prop_data_status": bindings_obj["data_status"],
#                     "prop_knowledge_id": bindings_obj["knowledge_id"],
#                     "prop_created_at": bindings_obj["created_at"],
#                     "prop_updated_at": bindings_obj["updated_at"],
#                 }

#             with self.gb_client(container_name) as gremlin_client:
#                 gremlin_client.submit(
#                     message=(
#                         "g.addV('chunk')"
#                         ".property('id', prop_node_id)"
#                         ".property('node_id', prop_node_id)"
#                         ".property('node_name', prop_node_name)"
#                         ".property('node_type', prop_node_type)"
#                         ".property('data_id', prop_data_id)"
#                         ".property('data_traceid', prop_data_traceid)"
#                         ".property('data_version', prop_data_version)"
#                         ".property('data_type', prop_data_type)"
#                         ".property('data_status', prop_data_status)"
#                         ".property('knowledge_id', prop_knowledge_id)"
#                         ".property('created_at', prop_created_at)"
#                         ".property('updated_at', prop_updated_at)"
#                     ),
#                     bindings=bindings,
#                 )

#         for _data in request.data:
#             bindings_obj = _data.__dict__
#             with self.gb_client(container_name) as gremlin_client:
#                 for _relationship in bindings_obj["relationship"]:
#                     query = "g.V(node_id).addE(relationship).to(g.V(target))"
#                     param = {
#                         "node_id":      bindings_obj["node_id"],
#                         "relationship": _relationship["relationship"],
#                         "target":       _relationship["target"],
#                     }
#                     gremlin_client.submit(query, param)            


#         # with get_gb_func(container_name) as gremlin_client:
#         #     for node in request.data:
#         #         bindings_obj = {}
#         #         for key, value in node.__dict__.items():
#         #             bindings_obj[key] = value
                    
#         #         bindings_obj["created_at"] = date_to_str(node.__dict__.get("created_at", datetime.now(timezone.utc)))
#         #         updated_at = date_to_str(datetime.now(timezone.utc))
#         #         bindings_obj["updated_at"] = updated_at

#         #         bindings={
#         #                 "prop_id": bindings_obj["node_id"],
#         #                 "prop_node_id": bindings_obj["node_id"],
#         #                 "prop_node_name": bindings_obj["node_name"],
#         #                 "prop_node_type": bindings_obj["node_type"],
#         #                 "prop_data_id": bindings_obj["data_id"],
#         #                 "prop_data_traceid": bindings_obj["data_traceid"],
#         #                 "prop_data_version": bindings_obj["data_version"],
#         #                 "prop_data_type": bindings_obj["data_type"],
#         #                 "prop_data_status": bindings_obj["data_status"],
#         #                 "prop_knowledge_id": bindings_obj["knowledge_id"],
#         #                 "prop_created_at": bindings_obj["created_at"],
#         #                 "prop_updated_at": bindings_obj["updated_at"],
#         #             }
            

#         #         gremlin_client.submit(
#         #             message=(
#         #                 "g.addV('chunk')"
#         #                 ".property('id', prop_node_id)"
#         #                 ".property('node_id', prop_node_id)"
#         #                 ".property('node_name', prop_node_name)"
#         #                 ".property('node_type', prop_node_type)"
#         #                 ".property('data_id', prop_data_id)"
#         #                 ".property('data_traceid', prop_data_traceid)"
#         #                 ".property('data_version', prop_data_version)"
#         #                 ".property('data_type', prop_data_type)"
#         #                 ".property('data_status', prop_data_status)"
#         #                 ".property('knowledge_id', prop_knowledge_id)"
#         #                 ".property('created_at', prop_created_at)"
#         #                 ".property('updated_at', prop_updated_at)"
#         #             ),
#         #             bindings=bindings,
#         #         )

#         #     for node in data:
#         #         bindings_obj = {}
#         #         for key, value in node.__dict__.items():
#         #             bindings_obj[key] = value
#         #         for relation in bindings_obj["relationship"]:
#         #             query = "g.V(node_id).addE(relationship).to(g.V(target))"
#         #             param = {
#         #                 "node_id": bindings_obj["node_id"],
#         #                 "relationship": relation["relationship"],
#         #                 "target": relation["target"],
#         #             }
#         #             gremlin_client.submit(query, param)

#         #     # res = gremlin_client.submit(
#         #     #     "g.V()"
#         #     # )

#         #     # for item in res:
#         #     #     print(f"[100] {item}")

#         response = Response(status_code=201, detail=self.response_format.ok(f"Success : Registered Relationship <container: {container_name}>"))
#         logger.info(response.detail)

#         return response
#     def read_by_node_id(self, request: RelationshipReadNodeRequest) -> tuple[RelationshipReadRepsonse, Response]:
#         try:
#             node_id = request.node_id
#             container_name = request.container_name if request.container_name else self.default_container

#             # Query node properties
#             query = "g.V().has('node_id', node_id).valueMap(true)"
#             bindings = {"node_id": node_id}

#             with self.gb_client(container_name) as gremlin_client:
#                 result_nodes = gremlin_client.submit(query, bindings).all().result()

#                 if not result_nodes:
#                     return RelationshipReadRepsonse(data=[]), Response(status_code=404, detail=f"Node {node_id} not found")

#                 raw_node_data = result_nodes[0]
#                 # Flatten properties
#                 node_data = {
#                     key: value[0] if isinstance(value, list) and len(value) == 1 else value
#                     for key, value in raw_node_data.items()
#                 }
#                 extracted_id = node_data.pop('id', None)
#                 extracted_label = node_data.pop('label', None)

#                 # Query for both incoming and outgoing relationships
#                 rel_query = """
#                     g.V().has('node_id', node_id).union(
#                         outE().as('e').otherV().as('v')
#                             .project('direction', 'relationship', 'target')
#                             .by(constant('outgoing'))
#                             .by(select('e').label())
#                             .by(select('v').values('node_id')),
#                         inE().as('e').otherV().as('v')
#                             .project('direction', 'relationship', 'target')
#                             .by(constant('incoming'))
#                             .by(select('e').label())
#                             .by(select('v').values('node_id'))
#                     )
#                 """
#                 rel_bindings = {"node_id": node_id}
#                 rel_results = gremlin_client.submit(rel_query, rel_bindings).all().result()

#                 # Build relationships with direction
#                 relationships = [
#                     ReadRelationshipModel(
#                         direction=r['direction'],
#                         relationship=r['relationship'],
#                         target=r['target']
#                     )
#                     for r in rel_results
#                 ]

#                 node_model = NodeModel(
#                     id=extracted_id,
#                     label=extracted_label,
#                     relationships=relationships,
#                     extra_properties=node_data
#                 )

#             # Prepare successful response
#             data_response = RelationshipReadRepsonse(data=[node_model])
#             success_response = Response(
#                 status_code=200,
#                 detail=f"Success: Read node {node_id}"
#             )
#             return data_response, success_response

#         except httpx.TimeoutException as e:
#             response = Response(status_code=502, detail=f"Timeout Error: Encountered Timeout Error when Connecting to Azure Server - {e}")
#             logger.error(response.detail)
#             return None, response

#         except httpx.HTTPError as e:
#             response = Response(status_code=502, detail=f"Connection Error: Encountered Connection Error when Connecting to Azure Server - {e}")
#             logger.error(response.detail)
#             return None, response

#         except Exception as e:
#             response = Response(status_code=500, detail=f"Common Error: Encountered Common Error when Calling Azure Server - {e}")
#             logger.error(response.detail)
#             return None, response
#     def update_node(self, request: KGNodeUpdateRequest) -> Response:
#         container_name = request.container_name
#         node_id = request.node_id
#         update_data = request.update_data.dict(exclude_unset=True)
        
#         # Extract relationships to handle separately
#         relationships = update_data.pop('relationship', [])
        
#         # Automatically update the timestamp
#         update_data["updated_at"] = datetime.now(timezone.utc).isoformat()

#         try:
#             with self.gb_client(container_name) as gremlin_client:
#                 # Check if the node exists
#                 exist_query = "g.V().has('node_id', node_id).count()"
#                 result = gremlin_client.submit(exist_query, {"node_id": node_id}).all().result()
#                 if result[0] == 0:
#                     return Response(
#                         status_code=404,
#                         detail=f"Node Not Found: Node {node_id} not found in container {container_name}"
#                     )

#                 # Build the update query for properties
#                 query = "g.V().has('node_id', node_id)"
#                 bindings = {"node_id": node_id}
#                 for i, (key, value) in enumerate(update_data.items()):
#                     query += f".property('{key}', prop_{i})"
#                     bindings[f"prop_{i}"] = value

#                 # Execute the property update query
#                 if update_data:
#                     gremlin_client.submit(query, bindings)

#                 # Process relationships to add edges
#                 for rel in relationships:
#                     rel_type = rel.get('relationship')
#                     target_id = rel.get('target')
#                     if not rel_type or not target_id:
#                         continue

#                     # Check if target node exists (parameterized)
#                     target_exist = gremlin_client.submit(
#                         "g.V().has('node_id', target_id).count()",
#                         {"target_id": target_id}
#                     ).all().result()[0]
#                     if target_exist == 0:
#                         return Response(
#                             status_code=404,
#                             detail=f"Target node {target_id} not found for relationship {rel_type}"
#                         )

#                     # Parameterized edge creation query
#                     edge_query = (
#                         "g.V().has('node_id', node_id).as('source')"
#                         ".V().has('node_id', target_id)"
#                         ".addE(rel_type).from('source')"  # Use parameter binding
#                     )
#                     bindings = {
#                         "node_id": node_id,
#                         "target_id": target_id,
#                         "rel_type": rel_type  # Parameterize the relationship type
#                     }

#                     gremlin_client.submit(edge_query, bindings)

#                 return Response(
#                     status_code=200,
#                     detail=f"Success: Updated node {node_id} in container {container_name}"
#                 )
#         except Exception as e:
#             # Handle exceptions as before
#             return Response(status_code=500, detail=f"Update Failed: {str(e)}")
#     def delete_by_node_id(self, request: RelationshipDeleteNodeRequest) -> Response:
#         try:
#             node_id = request.node_id
#             container_name = request.container_name
#             # Gremlin query to delete node based on node_id.
#             query = "g.V().has('node_id', node_id).drop()"
#             bindings = {"node_id": node_id}
            
#             with self.gb_client(container_name) as gremlin_client:
#                 gremlin_client.submit(query, bindings)
            
#             success_message = (
#                 f"Success: Deleted node with node_id '{node_id}' "
#                 f"from container '{container_name}'."
#             )
#             logger.info(success_message)
#             return Response(status_code=200, detail=self.response_format.ok(success_message))
        
#         except httpx.TimeoutException as e:
#             error_message = (
#                 f"Timeout Error: Azure Cosmos DB did not respond in time during deletion "
#                 f"of node with node_id '{node_id}'."
#             )
#             logger.error(error_message + f" Exception: {str(e)}")
#             return Response(status_code=502, detail=self.response_format.error(error_message, str(e)))
        
#         except httpx.HTTPError as e:
#             error_message = (
#                 f"Connection Error: An HTTP error occurred when deleting node with node_id "
#                 f"'{node_id}'."
#             )
#             logger.error(error_message + f" Exception: {str(e)}")
#             return Response(status_code=502, detail=self.response_format.error(error_message, str(e)))
        
#         except Exception as e:
#             error_message = (
#                 f"Delete Failed: Failed to delete node with node_id '{node_id}'."
#             )
#             logger.error(error_message + f" Exception: {str(e)}")
#             return Response(status_code=500, detail=self.response_format.error(error_message, str(e)))


#     def read_all_nodes(self, container_name: str = None) -> tuple[RelationshipReadRepsonse, Response]:
#         """
#         Retrieves all nodes from the given container.
#         If no container_name is provided, uses a default container (adjust as needed).
#         """
#         # Provide a sensible default if none is given
#         if not container_name:
#             container_name = "your_default_container_name"

#         try:
#             # Gremlin query to fetch all nodes' properties
#             query = "g.V().valueMap(true)"
            
#             with self.gb_client(container_name) as gremlin_client:
#                 # Fetch all raw node data
#                 result_nodes = gremlin_client.submit(query).all().result()
                
#                 if not result_nodes:
#                     # Return an empty list if there's no node at all
#                     return RelationshipReadRepsonse(data=[]), Response(
#                         status_code=200,
#                         detail=f"Success: No nodes found in container '{container_name}'."
#                     )
                
#                 node_models = []
#                 # Parse each node into a NodeModel
#                 for raw_node_data in result_nodes:
#                     # Flatten each property returned by valueMap(true)
#                     node_data = {
#                         key: value[0] if isinstance(value, list) and len(value) == 1 else value
#                         for key, value in raw_node_data.items()
#                     }
#                     extracted_id = node_data.pop('id', None)
#                     extracted_label = node_data.pop('label', None)

#                     node_model = NodeModel(
#                         id=extracted_id,
#                         label=extracted_label,
#                         # If you want to retrieve edges/relationships, you'd need a separate query
#                         relationships=[],
#                         extra_properties=node_data
#                     )
#                     node_models.append(node_model)

#             # Wrap results into your existing schemas
#             data_response = RelationshipReadRepsonse(data=node_models)
#             success_response = Response(
#                 status_code=200,
#                 detail=f"Success: Retrieved all nodes from container '{container_name}'."
#             )
#             return data_response, success_response

#         except httpx.TimeoutException as e:
#             error_message = (
#                 f"Timeout Error: Encountered a timeout when fetching all nodes from container '{container_name}'."
#             )
#             logger.error(error_message + f" Exception: {str(e)}")
#             return None, Response(
#                 status_code=502,
#                 detail=self.response_format.error(error_message, str(e))
#             )
#         except httpx.HTTPError as e:
#             error_message = (
#                 f"Connection Error: Encountered an HTTP error when fetching all nodes from container '{container_name}'."
#             )
#             logger.error(error_message + f" Exception: {str(e)}")
#             return None, Response(
#                 status_code=502,
#                 detail=self.response_format.error(error_message, str(e))
#             )
#         except Exception as e:
#             error_message = (
#                 f"Common Error: Encountered an error while fetching nodes from container '{container_name}'."
#             )
#             logger.error(error_message + f" Exception: {str(e)}")
#             return None, Response(
#                 status_code=500,
#                 detail=self.response_format.error(error_message, str(e))
#             )