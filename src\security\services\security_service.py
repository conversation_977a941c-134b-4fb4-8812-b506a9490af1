import inspect
import httpx

from ...settings import SETTINGS

from ..schemas.format import (
    ResponseFormatter,
    Response
)

from ..schemas.security import (
    UserKeyRetrievalRequest,
    UserKeyRetrievalResponse
)

from ..services import request_retrieve_userkey

from ...logger.log_handler import get_logger

logger = get_logger(__name__)

class SecurityServiceManager:
    response_format = ResponseFormatter(suffix = f"{inspect.stack()[0][3]}")

    def __init__(self, api_call: bool):
        self.api_call = api_call

    """
        Request Operation
    """
    def request_userkey(self, request: UserKeyRetrievalRequest) -> tuple[UserKeyRetrievalResponse, Response]:
        response_data = UserKeyRetrievalResponse(**request.__dict__)
        data_request = request
        
        try:
            # API Call
            if self.api_call == True:
                api_url = f"http://{SETTINGS.SCRT.HOST}:{SETTINGS.SCRT.PORT}/{SETTINGS.SCRT.REQUEST_USERKEY_API}"
                payload = data_request.json()
                response_data, response = self.api_call_static(data=payload, service="SecurityHub", api_url=api_url, method="post", timeout=SETTINGS.BASE.APP_TIMEOUT)
                
                if response.status_code < SETTINGS.STAT.SUCC_CODE_END:
                    response_data = response_data.json()
                    response = Response(status_code=200, detail=self.response_format.ok(f"Success : <{SETTINGS.BASE.APP_NAME}> Retrieved UserKey via API"))

            # Function Call
            else:   
                if SETTINGS.BASE.APP_FUNC == True:
                    try:
                        response_data = request_retrieve_userkey(request=data_request, api_call=False)
                        response = Response(status_code=200, detail=self.response_format.ok(f"Success : <{SETTINGS.BASE.APP_NAME}> Retrieved UserKey via Function Call"))
                    except Exception as e:
                        response = Response(status_code=500, detail=self.response_format.error(f"Function Retrieval Error : <{SETTINGS.BASE.APP_NAME}> Failed to Retrieve UserKey via Function Call"))
                
                else:
                    response = Response(status_code=500, detail=self.response_format.error(f"Configuration Error : <{SETTINGS.BASE.APP_NAME}> Used Function Call to Retrieve UserKey but <APP_FUNC> is False"))
                    logger.error(response.detail)

        # Handle common exceptions that might occur
        except (BaseException, Exception) as e:
            response = Response(status_code=500, detail=self.response_format.error(f"Common Error : <{SETTINGS.BASE.APP_NAME}> Retrieving UserKey", str(e)))
            logger.error(response.detail)

        # Handle any other exceptions that might occur
        except:
            response = Response(status_code=500, detail=self.response_format.error(f"Unexpected Error : <{SETTINGS.BASE.APP_NAME}> Retrieving UserKey"))
            logger.error(response.detail)

        return response_data, response


    
    def api_call_static(self, data, service: str, api_url: str, method: str, timeout: float | None) -> tuple[httpx.Response | None, Response]:
        response_data = None

        try:
            if method.lower() == "post":

                if isinstance(data, str):
                    if timeout:
                        resp = httpx.post(api_url, data=data, timeout=timeout)
                    else:
                        resp = httpx.post(api_url, data=data)

                else:
                    if timeout:
                        resp = httpx.post(api_url, json=data, timeout=timeout)
                    else:
                        resp = httpx.post(api_url, json=data)

            else:
                response = Response(status_code=500, detail=self.response_format.error(f"API Method Error : Unknown API Method <{method}>"))
                logger.error(response.detail)
                return response_data, response

            if not resp.status_code == httpx.codes.ok:
                response = Response(status_code=resp.status_code, detail=self.response_format.error(f"Response Error : Retrieving Data from <{service}> API Server", resp["detail"]))
                logger.error(response.detail)
            
            else:
                response = Response(status_code=resp.status_code, detail=self.response_format.ok(f"Success : Retrieved Data from <{service}> API Server"))
                response_data = resp

        except httpx.TimeoutException as e:
            response = Response(status_code=502, detail=self.response_format.error(f"Timeout Error : Retrieving Data <{service}> API Server", str(e)))
            logger.error(response.detail)

        except httpx.HTTPError as e:
            response = Response(status_code=502, detail=self.response_format.error(f"Connection Error : Retrieving Data <{service}> API Server", str(e)))
            logger.error(response.detail)

        # Handle common exceptions that might occur
        except (BaseException, Exception) as e:
            response = Response(status_code=500, detail=self.response_format.error(f"Common Error : Connecting to <{service}> API Server", str(e)))
            logger.error(response.detail)

        # Handle any other exceptions that might occur
        except:
            response = Response(status_code=500, detail=self.response_format.error(f"Unexpected Error : Connecting to <{service}> API Server"))
            logger.error(response.detail)

        return response_data, response